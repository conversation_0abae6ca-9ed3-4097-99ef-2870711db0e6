using System.Text;
using System.Text.Json;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using OpenAI.Chat;

namespace Aihub.Server.Llm;

public class LlmClient : ITransientService {

    private readonly ChatClient _chatClient;

    public LlmClient(ChatClient chatClient){
        _chatClient = chatClient;
    }

    private AssistantChatMessage FromAssistantMessage(AssistantMessage assistantMessage){
        var chatMessage = new AssistantChatMessage(assistantMessage.Content ?? string.Empty);
        if(assistantMessage.Extra is not null && assistantMessage.Extra.ToolCalls.Count > 0){
            foreach(var toolCall in assistantMessage.Extra.ToolCalls){
                chatMessage.ToolCalls.Add(ChatToolCall.CreateFunctionToolCall(
                    id: toolCall.CallId,
                    functionName: toolCall.CallName,
                    functionArguments: new BinaryData(toolCall.CallParameters)
                ));
            }
        }
        return chatMessage;
    }

    private ToolChatMessage FromToolMessage(ToolMessage toolMessage)
    {
        if (toolMessage.Extra is null) throw new Exception("Tool message extra is null");

        return new ToolChatMessage(toolMessage.Extra.CallId, JsonSerializer.Serialize(new
        {
            isSuccess = toolMessage.Extra.IsSuccess,
            result = toolMessage.Content
        }, JsonSerializerOptions.Default));

    }

    private (IEnumerable<ChatMessage>, ChatCompletionOptions) FromMessages(IEnumerable<Message> messages, IEnumerable<ToolFunction> tools){
        var chatMessages = new List<ChatMessage>();
        var options = new ChatCompletionOptions();

        foreach(var message in messages){
            switch(message){
                case SystemMessage systemMessage:
                    chatMessages.Add(new SystemChatMessage(systemMessage.Content));
                    break;
                case UserMessage userMessage:
                    chatMessages.Add(new UserChatMessage(userMessage.Content!));
                    break;
                case AssistantMessage assistantMessage:
                    chatMessages.Add(FromAssistantMessage(assistantMessage));
                    break;
                case ToolMessage toolMessage:
                    chatMessages.AddRange(FromToolMessage(toolMessage));
                    break;
                default:
                    throw new InvalidOperationException("Invalid message type");
            }
        }


        if(tools.Any()){
            foreach(var toolCall in tools){
                options.Tools.Add(ChatTool.CreateFunctionTool(
                    functionName: toolCall.FunctionName,
                    functionDescription: toolCall.FunctionDescription,
                    functionParameters: new BinaryData(JsonSerializer.Serialize(new {
                        type = "object",
                        properties = toolCall.FunctionParameters.Parameters.ToDictionary(p => p.ParameterName, p => new {
                            type = p.ParameterType switch{
                                ToolFunctionParameterType.String => "string",
                                ToolFunctionParameterType.Int => "integer",
                                ToolFunctionParameterType.Float => "number",
                                ToolFunctionParameterType.Boolean => "boolean",
                                _ => throw new InvalidOperationException("Invalid parameter type")
                            },
                            description = p.ParameterDescription
                        }),
                        required = toolCall.FunctionParameters.Required,
                        additionalProperties = false
                    }), StandardizedJsonOption.DefaultOptions)
                ));
            }
        }

        options.Temperature = 0f;

        return (chatMessages, options);
    }

    public async Task<ChatCompletionResult> CompleteChatStreamingAsync(
        IEnumerable<Message> messages, 
        IEnumerable<ToolFunction> tools,
        Func<string, string, Task> onChunk = null!,
        CancellationToken cancellationToken = default){

        var (chatMessages, options) = FromMessages(messages, tools);

        var contentBuilder = new StringBuilder();
        string? lastToolcallId = null;
        var toolCallsInProgress = new Dictionary<string, ToolCallInStreaming>();
        var response = _chatClient.CompleteChatStreamingAsync(chatMessages, options, cancellationToken: cancellationToken);

        StreamingChatCompletionUpdate? lastUpdate = null;

        await foreach(var update in response){

            lastUpdate = update;

            if(update.ContentUpdate.Count > 0){
                var chunk = update.ContentUpdate[0].Text;
                contentBuilder.Append(chunk);
                if(onChunk is not null){
                    await onChunk(chunk, contentBuilder.ToString());
                }
            }


            // 处理工具调用更新
            if (update.ToolCallUpdates.Count > 0)
            {
                foreach (var toolCallUpdate in update.ToolCallUpdates)
                {
                    var toolCallId = toolCallUpdate.ToolCallId;

                    // 如果是新的工具调用，创建一个新的 StreamToolCall
                    if (toolCallId is not null && !toolCallsInProgress.ContainsKey(toolCallId))
                    {
                        lastToolcallId = toolCallId;
                        toolCallsInProgress[toolCallId] = new ToolCallInStreaming
                        {
                            Id = toolCallId,
                            FunctionName = toolCallUpdate.FunctionName ?? string.Empty,
                            FunctionArguments = string.Empty
                        };
                    }

                    // 累积函数参数
                    var argsUpdate = toolCallUpdate.FunctionArgumentsUpdate;
                    if (lastToolcallId is not null && argsUpdate != null && argsUpdate.ToArray().Length > 0)
                    {
                        var existingArgs = toolCallsInProgress[lastToolcallId].FunctionArguments;
                        var newArgs = existingArgs + argsUpdate.ToString();

                        // 直接更新 StreamToolCall 对象的属性
                        toolCallsInProgress[lastToolcallId].FunctionArguments = newArgs;

                        // 更新函数名称（如果有的话）
                        if (!string.IsNullOrEmpty(toolCallUpdate.FunctionName))
                        {
                            toolCallsInProgress[lastToolcallId].FunctionName = toolCallUpdate.FunctionName;
                        }
                    }
                }
            }
        }

        // 发送最终消息
        List<ToolCall> toolCalls = new List<ToolCall>();
        if(toolCallsInProgress.Count > 0){
            toolCalls = toolCallsInProgress.Values.Select(t => new ToolCall{
                Id = t.Id,
                FunctionName = t.FunctionName,
                FunctionArguments = t.FunctionArguments
            }).ToList();
        }

        return new ChatCompletionResult{
            Content = contentBuilder.ToString(),
            ToolCalls = toolCalls,
            InputTokenCount = lastUpdate?.Usage?.InputTokenCount ?? 0,
            OutputTokenCount = lastUpdate?.Usage?.OutputTokenCount ?? 0
        };
    }

    public async Task<ChatCompletionResult> CompleteChatAsync(
        IEnumerable<Message> messages, 
        IEnumerable<ToolFunction> tools,
        CancellationToken cancellationToken = default){

        var (chatMessages, options) = FromMessages(messages, tools);

        var response = await _chatClient.CompleteChatAsync(chatMessages, options, cancellationToken: cancellationToken);
        var toolCalls = response.Value.ToolCalls?.Select(t => new ToolCall{
            Id = t.Id,
            FunctionName = t.FunctionName,
            FunctionArguments = t.FunctionArguments.ToString()
        }).ToList() ?? new List<ToolCall>();

        return new ChatCompletionResult{
            Content = response.Value.Content.FirstOrDefault()?.Text ?? string.Empty,
            ToolCalls = toolCalls,
            InputTokenCount = response.Value.Usage?.InputTokenCount ?? 0,
            OutputTokenCount = response.Value.Usage?.OutputTokenCount ?? 0
        };

    }

}

public class ChatCompletionResult
{
    public required string Content { get; set; }
    public List<ToolCall> ToolCalls { get; set; } = new List<ToolCall>();
    public int InputTokenCount { get; set; }
    public int OutputTokenCount { get; set; }
}
