namespace Aihub.Server.Models;

public class AssistantMessageExtraToolCall {

    /// <summary>
    /// 一个随机的Guid，用于标识一个工具调用方便和前端对应起来的同时不泄露 CallId 和 InternalId
    /// </summary>
    public required Guid Id {get;set;}
    /// <summary>
    /// 外部调用id(基本上就是 LLM 的 CallToolId)
    /// </summary>
    public required string CallId {get;set;}

    /// <summary>
    /// 调用类型
    /// </summary>
    public required AssistantMessageExtraToolCallType Type {get;set;}
    
    /// <summary>
    /// 内部调用id 参考 ToolDescription.ToolId 或 AgentOption.Id
    /// </summary>
    public required string InternalId {get;set;}

    /// <summary>
    /// SubAgent 调用的会话ID，如果为空，则表示是 Tool 调用
    /// </summary>
    public Guid? SessionId {get;set;}

    public required string CallName {get;set;}
    public required string CallParameters {get;set;}
    public required string Name {get;set;}
    public required string Description {get;set;}
    public required bool IsUserAware {get;set;}
    public required bool NeedConfirm {get;set;}
    public required bool IsConfirm {get;set;}

    public required AssistantMessageExtraToolCallSeverity Severity {get;set;}

    /// <summary>
    /// 工具调用结果消息ID，如果为空，则表示工具调用结果还未更新
    /// </summary>
    public Guid? ToolResultMessageId {get;set;}
}

public enum AssistantMessageExtraToolCallType {
    ToolCall,
    SubAgentCall
}

public enum AssistantMessageExtraToolCallSeverity {
    Low, Medium, High
}