using System.Reflection;
using Aihub.Server.Agent.Tool;

namespace Aihub.Server.Models;


public class ToolDescription
{
    public required string ToolId { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }

    public string? NameForUser { get; set; }
    public string? DescriptionForUser { get; set; }

    public required string[] Parameters { get; set; }

    public Dictionary<string, string> ParameterDescriptions { get; set; } = new Dictionary<string, string>();

    public Dictionary<string, ToolDescriptionParameterType> ParameterTypes { get; set; } = new Dictionary<string, ToolDescriptionParameterType>();

    public string[] RequiredParameters { get; set; } = new string[0];

    public required Type ToolType { get; set; }
    public required MethodInfo ToolMethod { get; set; }

    public required bool UserAware { get; set; }
    public required bool NeedConfirm { get; set; }

    public required ToolSeverity Severity { get; set; }

}
