using Aihub.Server.Agent;
using Org.BouncyCastle.Asn1.Ocsp;

namespace Aihub.Server.Models;

public class SubAgentDescription {
    public required string Key { get; set; }
    public required string Name { get; set; }
    public required string Description { get; set; }
    public required string DisplayName { get; set; }

    public required string ParameterName { get; set; }

    public required string ParameterDescription { get; set; }

    public required Type ToolType { get; set; }

    public required bool UserAware { get; set; }

    public required SubAgentSeverity Severity { get; set; }
}
