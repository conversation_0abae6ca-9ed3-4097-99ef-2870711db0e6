using Aihub.Server.Models;
using Aihub.Server.Models.Dto;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Converters;

public class MessageToDtoConverter
{
    public static MessageDto ConvertToDto(Message message)
    {

        switch (message)
        {
            case UserMessage userMessage:
                return new UserMessageDto
                {
                    Id = message.Id,
                    Content = userMessage.Content!,
                    Timestamp = message.CreatedAt
                };
            case AssistantMessage assistantMessage:
                return new AssistantMessageDto
                {
                    Id = message.Id,
                    Content = assistantMessage.Content!,
                    Timestamp = message.CreatedAt,
                    ToolCalls = assistantMessage.Extra?.ToolCalls.Select(t => new AssistantMessageToolCallVo
                    {
                        Id = t.Id,
                        ToolName = t.Name,
                        ToolDescription = t.Description,
                        NeedConfirm = t.NeedConfirm,
                        IsConfirm = t.IsConfirm,
                        Severity = t.Severity switch {
                            AssistantMessageExtraToolCallSeverity.Low => AssistantMessageToolCallSeverity.Low,
                            AssistantMessageExtraToolCallSeverity.Medium => AssistantMessageToolCallSeverity.Medium,
                            AssistantMessageExtraToolCallSeverity.High => AssistantMessageToolCallSeverity.High,
                            _ => throw new InvalidOperationException("Invalid severity")
                        }
                    }).ToList() ?? new List<AssistantMessageToolCallVo>()
                };
            case SubAgentAssistantMessage subAgentAssistantMessage:
                return new AssistantMessageDto
                {
                    Id = message.Id,
                    Content = subAgentAssistantMessage.Content!,
                    Timestamp = message.CreatedAt,
                    ToolCalls = subAgentAssistantMessage.Extra?.ToolCalls.Select(t => new AssistantMessageToolCallVo
                    {
                        Id = t.Id,
                        ToolName = t.Name,
                        ToolDescription = t.Description,
                        NeedConfirm = t.NeedConfirm,
                        IsConfirm = t.IsConfirm,
                        Severity = t.Severity switch {
                            AssistantMessageExtraToolCallSeverity.Low => AssistantMessageToolCallSeverity.Low,
                            AssistantMessageExtraToolCallSeverity.Medium => AssistantMessageToolCallSeverity.Medium,
                            AssistantMessageExtraToolCallSeverity.High => AssistantMessageToolCallSeverity.High,
                            _ => throw new InvalidOperationException("Invalid severity")
                        }
                    }).ToList() ?? new List<AssistantMessageToolCallVo>()
                };
            default:
                throw new InvalidOperationException("Invalid message type");
        }
    }
}
