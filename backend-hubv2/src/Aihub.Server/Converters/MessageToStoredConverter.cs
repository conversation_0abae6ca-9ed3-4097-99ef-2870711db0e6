using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;

namespace Aihub.Server.Converters;

public class MessageToStoredConverter {
    public static StoredMessage ConvertToStored(Message message){
        
        var storedMessage = new StoredMessage
        {
            Id = message.Id,
            SessionId = message.Session!.Value,
            Category = StoredMessageCategory.User,
            Content = message.Content!,
            CreatedAt = message.CreatedAt,
        };

        switch(message){
            case SystemMessage systemMessage:
                storedMessage.Category = StoredMessageCategory.System;
                break;
            case AssistantMessage assistantMessage:
                storedMessage.Category = StoredMessageCategory.Assistant;
                break;
            case UserMessage userMessage:
                storedMessage.Category = StoredMessageCategory.User;
                break;
            default:
                throw new InvalidOperationException("Invalid message type");
        }

        return storedMessage;
    }

}
