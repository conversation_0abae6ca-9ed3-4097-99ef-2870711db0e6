using Aihub.Server.Exceptions;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Contexts;

public class SubAgentAndToolCallTimesCounter : IScopedService
{
    private int _times = 0;
    private readonly AihubOption _aiHubOption;
    private SubAgentAndToolCallTimesCounter? _associatedCounter;

    public SubAgentAndToolCallTimesCounter(IOptions<AihubOption> aiHubOption)
    {
        _aiHubOption = aiHubOption.Value;
        _associatedCounter = null;
    }

    // 关联另外一个计数器
    public void AssociateWith(SubAgentAndToolCallTimesCounter otherCounter)
    {
        _associatedCounter = otherCounter;
    }

    public void Increment()
    {
        if (_associatedCounter is not null)
        {
            _associatedCounter.Increment();
        }
        else
        {
            _times++;
            if (_times > _aiHubOption.MaxToolCallLoopTimes)
            {
                throw new MaxToolCallLoopTimesReachedException();
            }
        }
    }
}
