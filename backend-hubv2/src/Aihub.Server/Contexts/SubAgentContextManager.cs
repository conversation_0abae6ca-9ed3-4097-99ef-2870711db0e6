using Aihub.Server.Scrutors;

namespace Aihub.Server.Contexts;

public class SubAgentContextManager :
    ICurrentSubAgentIdAccessor,
    ICurrentSubAgentSessionIdAccessor,
    IScopedService
{
    private Guid? _subAgentId;
    private Guid? _subAgentSessionId;

    public Guid? GetSubAgentSessionId()
    {
        return _subAgentSessionId;
    }

    public Guid? GetSubAgentId()
    {
        return _subAgentId;
    }

    public void InitializeWith(Guid subAgentSessionId, Guid subAgentId)
    {
        _subAgentSessionId = subAgentSessionId;
        _subAgentId = subAgentId;
    }
}
