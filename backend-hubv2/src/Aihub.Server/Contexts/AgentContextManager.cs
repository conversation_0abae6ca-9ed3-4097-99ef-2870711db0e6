using Aihub.Server.Contexts;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Contexts;

public class AgentContextManager :
    ICurrentAgentIdAccessor,
    ICurrentSessionIdAccessor,
    IScopedService
{

    private Guid? _sessionId;
    private Guid? _agentId;

    public Guid? GetSessionId() => _sessionId;
    public Guid? GetAgentId() => _agentId;

    public void InitializeWith(Guid sessionId, Guid agentId)
    {
        _sessionId = sessionId;
        _agentId = agentId;
    }
}
