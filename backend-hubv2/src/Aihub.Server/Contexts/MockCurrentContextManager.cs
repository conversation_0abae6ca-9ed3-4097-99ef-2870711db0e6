using System.Text.Json;
using Aihub.Server.EfCore;
using Aihub.Server.Entites;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Contexts;
public class MockCurrentContextManager
{

    private Guid? _tenantId;
    private Guid? _applicationId;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;

    public MockCurrentContextManager(
        IDbContextFactory<AihubDbContext> dbContextFactory
    )
    {
        _dbContextFactory = dbContextFactory;
        InitializeAsync().GetAwaiter().GetResult();
    }

    public async Task InitializeAsync()
    {

        var dbContext = _dbContextFactory.CreateDbContext();

        _tenantId = (await dbContext.Tenants.FirstOrDefaultAsync())?.Id;
        _applicationId = (await dbContext.Applications.FirstOrDefaultAsync())?.Id;

        if (_tenantId is null)
        {
            var tenant = new Tenant
            {
                Id = Guid.CreateVersion7(),
                Key = "MockTenantKey",
                Name = "MockTenant",
                Description = "MockTenantDescription",
                CreatedAt = DateTime.UtcNow
            };
            await dbContext.Tenants.AddAsync(tenant);
            await dbContext.SaveChangesAsync();
            _tenantId = tenant.Id;
        }

        if (_applicationId is null)
        {
            var application = new Application
            {
                Id = Guid.CreateVersion7(),
                Key = "MockApplication",
                DisplayName = "MockApplication",
                Description = "MockApplicationDescription",
                TenantId = _tenantId.Value,
                CreatedAt = DateTime.UtcNow
            };
            await dbContext.Applications.AddAsync(application);

            var agentOption = new AgentOption
            {
                Id = Guid.CreateVersion7(),
                Key = "primary",
                Name = "Primary",
                Description = "Primary Agent Option",
                CreatedAt = DateTime.UtcNow
            };
            await dbContext.AgentOptions.AddAsync(agentOption);
            var agentOption2 = new AgentOption
            {
                Id = Guid.CreateVersion7(),
                Key = "database-agent",
                Name = "Database",
                Description = "Database Agent Option",
                Extra = JsonSerializer.Serialize(new AgentOptionDatabaseAgentExtra
                {
                    DbType = AgentOptionDatabaseAgentExtraDbType.MySql,
                    DbConnectionString = "Server=localhost;Database=tsz-integrated-platform;User=root;Password=****;",
                    CachedSchema = 
                        """
                        # Database schema

                        ```sql
                        CREATE TABLE `contract` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `contract_no` varchar(100) NOT NULL COMMENT '合同编号',
                        `contract_name` varchar(200) NOT NULL COMMENT '合同名称',
                        `contract_type` varchar(50) DEFAULT NULL COMMENT '合同类型(关联字典编码：contract_type)',
                        `contract_category` varchar(50) DEFAULT NULL COMMENT '合同分类(关联字典编码：contract_category)',
                        `contract_amount` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
                        `opportunity_id` bigint DEFAULT NULL COMMENT '关联商机ID(关联opportunity表)',
                        `signing_date` date DEFAULT NULL COMMENT '签署日期',
                        `effective_date` date DEFAULT NULL COMMENT '生效日期',
                        `expiry_date` date DEFAULT NULL COMMENT '到期日期',
                        `contract_status` varchar(20) DEFAULT 'draft' COMMENT '合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)',
                        `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式(关联字典编码：payment_method)',
                        `signing_location` varchar(200) DEFAULT NULL COMMENT '签署地点',
                        `responsible_user_id` bigint DEFAULT NULL COMMENT '负责人ID(关联sys_user表)',
                        `dept_id` bigint DEFAULT NULL COMMENT '所属部门ID(关联sys_dept表)',
                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='合同主表';

                        CREATE TABLE `contract_attachment` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `contract_id` bigint NOT NULL COMMENT '合同ID(关联contract表)',
                        `attachment_id` bigint NOT NULL COMMENT '附件ID(关联tsz_attachment表)',
                        `sort` int DEFAULT '0' COMMENT '排序',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE,
                        KEY `idx_contract_id` (`contract_id`) USING BTREE,
                        KEY `idx_attachment_id` (`attachment_id`) USING BTREE
                        ) COMMENT='合同文件关联表';

                        CREATE TABLE `contract_partner_relation` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `contract_id` bigint NOT NULL COMMENT '合同ID',
                        `partner_id` bigint NOT NULL COMMENT '伙伴ID',
                        `partner_role` varchar(20) NOT NULL COMMENT '伙伴角色(关联字典编码：partner_role)',
                        `partner_role_desc` varchar(20) DEFAULT NULL COMMENT '角色描述(关联字典编码：partner_role_desc)',
                        `signing_person` varchar(100) DEFAULT NULL COMMENT '签署人',
                        `signing_person_title` varchar(100) DEFAULT NULL COMMENT '签署人职务',
                        `signing_date` date DEFAULT NULL COMMENT '签署日期',
                        `sort` int DEFAULT '0' COMMENT '排序',
                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='合同伙伴关联表';

                        CREATE TABLE `contract_payment` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `contract_id` bigint NOT NULL COMMENT '合同ID',
                        `payment_no` varchar(100) DEFAULT NULL COMMENT '付款单号',
                        `payment_type` varchar(20) NOT NULL COMMENT '付款类型(关联字典编码：payment_type)',
                        `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式(关联字典编码：payment_method)',
                        `payer_partner_id` bigint DEFAULT NULL COMMENT '付款方ID(关联partner表)',
                        `payee_partner_id` bigint DEFAULT NULL COMMENT '收款方ID(关联partner表)',
                        `planned_amount` decimal(15,2) DEFAULT NULL COMMENT '计划金额',
                        `actual_amount` decimal(15,2) DEFAULT NULL COMMENT '实际金额',
                        `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
                        `planned_date` date DEFAULT NULL COMMENT '计划付款日期',
                        `actual_date` date DEFAULT NULL COMMENT '实际付款日期',
                        `payment_status` varchar(20) DEFAULT 'pending' COMMENT '付款状态(pending-待付款 paid-已付款 partial-部分付款 overdue-已逾期 cancelled-已取消)',
                        `bank_name` varchar(200) DEFAULT NULL COMMENT '付款银行',
                        `bank_account` varchar(50) DEFAULT NULL COMMENT '付款账号',
                        `transaction_no` varchar(100) DEFAULT NULL COMMENT '交易流水号',
                        `voucher_attachment_id` varchar(500) DEFAULT NULL COMMENT '付款凭证文件Id',
                        `invoice_status` varchar(20) DEFAULT 'not_issued' COMMENT '发票状态(not_issued-未开票 issued-已开票 received-已收票)',
                        `invoice_no` varchar(100) DEFAULT NULL COMMENT '发票号码',
                        `invoice_amount` decimal(15,2) DEFAULT NULL COMMENT '发票金额',
                        `invoice_date` date DEFAULT NULL COMMENT '开票日期',
                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='合同付款记录表';

                        CREATE TABLE `opportunity` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `opportunity_code` varchar(50) NOT NULL COMMENT '商机编码(自动生成)',
                        `opportunity_name` varchar(200) NOT NULL COMMENT '商机名称',
                        `opportunity_type` varchar(20) NOT NULL COMMENT '商机类型(关联字典编码：opportunity_type)',
                        `opportunity_source` varchar(20) DEFAULT NULL COMMENT '商机来源(关联字典编码：opportunity_source)',
                        `partner_id` bigint DEFAULT NULL COMMENT '关联客户ID(关联partner表)',
                        `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                        `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                        `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
                        `opportunity_stage` varchar(20) DEFAULT 'initial' COMMENT '商机阶段(initial-初步接触 interested-有意向 proposal-方案阶段 negotiation-谈判阶段 closed_won-成交 closed_lost-失败)',
                        `win_probability` int DEFAULT '0' COMMENT '成单概率(%)',
                        `estimated_amount` decimal(15,2) DEFAULT NULL COMMENT '预估金额',
                        `estimated_close_date` date DEFAULT NULL COMMENT '预计成交日期',
                        `actual_close_date` date DEFAULT NULL COMMENT '实际成交日期',
                        `opportunity_status` varchar(20) DEFAULT 'active' COMMENT '商机状态(active-进行中 won-已成交 lost-已失败 cancelled-已取消 archived-已归档)',
                        `lost_reason` varchar(20) DEFAULT NULL COMMENT '失败原因(关联字典编码：lost_reason)',
                        `priority` varchar(10) DEFAULT 'medium' COMMENT '优先级(high-高 medium-中 low-低)',
                        `product_interest` varchar(500) DEFAULT NULL COMMENT '感兴趣的产品/服务',
                        `requirements` text COMMENT '客户需求描述',
                        `competition_info` varchar(500) DEFAULT NULL COMMENT '竞争对手信息',
                        `next_action` varchar(500) DEFAULT NULL COMMENT '下一步行动计划',
                        `next_follow_date` date DEFAULT NULL COMMENT '下次跟进日期',
                        `responsible_user_id` bigint DEFAULT NULL COMMENT '负责人ID(关联sys_user表)',
                        `dept_id` bigint DEFAULT NULL COMMENT '所属部门ID(关联sys_dept表)',
                        `tags` varchar(200) DEFAULT NULL COMMENT '标签(多个标签用逗号分隔)',
                        `archive_reason` varchar(500) DEFAULT NULL COMMENT '归档原因',
                        `remark` text COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='商机线索表';

                        CREATE TABLE `opportunity_follow` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `opportunity_id` bigint NOT NULL COMMENT '商机ID',
                        `follow_type` varchar(20) NOT NULL COMMENT '跟进方式(关联字典编码：follow_type)',
                        `follow_date` datetime NOT NULL COMMENT '跟进时间',
                        `follow_duration` int DEFAULT NULL COMMENT '跟进时长(分钟)',
                        `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                        `follow_content` text NOT NULL COMMENT '跟进内容',
                        `follow_result` varchar(20) DEFAULT NULL COMMENT '跟进结果(关联字典编码：follow_result)',
                        `next_action` varchar(500) DEFAULT NULL COMMENT '下一步行动计划',
                        `next_follow_date` date DEFAULT NULL COMMENT '下次跟进日期',
                        `attachment_id` varchar(500) DEFAULT NULL COMMENT '附件ID',
                        `follow_user_id` bigint DEFAULT NULL COMMENT '跟进人ID(关联sys_user表)',
                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='商机跟进记录表';

                        CREATE TABLE `partner` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `partner_name` varchar(200) NOT NULL COMMENT '伙伴名称',
                        `partner_code` varchar(50) DEFAULT NULL COMMENT '伙伴编码',
                        `is_our_company` tinyint DEFAULT '0' COMMENT '是否我司或旗下企业(1-是 0-否)',
                        `partner_type` varchar(20) NOT NULL COMMENT '伙伴类型(关联字典编码：partner_type)',
                        `legal_representative` varchar(100) DEFAULT NULL COMMENT '法定代表人',
                        `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
                        `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                        `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
                        `address` varchar(500) DEFAULT NULL COMMENT '地址',
                        `certificate_type` varchar(20) DEFAULT NULL COMMENT '证件类型(关联字典编码：certificate_type)',
                        `certificate_number` varchar(50) DEFAULT NULL COMMENT '证件号码',
                        `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
                        `bank_name` varchar(200) DEFAULT NULL COMMENT '开户银行',
                        `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
                        `status` varchar(10) DEFAULT 'active' COMMENT '状态(active-正常 inactive-禁用)',
                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='业务伙伴表';

                        CREATE TABLE `sys_dept` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `name` varchar(100) NOT NULL COMMENT '部门名称',
                        `code` varchar(100) NOT NULL COMMENT '部门编号',
                        `parent_id` bigint DEFAULT '0' COMMENT '父节点id',
                        `tree_path` varchar(255) NOT NULL COMMENT '父节点id路径',
                        `sort` smallint DEFAULT '0' COMMENT '显示顺序',
                        `status` tinyint DEFAULT '1' COMMENT '状态(1-正常 0-禁用)',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '逻辑删除标识(1-已删除 0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE,
                        UNIQUE KEY `uk_code` (`code`) USING BTREE COMMENT '部门编号唯一索引'
                        ) COMMENT='部门表';

                        CREATE TABLE `sys_dict` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ',
                        `dict_code` varchar(50) DEFAULT NULL COMMENT '类型编码',
                        `name` varchar(50) DEFAULT NULL COMMENT '类型名称',
                        `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:正常;1:禁用)',
                        `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `is_deleted` tinyint DEFAULT '0' COMMENT '是否删除(1-删除，0-未删除)',
                        PRIMARY KEY (`id`) USING BTREE,
                        KEY `idx_dict_code` (`dict_code`)
                        ) COMMENT='字典表';

                        CREATE TABLE `sys_dict_item` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `dict_code` varchar(50) DEFAULT NULL COMMENT '关联字典编码，与sys_dict表中的dict_code对应',
                        `value` varchar(50) DEFAULT NULL COMMENT '字典项值',
                        `label` varchar(100) DEFAULT NULL COMMENT '字典项标签',
                        `tag_type` varchar(50) DEFAULT NULL COMMENT '标签类型，用于前端样式展示（如success、warning等）',
                        `status` tinyint DEFAULT '0' COMMENT '状态（1-正常，0-禁用）',
                        `sort` int DEFAULT '0' COMMENT '排序',
                        `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='字典项表';

                        CREATE TABLE `sys_menu` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                        `parent_id` bigint NOT NULL COMMENT '父菜单ID',
                        `tree_path` varchar(255) DEFAULT NULL COMMENT '父节点ID路径',
                        `name` varchar(64) NOT NULL COMMENT '菜单名称',
                        `type` tinyint NOT NULL COMMENT '菜单类型（1-菜单 2-目录 3-外链 4-按钮）',
                        `route_name` varchar(255) DEFAULT NULL COMMENT '路由名称（Vue Router 中用于命名路由）',
                        `route_path` varchar(128) DEFAULT NULL COMMENT '路由路径（Vue Router 中定义的 URL 路径）',
                        `component` varchar(128) DEFAULT NULL COMMENT '组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）',
                        `perm` varchar(128) DEFAULT NULL COMMENT '【按钮】权限标识',
                        `always_show` tinyint DEFAULT '0' COMMENT '【目录】只有一个子路由是否始终显示（1-是 0-否）',
                        `keep_alive` tinyint DEFAULT '0' COMMENT '【菜单】是否开启页面缓存（1-是 0-否）',
                        `visible` tinyint(1) DEFAULT '1' COMMENT '显示状态（1-显示 0-隐藏）',
                        `sort` int DEFAULT '0' COMMENT '排序',
                        `icon` varchar(64) DEFAULT NULL COMMENT '菜单图标',
                        `redirect` varchar(128) DEFAULT NULL COMMENT '跳转路径',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `params` varchar(255) DEFAULT NULL COMMENT '路由参数',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='菜单管理';

                        CREATE TABLE `sys_role` (
                        `id` bigint NOT NULL AUTO_INCREMENT,
                        `name` varchar(64) NOT NULL COMMENT '角色名称',
                        `code` varchar(32) NOT NULL COMMENT '角色编码',
                        `sort` int DEFAULT NULL COMMENT '显示顺序',
                        `status` tinyint(1) DEFAULT '1' COMMENT '角色状态(1-正常 0-停用)',
                        `data_scope` tinyint DEFAULT NULL COMMENT '数据权限(1-所有数据 2-部门及子部门数据 3-本部门数据 4-本人数据)',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人 ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识(0-未删除 1-已删除)',
                        PRIMARY KEY (`id`) USING BTREE,
                        UNIQUE KEY `uk_name` (`name`) USING BTREE COMMENT '角色名称唯一索引',
                        UNIQUE KEY `uk_code` (`code`) USING BTREE COMMENT '角色编码唯一索引'
                        ) COMMENT='角色表';

                        CREATE TABLE `sys_role_menu` (
                        `role_id` bigint NOT NULL COMMENT '角色ID',
                        `menu_id` bigint NOT NULL COMMENT '菜单ID',
                        UNIQUE KEY `uk_roleid_menuid` (`role_id`,`menu_id`) USING BTREE COMMENT '角色菜单唯一索引'
                        ) COMMENT='角色和菜单关联表';

                        CREATE TABLE `sys_user` (
                        `id` bigint NOT NULL AUTO_INCREMENT,
                        `username` varchar(64) DEFAULT NULL COMMENT '用户名',
                        `nickname` varchar(64) DEFAULT NULL COMMENT '昵称',
                        `gender` tinyint(1) DEFAULT '1' COMMENT '性别((1-男 2-女 0-保密)',
                        `password` varchar(100) DEFAULT NULL COMMENT '密码',
                        `dept_id` int DEFAULT NULL COMMENT '部门ID',
                        `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
                        `mobile` varchar(20) DEFAULT NULL COMMENT '联系方式',
                        `status` tinyint(1) DEFAULT '1' COMMENT '状态(1-正常 0-禁用)',
                        `email` varchar(128) DEFAULT NULL COMMENT '用户邮箱',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `update_by` bigint DEFAULT NULL COMMENT '修改人ID',
                        `is_deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识(0-未删除 1-已删除)',
                        `openid` char(28) DEFAULT NULL COMMENT '微信 openid',
                        `oauth_provider` varchar(32) DEFAULT NULL COMMENT 'OAuth 提供商类型 (google, github, gitlab, etc.)',
                        `oauth_id` varchar(128) DEFAULT NULL COMMENT 'OAuth 提供商用户ID',
                        PRIMARY KEY (`id`) USING BTREE,
                        UNIQUE KEY `login_name` (`username`) USING BTREE
                        ) COMMENT='用户信息表';

                        CREATE TABLE `sys_user_role` (
                        `user_id` bigint NOT NULL COMMENT '用户ID',
                        `role_id` bigint NOT NULL COMMENT '角色ID',
                        PRIMARY KEY (`user_id`,`role_id`) USING BTREE
                        ) COMMENT='用户和角色关联表';

                        CREATE TABLE `tsz_attachment` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                        `file_name` varchar(255) NOT NULL COMMENT '文件名',
                        `file_path` varchar(255) NOT NULL COMMENT '文件路径',
                        `file_type` varchar(255) NOT NULL COMMENT '文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)',
                        `file_size` bigint DEFAULT NULL COMMENT '文件大小',
                        `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                        `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                        `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
                        PRIMARY KEY (`id`) USING BTREE
                        ) COMMENT='附件表';
                        ```
                        
                        # Dictionary field description
                        If the comment of a field in a data table contains '关联字典编码：xxx', it means that the field is a field in the dictionary table
                        ```sql
                        SELECT value, label FROM sys_dict_item WHERE dict_code = 'xxx'
                        ```
                        Then use the queried values and labels to replace the corresponding label with '关联字典编码：xxx' in the comment.

                        # `is_deleted` field description
                        Please pay attention to the is_deleted field in the database table, which indicates that the record has been deleted and is hidden. Please filter out these records when querying.

                        # Other Information
                        Database Type: MySQL
                        Database Version: 8.0.1
                        """, 
                }, StandardizedJsonOption.DefaultOptions),
                ParentId = agentOption.Id,
                CreatedAt = DateTime.UtcNow
            };
            await dbContext.AgentOptions.AddAsync(agentOption2);
            application.AgentOptionId = agentOption.Id;

            await dbContext.SaveChangesAsync();
            _applicationId = application.Id;
        }
    }

    public Guid? GetTenantId() => _tenantId;
    public void SetTenantId(Guid tenantId) => _tenantId = tenantId;
    public void ClearTenantId() => _tenantId = null;

    public Guid? GetApplicationId() => _applicationId;
    public void SetApplicationId(Guid applicationId) => _applicationId = applicationId;
    public void ClearApplicationId() => _applicationId = null;

}
