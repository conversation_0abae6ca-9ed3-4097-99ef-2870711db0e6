using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 用户角色关联实体，用于存储用户与角色之间的关联关系
/// </summary>
public class UserRole {

    /// <summary>
    /// 用户角色关联ID
    /// </summary>
    [Key]
    [Comment("用户角色关联ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [Comment("用户ID")]
    public required Guid UserId { get; set; }

    /// <summary>
    /// 关联的用户实体导航属性
    /// </summary>
    [Comment("关联的用户实体导航属性")]
    public User User { get; set; } = null!;

    /// <summary>
    /// 角色ID
    /// </summary>
    [Comment("角色ID")]
    public required Guid RoleId { get; set; }

    /// <summary>
    /// 关联的角色实体导航属性
    /// </summary>
    [Comment("关联的角色实体导航属性")]
    public Role Role { get; set; } = null!;
}
