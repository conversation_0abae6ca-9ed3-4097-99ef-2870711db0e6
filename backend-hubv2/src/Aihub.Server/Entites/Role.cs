using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites;

/// <summary>
/// 角色实体，用于存储角色信息
/// </summary>
public class Role {
    /// <summary>
    /// 角色ID
    /// </summary>
    [Key]
    [Comment("角色ID")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [Comment("角色名称")]
    public required string Name { get; set; }

    /// <summary>
    /// 角色编码
    /// </summary>
    [Comment("角色编码")]
    public required string Code { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>
    [Comment("显示顺序")]
    public int? Sort { get; set; }

    /// <summary>
    /// 角色状态(1-正常 0-停用)
    /// </summary>
    [Comment("角色状态(1-正常 0-停用)")]
    public RoleStatus Status { get; set; } = RoleStatus.Active;

    /// <summary>
    /// 数据权限(1-所有数据 2-部门及子部门数据 3-本部门数据 4-本人数据)
    /// </summary>
    [Comment("数据权限(1-所有数据 2-部门及子部门数据 3-本部门数据 4-本人数据)")]
    public DataScope? DataScope { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Comment("创建人ID")]
    public Guid? CreateBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Comment("创建时间")]
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新人ID
    /// </summary>
    [Comment("更新人ID")]
    public Guid? UpdateBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Comment("更新时间")]
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 逻辑删除标识(0-未删除 1-已删除)
    /// </summary>
    [Comment("逻辑删除标识(0-未删除 1-已删除)")]
    public bool IsDeleted { get; set; } = false;
}


/// <summary>
/// 角色状态枚举
/// </summary>
public enum RoleStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Active = 1,
    /// <summary>
    /// 停用
    /// </summary>
    Inactive = 0,
}

/// <summary>
/// 数据权限枚举
/// </summary>
public enum DataScope
{
    /// <summary>
    /// 所有数据
    /// </summary>
    All = 1,
    /// <summary>
    /// 部门及子部门数据
    /// </summary>
    DeptAndChildren = 2,
    /// <summary>
    /// 本部门数据
    /// </summary>
    Dept = 3,
    /// <summary>
    /// 本人数据
    /// </summary>
    Self = 4,
}

