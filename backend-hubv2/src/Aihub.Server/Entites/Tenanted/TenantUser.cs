using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Entites.Tenanted;

/// <summary>
/// 租户用户实体，用于存储租户用户信息
/// </summary>
public class TenantUser
{
    /// <summary>
    /// 用户唯一标识符
    /// </summary>
    [Key]
    [Comment("用户唯一标识符")]
    public required Guid Id { get; set; }

    /// <summary>
    /// 用户唯一键（如用户名或唯一标识）
    /// </summary>
    [Comment("用户唯一键（如用户名或唯一标识）")]
    public required string UniqueUserKey { get; set; }

    /// <summary>
    /// 用户密码的哈希值
    /// </summary>
    [Comment("用户密码的哈希值")]
    public required string PasswordHash { get; set; }

    /// <summary>
    /// 用户显示名称
    /// </summary>
    [Comment("用户显示名称")]
    public string? DisplayName { get; set; }

    /// <summary>
    /// 用户角色标识（如管理员、普通用户等）
    /// </summary>
    [Comment("用户角色标识（如管理员、普通用户等）")]
    public required string RoleKey { get; set; }

    /// <summary>
    /// 用户最后一次登录时间
    /// </summary>
    [Comment("用户最后一次登录时间")]
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 用户创建时间
    /// </summary>
    [Comment("用户创建时间")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 所属租户ID
    /// </summary>
    [ForeignKey(nameof(Tenant))]
    [Comment("所属租户ID")]
    public required Guid TenantId { get; set; }

    /// <summary>
    /// 所属租户实体导航属性
    /// </summary>
    [Comment("所属租户实体导航属性")]
    public Tenant Tenant { get; set; } = null!;
}