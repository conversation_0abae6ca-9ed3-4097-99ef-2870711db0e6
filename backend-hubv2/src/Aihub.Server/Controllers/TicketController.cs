using System.ComponentModel.DataAnnotations;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Aihub.Server.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Controllers;

[ApiController]
[Route("api/v1/ticket")]
public class TicketController : ControllerBase
{
    private readonly ILogger<TicketController> _logger;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly AihubOption _aihubOption;
    public TicketController(
        IDbContextFactory<AihubDbContext> dbContextFactory,
        IOptions<AihubOption> aihubOption,
        ILogger<TicketController> logger)
    {
        _dbContextFactory = dbContextFactory;
        _aihubOption = aihubOption.Value;
        _logger = logger;
    }

    [HttpPost("create")]
    public async Task<TicketResponse> CreateTicket([FromBody] CreateTicketRequest request)
    {
        // 从 Header 中获取 Authorization
        var authorization = Request.Headers["Authorization"].FirstOrDefault();
        if (string.IsNullOrEmpty(authorization)) throw new Exception("Invalid authorization");

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var tenant = await dbContext.Tenants.FirstOrDefaultAsync(t => t.SecretKey == authorization && !t.Disabled);
        if (tenant is null) throw new Exception("Invalid secret key");

        Application? application = null;

        if (request.Application is not null)
        {
            application = await dbContext.Applications.FirstOrDefaultAsync(a => a.Key == request.Application && a.TenantId == tenant.Id);
            if (application is null) throw new Exception("Invalid application id");
        }
        else
        {
            application = await dbContext.Applications.FirstOrDefaultAsync(a => a.TenantId == tenant.Id);
            if (application is null) throw new Exception("No application found");
        }

        var ticketCode = TicketHelper.CreateTicketCode();
        var ticketExpiredAt = DateTime.UtcNow.AddMinutes(_aihubOption.TicketExpiredMinutes);
        var ticket = new Ticket
        {
            Id = Guid.NewGuid(),
            UserKey = request.UserKey,
            TicketCode = ticketCode,
            ApplicationId = application.Id,
            ExpiredAt = ticketExpiredAt,
            CreatedAt = DateTime.UtcNow,
            TenantId = tenant.Id,
            IsUsed = false,
            TicketType = TicketType.OneTime,
        };

        await dbContext.Tickets.AddAsync(ticket);
        await dbContext.SaveChangesAsync();

        return new TicketResponse
        {
            TicketCode = ticket.TicketCode,
            ExpiredAt = ticketExpiredAt,
        };
    }



}

public class CreateTicketRequest
{
    [Required]
    public required string UserKey { get; set; }

    public string? Application { get; set; }
}

public class TicketResponse
{
    public required string TicketCode { get; set; }
    public required DateTime ExpiredAt { get; set; }
}