<!DOCTYPE html>
<html>
<head>
    <title>Aihub AI Server</title>
    <style>
        body { text-align: center; padding: 30px; max-width: 800px; margin: 0 auto; }
        .status { color: green; font-size: 24px; margin: 20px 0; }
        .signalr-status { font-size: 18px; margin: 20px 0; }
        .connected { color: green; }
        .disconnected { color: red; }
        .connecting { color: orange; }
        button { padding: 10px 20px; font-size: 16px; margin: 10px; cursor: pointer; }
        .debug-info { text-align: left; background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .debug-info pre { margin: 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Aihub AI Server</h1>
    <div class="status">Server Running</div>
    
    <div class="signalr-status" id="signalrStatus">SignalR: Not Connected</div>
    
    <button onclick="testSignalR()">Test SignalR Connection</button>
    <button onclick="clearDebugInfo()">Clear Debug Info</button>
    
    <div class="debug-info">
        <h3>Debug Information:</h3>
        <pre id="debugInfo">Ready to test SignalR connection...</pre>
    </div>

    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
    <script>
        let connection = null;
        const statusElement = document.getElementById('signalrStatus');
        const debugElement = document.getElementById('debugInfo');
        
        function addDebugInfo(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugElement.textContent += `[${timestamp}] ${message}\n`;
        }
        
        function updateStatus(status, className) {
            statusElement.textContent = `SignalR: ${status}`;
            statusElement.className = `signalr-status ${className}`;
        }
        
        async function testSignalR() {
            try {
                addDebugInfo('Starting SignalR connection test...');
                updateStatus('Connecting...', 'connecting');
                
                // Create connection
                connection = new signalR.HubConnectionBuilder()
                    .withUrl("/api/chathub")
                    .build();
                
                // Set up event handlers
                connection.onclose((error) => {
                    updateStatus('Disconnected', 'disconnected');
                    if (error) {
                        addDebugInfo(`Connection closed with error: ${error}`);
                    } else {
                        addDebugInfo('Connection closed successfully');
                    }
                });
                
                connection.onreconnecting((error) => {
                    updateStatus('Reconnecting...', 'connecting');
                    addDebugInfo(`Reconnecting due to error: ${error}`);
                });
                
                connection.onreconnected((connectionId) => {
                    updateStatus('Reconnected', 'connected');
                    addDebugInfo(`Reconnected with connection ID: ${connectionId}`);
                });
                
                // Start connection
                await connection.start();
                updateStatus('Connected', 'connected');
                addDebugInfo(`Successfully connected! Connection ID: ${connection.connectionId}`);
                addDebugInfo(`Connection State: ${connection.state}`);
                addDebugInfo(`Server URL: ${window.location.origin}/travelhub`);
                
            } catch (error) {
                updateStatus('Connection Failed', 'disconnected');
                addDebugInfo(`Connection failed: ${error.message}`);
                addDebugInfo(`Error details: ${JSON.stringify(error, null, 2)}`);
            }
        }
        
        function clearDebugInfo() {
            debugElement.textContent = 'Debug info cleared...\n';
        }
        
        // Add initial debug info
        addDebugInfo(`Page loaded at: ${new Date().toLocaleString()}`);
        addDebugInfo(`Current URL: ${window.location.href}`);
        addDebugInfo(`SignalR Hub URL: ${window.location.origin}/chathub`);
    </script>
</body>
</html>