
using Aihub.Server.Contexts;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Llm;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent.SubAgents;

[SubAgent("run_data_query_agent",
    Key = "database-agent",
    Description = 
        """
        这个Tool可以帮助用户查询业务实体的结构、数据等内容。
        """,
    DisplayName = "查询系统数据",
    ParameterName = "query",
    ParameterDescription = "请用自然语言描述用户需要查询的内容")]
public class DatabaseSubAgent : SubAgent
{


    public DatabaseSubAgent(
        SubAgentAndToolCallTimesCounter subAgentAndToolCallTimesCounter,
        SubAgentRunnerContext subAgentRunnerContext,
        ToolsDiscovery toolsDiscovery,
        LlmClient llmClient
    ) : base(subAgentAndToolCallTimesCounter, toolsDiscovery, subAgentRunnerContext, llmClient)
    {
    }

    public override IEnumerable<Type> GetToolTypes()
    {
        //return new[] { typeof(DatabaseSubAgentQuerySqlTool), typeof(DatabaseSubAgentResultDictChecker) };
        return new[] { typeof(DatabaseSubAgentQuerySqlTool) };
    }


    // public override async Task<bool> IsSubAgentJobDone(string finalMessage, List<AssistantMessageExtraToolCall> assistantMessageExtraToolCalls, CancellationToken cancellationToken)
    // {

    //     var baseLine = await SubAgentRunnerContext.IsSubAgnetJobDone(finalMessage, assistantMessageExtraToolCalls, cancellationToken);
    //     if (!baseLine) return false;

    //     var llmAwareMessages = await SubAgentRunnerContext.GetLlmAwareHistoryMessagesAsync();
    //     var llmAwareMessagesAsString = llmAwareMessages.Where(w => w is AssistantMessage)
    //         .Cast<AssistantMessage>()
    //         .SelectMany(s => s.Extra?.ToolCalls.Select(t => t.CallName ?? string.Empty) ?? Enumerable.Empty<string>())
    //         .ToList();

    //     if (llmAwareMessagesAsString.Last() == "check_result_dict") return true;

    //     if(llmAwareMessagesAsString.Any(a=> a == "check_result_dict")){
    //         // 如果有 check_result_dict 的调用，则说明已经检查过结果字典了
    //         return true;
    //     }

    //     await SubAgentRunnerContext.AddVirtualUserMessage("请使用 check_result_dict 工具检查结果字典", cancellationToken);
    //     return false;
    // }
}
