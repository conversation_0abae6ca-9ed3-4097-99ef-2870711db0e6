
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data.Common;
using System.Linq;
using System.Text.Json;
using Aihub.Server.Agent.SubAgents;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Options;
using Microsoft.EntityFrameworkCore;
using MySql.Data.MySqlClient;

namespace Aihub.Server.Agent.SubAgents;

[Obsolete("这个工具已经废弃")]
public class DatabaseSubAgentResultDictChecker : ITool {

    private readonly SubAgentContextManager _subAgentContextManager;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly ILogger _logger;

    public DatabaseSubAgentResultDictChecker(
        SubAgentContextManager subAgentContextManager,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        ILogger<DatabaseSubAgentQuerySqlTool> logger){
        _subAgentContextManager = subAgentContextManager;
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }


    [Tool("check_result_dict",
        """
        检查 `query_sql` 工具的返回结果是否包含字典字段。
        如果包含则需要再次调用 query_sql 工具，直到返回结果不包含字典字段为止。
        """,
        NameForUser = "检查结果字典",
        DescriptionForUser = "检查 `query_sql` 工具的返回结果是否包含字典字段，如果包含则需要再次调用 query_sql 工具，直到返回结果不包含字典字段为止。",
        NeedConfirm = false,
        UserAware = false,
        Severity = ToolSeverity.Medium
    )]
    public Task<object> CheckResultDict(
        [Required]
        [Description("The JSON string to check.")]
        string json){
        
        var keywords = new string[]{
            "construction",
            "service",
            "active",
            "supplement"
        };

        return Task.FromResult((object)(keywords.Any(keyword => json.Contains($"\"{keyword}\"")) ? "返回的结果中还有未关联字典表的字段，请重新编写带有字典表关联的查询 sql" : "返回结果正确的关联了字典表。"));
        
    }
}
