
using System.Text.Json;
using Aihub.Server.Agent.Tool;
using Aihub.Server.Contexts;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Llm;
using Aihub.Server.Options;
using Microsoft.Extensions.Options;

namespace Aihub.Server.Agent.SubAgents;

[SubAgent("run_manual_guide_agent",
    Key = "manual-guide-agent",
    Description = 
        """
        这个Tool可以帮助用户提供系统操作方法的指导，涉及到系统功能路径、操作步骤、操作方法等。
        """,
    DisplayName = "阅读系统操作手册",
    ParameterName = "operation_description",
    ParameterDescription = "请用自然语言描述用户需要执行的操作")]
public class ManualGuideSubAgent : SubAgent
{
    public ManualGuideSubAgent(
        SubAgentAndToolCallTimesCounter subAgentAndToolCallTimesCounter,
        SubAgentRunnerContext subAgentRunnerContext,
        ToolsDiscovery toolsDiscovery,
        LlmClient llmClient
    ) : base(subAgentAndToolCallTimesCounter, toolsDiscovery, subAgentRunnerContext, llmClient)
    { }

    public override IEnumerable<Type> GetToolTypes() => Array.Empty<Type>();
}

public class ManualGuideSubAgentPromptProvider : IAgentPromptProvider<ManualGuideSubAgent>
{
    public Task<string> GetPromptWithAgentOptionAsync(AgentOption agentOption)
    {
        if(string.IsNullOrWhiteSpace(agentOption.Extra)){
            throw new Exception("Agent option extra is empty");
        }

        var extra = JsonSerializer.Deserialize<AgentOptionManualGuideAgentExtra>(agentOption.Extra, StandardizedJsonOption.DefaultOptions);
        if(extra is null) throw new Exception("Agent option extra is not a valid manual guide agent extra");

        return Task.FromResult(
            $"""
            你是系统平台的智能助手，用户会向你提出各种操作问题，你需要根据用户的问题提供系统操作方法的指导。

            {extra.ManualGuide}

            # 输出
            - 请根据上面的给出的内容并结合用户的问题给出操作方法。
            - 操作方法应尽量详细，应尽可能把操作方法分割成多个步骤，并给出每个步骤的详细说明。
            - 每个步骤应该有步骤编号。
            - 如果用户的问题不在手册范围内，请告诉用户我们无法提供帮助。

            """);
    }
}
