namespace Aihub.Server.Agent.Tool;

public class ToolAttribute : Attribute
{
    public ToolAttribute(string name, string description)
    {
        Name = name;
        Description = description;
    }

    public string Name { get; set; }
    public string Description { get; set; }

    public string? NameForUser { get; set; }
    public string? DescriptionForUser { get; set; }

    /// <summary>
    /// 用户是否能够感知到该工具
    /// </summary>
    public bool UserAware { get; set; } = false;

    /// <summary>
    /// 是否需要用户确认
    /// </summary>
    public bool NeedConfirm { get; set; } = false;

    public ToolSeverity Severity { get; set; } = ToolSeverity.Low;
}

public enum ToolSeverity {
    Low, Medium, High
}