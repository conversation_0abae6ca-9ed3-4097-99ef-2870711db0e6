using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Agent;

public class AgentPromptAccessor : ITransientService{

    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly SubAgentsDiscovery _subAgentsDiscovery;
    private readonly IServiceProvider _serviceProvider;
    private readonly AgentKeyTypeAccessor _agentKeyTypeAccessor;
    public AgentPromptAccessor(
        AgentKeyTypeAccessor agentKeyTypeAccessor,
        IServiceProvider serviceProvider,
        SubAgentsDiscovery subAgentsDiscovery,
        IDbContextFactory<AihubDbContext> dbContextFactory)
    {
        _agentKeyTypeAccessor = agentKeyTypeAccessor;
        _serviceProvider = serviceProvider;
        _dbContextFactory = dbContextFactory;
        _subAgentsDiscovery = subAgentsDiscovery;
    }

    /// <summary>
    /// 根据 agentOptionId 获取对应的 Prompt 内容
    /// </summary>
    /// <param name="agentOptionId">代理选项的唯一标识</param>
    /// <returns>返回对应的 Prompt 字符串</returns>
    public async Task<string> GetPromptAsync(Guid agentOptionId)
    {
        // 创建数据库上下文
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

        // 根据 agentOptionId 查询 AgentOption 实体
        var agentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(x => x.Id == agentOptionId);
        if (agentOption is null) 
            throw new Exception("AgentOption is null"); // 未找到对应的 AgentOption，抛出异常

        // 获取当前 Agent 的类型
        var agentType = _agentKeyTypeAccessor.GetAgentType(agentOption.Key);
        if (agentType is null) 
            throw new Exception("AgentType is null"); // 未找到对应的 Agent 类型，抛出异常

        // 构造泛型接口类型 IAgentPromptProvider<agentType>
        var agentPromptProviderType = typeof(IAgentPromptProvider<>).MakeGenericType(agentType);

        // 通过依赖注入容器获取对应的 IAgentPromptProvider 实例
        var agentPromptProvider = _serviceProvider.GetRequiredService(agentPromptProviderType) as IAgentPromptProvider;
        if (agentPromptProvider is null) 
            throw new Exception("AgentPromptProvider is null"); // 未找到对应的 PromptProvider，抛出异常

        // 调用 PromptProvider 获取 Prompt 内容
        return await agentPromptProvider.GetPromptWithAgentOptionAsync(agentOption);
    }
}
