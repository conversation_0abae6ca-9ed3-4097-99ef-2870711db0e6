using Aihub.Server.Llm;
using Aihub.Server.Models;
using Aihub.Server.Scrutors;
using System.Text.Json.Nodes;

namespace Aihub.Server.Agent;

public class SubAgentConverter : ITransientService {
    private readonly SubAgentsDiscovery _subAgentsDiscovery;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SubAgentConverter> _logger;
    public SubAgentConverter(
        IServiceProvider serviceProvider,
        SubAgentsDiscovery subAgentsDiscovery,
        ILogger<SubAgentConverter> logger)
    {
        _serviceProvider = serviceProvider;
        _subAgentsDiscovery = subAgentsDiscovery;
        _logger = logger;
    }

    private string BuildSubAgentDescription(Type subAgentType, string parameterValue)
    {
        var builderType = typeof(ISubAgentDescriptionBuilder<>).MakeGenericType(subAgentType);
        var builder = _serviceProvider.GetService(builderType) as ISubAgentDescriptionBuilder;
        if(builder is null) {
            return parameterValue;
        }

        return builder.Build(parameterValue);
    }
    
    public List<AssistantMessageExtraToolCall> ConvertToolCalls(List<ToolCall> toolCalls, IEnumerable<SubAgentOptionWithDescription> provideTools)
    {
        return toolCalls
            .Where(a => provideTools.Any(b => b.SubAgentDescription.Name == a.FunctionName))
            .Select(a =>
            {
                var toolInfo = provideTools.FirstOrDefault(b => b.SubAgentDescription.Name == a.FunctionName);
                if(toolInfo is null) throw new Exception($"Tool info not found for tool call: {a.FunctionName}");

                string? description = null;
                try
                {
                    var parameterJson = JsonObject.Parse(a.FunctionArguments.ToString());
                    var parameterValue = parameterJson?[toolInfo.SubAgentDescription.ParameterName]?.ToString() ?? throw new Exception($"Parameter {toolInfo.SubAgentDescription.ParameterName} is null for tool call: {a.FunctionName}");
                    description = BuildSubAgentDescription(toolInfo.SubAgentDescription.ToolType, parameterValue);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error in SubAgentConverter.ConvertToolCalls");
                    // 如果解析失败，nothing happen
                }


                return new AssistantMessageExtraToolCall
                {
                    Id = Guid.NewGuid(),
                    CallId = a.Id,
                    Type = AssistantMessageExtraToolCallType.SubAgentCall,
                    InternalId = toolInfo.StoredAgentOptionId.ToString(),
                    CallName = a.FunctionName,
                    CallParameters = a.FunctionArguments,
                    Name = toolInfo.SubAgentDescription.DisplayName,
                    Description = description ?? toolInfo.SubAgentDescription.DisplayName,
                    IsUserAware = toolInfo.SubAgentDescription.UserAware,
                    NeedConfirm = false,
                    IsConfirm = true,
                    Severity = toolInfo.SubAgentDescription.Severity switch {
                        SubAgentSeverity.Low => AssistantMessageExtraToolCallSeverity.Low,
                        SubAgentSeverity.Medium => AssistantMessageExtraToolCallSeverity.Medium,
                        SubAgentSeverity.High => AssistantMessageExtraToolCallSeverity.High,
                        _ => throw new InvalidOperationException("Invalid severity")
                    }
                };
            }).ToList();
    }
}