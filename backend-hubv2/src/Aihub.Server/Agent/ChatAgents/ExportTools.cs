using Aihub.Server.Agent.Tool;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Aihub.Server.Agent.ChatAgents;

public class ExportTools : ITool {


    [Tool("markdown_table_to_excel",
        "markdown_table_to_excel 可以将输入的 markdown 表格转换为 excel 文件，并输出下载连接，请注意导出表格需要占用一些资源，请在用户明确要求的情况下才能执行。",
        NameForUser = "导出为 Excel",
        DescriptionForUser = "导出为 Excel 文件并提供下载连接",
        NeedConfirm = false,
        UserAware = false,
        Severity = ToolSeverity.Low
    )]
    public Task<object> MarkdownTableToExcelTool(
        [Required]
        [Description("导出的文件名。")]
        string title,
        [Required]
        [Description("The markdown table to export.")]
        string markdownTable) {
        // TODO 实现导出为 excel 文件
        return Task.FromResult<object>($"https://example.com/{title}.xlsx");
    }

}
