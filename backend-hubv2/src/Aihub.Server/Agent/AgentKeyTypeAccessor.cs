using Aihub.Server.Agent.ChatAgents;
using Aihub.Server.Scrutors;

namespace Aihub.Server.Agent;

public class AgentKeyTypeAccessor : ITransientService{
    private readonly SubAgentsDiscovery _subAgentsDiscovery;

    public AgentKeyTypeAccessor(SubAgentsDiscovery subAgentsDiscovery){
        _subAgentsDiscovery = subAgentsDiscovery;
    }

    public Type? GetChatAgentType(string agentKey){
        switch(agentKey){
            case "primary":
                return typeof(PrimaryAgent);
            default:
                return null;
        }
    }

    public Type? GetAgentType(string agentKey)
    {

        var chatAgentType = GetChatAgentType(agentKey);
        if (chatAgentType is not null) return chatAgentType;

        var agentDescription = _subAgentsDiscovery.DiscoverTools().FirstOrDefault(x => x.Key == agentKey);
        return agentDescription?.ToolType;
    }
}
