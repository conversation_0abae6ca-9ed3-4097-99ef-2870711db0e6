namespace Aihub.Server.Agent;

[AttributeUsage(AttributeTargets.Class)]
public class SubAgentAttribute : Attribute
{
    public string Name { get; set; }
    public required string Description { get; set; }
    public required string Key {get;set;}
    public required string DisplayName { get; set; }
    public string ParameterName {get;set;} = "query";
    public string ParameterDescription {get;set;} = "query 参数的描述";
    public SubAgentSeverity Severity  {get;set;} = SubAgentSeverity.Low;
    public SubAgentAttribute(string name)
    {
        Name = name;
    }
}

public enum SubAgentSeverity {
    Low, Medium, High
}