using System.Text.Json;
using Aihub.Server.Contexts;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Exceptions;
using Aihub.Server.Models;
using Aihub.Server.Options;
using Aihub.Server.Scrutors;
using Aihub.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Agent;

public class SubAgentRunner : ITransientService
{
    private readonly SubAgentAndToolCallTimesCounter _subAgentAndToolCallTimesCounter;
    private readonly ICurrentSessionIdAccessor _currentSessionIdAccessor;
    private readonly SubAgentsDiscovery _subAgentsDiscovery;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly IServiceProvider _serviceProvider;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ChatHubWrapper _chatHubWrapper;
    private readonly ILogger _logger;

    public SubAgentRunner(
        SubAgentAndToolCallTimesCounter subAgentAndToolCallTimesCounter,
        ICurrentSessionIdAccessor currentSessionIdAccessor,
        SubAgentsDiscovery subAgentsDiscovery,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        IServiceProvider serviceProvider,
        IServiceScopeFactory serviceScopeFactory,
        ChatHubWrapper chatHubWrapper,
        ILogger<SubAgentRunner> logger)
    {
        _subAgentAndToolCallTimesCounter = subAgentAndToolCallTimesCounter;
        _currentSessionIdAccessor = currentSessionIdAccessor;
        _subAgentsDiscovery = subAgentsDiscovery;
        _dbContextFactory = dbContextFactory;
        _serviceProvider = serviceProvider;
        _serviceScopeFactory = serviceScopeFactory;
        _chatHubWrapper = chatHubWrapper;
        _logger = logger;
    }
    public async Task RunAsync(Guid subAgentOptionId, Guid sessionId, Guid parentToolCallId, CancellationToken cancellationToken)
    {
        using var scope = _serviceScopeFactory.CreateScope();

        // 设置当前代理上下文
        var sessionContextManager = scope.ServiceProvider.GetRequiredService<SessionContextManager>();
        sessionContextManager.SetSessionId(_currentSessionIdAccessor.GetSessionId() ?? throw new Exception("SessionId is null"));

        var subAgentContextManager = scope.ServiceProvider.GetRequiredService<SubAgentContextManager>();
        subAgentContextManager.InitializeWith(sessionId, subAgentOptionId);

        // 初始化计数器
        var subAgentAndToolCallTimesCounter = scope.ServiceProvider.GetRequiredService<SubAgentAndToolCallTimesCounter>();
        subAgentAndToolCallTimesCounter.AssociateWith(_subAgentAndToolCallTimesCounter);

        try
        {
            // 初始化会话上下文
            var dbContextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<AihubDbContext>>();
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var agentOption = await dbContext.AgentOptions.FirstOrDefaultAsync(x => x.Id == subAgentContextManager.GetSubAgentId());
            if (agentOption is null) throw new Exception("AgentOption not found");

            var agent = CreateSubAgent(scope, agentOption.Key);
            var result = await agent.RunAsync(cancellationToken);

            if (result == SubAgentResultType.JobDone)
            {
                var lastMessage = await dbContext.StoredMessages
                    .OrderByDescending(o => o.CreatedAt)
                    .FirstOrDefaultAsync(f => f.SessionId == sessionId);

                if (lastMessage is null) throw new Exception("SubAgent 执行结束，但未找到最后一条消息");
                if (lastMessage.Category != StoredMessageCategory.Assistant) throw new Exception("SubAgent 执行结束，但最后一条消息不是 Assistant 消息");

                await UpdateToolCallResult(scope, sessionId, parentToolCallId, lastMessage.Content, true);
            }
        }
        catch (MaxToolCallLoopTimesReachedException ex)
        {
            _logger.LogInformation(ex, "Max tool call loop times reached");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running sub agent");
            await UpdateToolCallResult(scope, sessionId, parentToolCallId, "error", false);
        }
        finally
        {
            scope.Dispose();
        }

    }

    private async Task UpdateToolCallResult(IServiceScope scope, Guid sessionId, Guid parentToolCallId, object? result, bool isSuccess)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        
        var session = await dbContext.Sessions.FirstOrDefaultAsync(f => f.Id == sessionId);
        if (session is null) throw new Exception("Session not found");

        var parentMessage = await dbContext.StoredMessages.FirstOrDefaultAsync(f => f.Id == session.ParentMessageId);
        if (parentMessage is null) throw new Exception("Parent message not found");

        var assistantMessageExtra = parentMessage.Extra is not null
            ? JsonSerializer.Deserialize<AssistantMessageExtra>(parentMessage.Extra, StandardizedJsonOption.DefaultOptions)
            : null;
        if (assistantMessageExtra is null) throw new Exception("Assistant message extra not found");

        var toolCall = assistantMessageExtra.ToolCalls.FirstOrDefault(f => f.Id == parentToolCallId);
        if (toolCall is null) throw new Exception("Tool call not found");

        var toolExecutor = scope.ServiceProvider.GetRequiredService<ToolExecutor>();
        await toolExecutor.UpdateToolCallResult(parentMessage.Id, toolCall, result, isSuccess);
    }

    private ISubAgent CreateSubAgent(IServiceScope scope, string agentKey)
    {
        var subAgentDescription = _subAgentsDiscovery.DiscoverTools().FirstOrDefault(d => d.Key == agentKey);
        if (subAgentDescription is null) throw new Exception($"Sub agent description not found for key: {agentKey}");

        var subAgent = scope.ServiceProvider.GetRequiredService(subAgentDescription.ToolType) as ISubAgent;
        if (subAgent is null) throw new Exception($"Sub agent not found for key: {agentKey}");

        return subAgent;
    }
}