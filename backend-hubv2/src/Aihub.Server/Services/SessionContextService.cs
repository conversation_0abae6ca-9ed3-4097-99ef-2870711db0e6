using System.Collections.Concurrent;
using Aihub.Server.Agent;
using Aihub.Server.Contexts;
using Aihub.Server.Converters;
using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Models;
using Aihub.Server.Models.Dto;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Services;

public class SessionContextService
    : ICurrentClientIdentityAccessor,
      IScopedService
{

    private readonly AgentPromptAccessor _agentPromptAccessor;
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;
    private readonly SessionContextManager _sessionContextManager;
    private readonly SessionService _sessionService;
    private readonly MessageService _messageService;
    private readonly ClientIdentityService _clientIdentityService;
    private readonly AgentRunner _agentRunner;
    private readonly ChatHubWrapper _chatHubWrapper;
    private readonly TenantContextManager _tenantContextManager;
    private readonly ApplicationContextManager _applicationContextManager;

    private static ConcurrentDictionary<string, string> _sessionTicketCodeUserKeyMap = new();
    private static ConcurrentDictionary<string, Guid> _sessionUserKeyClientIdentityIdMap = new();
    private static ConcurrentDictionary<Guid, string> _sessionClientIdentityIdConnectionMap = new();
    private static ConcurrentDictionary<Guid, bool> _sessionClientIdentityIdIsBusyMap = new();
    private static ConcurrentDictionary<Guid, CancellationTokenSource> _sessionClientIdentityIdCancelTokenSourceMap = new();

    private Guid? _clientIdentityId;
    private string? _userKey;

    public SessionContextService(
        AgentPromptAccessor agentPromptAccessor,
        IDbContextFactory<AihubDbContext> dbContextFactory,
        SessionContextManager sessionContextManager,
        ChatHubWrapper chatHubWrapper,
        SessionService sessionService,
        MessageService messageService,
        AgentRunner agentRunner,
        ClientIdentityService clientIdentityService,
        TenantContextManager tenantContextManager,
        ApplicationContextManager applicationContextManager
    )
    {
        _agentPromptAccessor = agentPromptAccessor;
        _dbContextFactory = dbContextFactory;
        _sessionContextManager = sessionContextManager;
        _chatHubWrapper = chatHubWrapper;
        _sessionService = sessionService;
        _messageService = messageService;
        _agentRunner = agentRunner;
        _clientIdentityService = clientIdentityService;
        _tenantContextManager = tenantContextManager;
        _applicationContextManager = applicationContextManager;
    }

    public Guid? GetClientIdentityId() => _clientIdentityId;
    public string? GetUserKey() => _userKey;

    private Guid GetClientIdentityId(string userKey)
    {
        if (_sessionUserKeyClientIdentityIdMap.TryGetValue(userKey, out var clientIdentityId))
        {
            return clientIdentityId;
        }
        else
        {
            throw new Exception("ClientIdentityId not found");
        }
    }

    /// <summary>
    /// 手动初始化上下文
    /// </summary>
    /// <param name="ticketCode"></param>
    /// <param name="sessionId"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task ManualInitializeWith(string ticketCode, Guid sessionId){

        // 检查 userKey 是否为空
        if (ticketCode is null) throw new Exception("TicketCode is null");

        await InitializeContextFromTicketCode(ticketCode);

        // 保存当前会话ID
        _sessionContextManager.SetSessionId(sessionId);

    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ticketCode"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private async Task TradeUserKeyFromTicketCode(string ticketCode){

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var ticket = await dbContext.Tickets.FirstOrDefaultAsync(t => t.TicketCode == ticketCode);

        if (ticket is null) throw new Exception("Ticket not found");
        if (ticket.IsUsed) throw new Exception("Ticket already used");
        if (ticket.ExpiredAt < DateTime.UtcNow) throw new Exception("Ticket expired");

        if(ticket.TicketType == TicketType.OneTime){
            ticket.IsUsed = true;
            await dbContext.SaveChangesAsync();
        }

        _tenantContextManager.SetTenantId(ticket.TenantId);
        _applicationContextManager.SetApplicationId(ticket.ApplicationId);
        _userKey = ticket.UserKey;

        // 获取或创建当前用户的 ClientIdentityId
        var clientIdentityId = await _clientIdentityService.GetOrCreateClientIdentity(_userKey, ticket.TenantId);
        _clientIdentityId = clientIdentityId;

        _sessionUserKeyClientIdentityIdMap.AddOrUpdate(ticket.UserKey, clientIdentityId, (key, oldValue) => clientIdentityId);
        _sessionTicketCodeUserKeyMap.AddOrUpdate(ticketCode, ticket.UserKey, (key, oldValue) => ticket.UserKey);
    }

    /// <summary>
    /// 将 ticketCode 转换为 userKey
    /// </summary>
    /// <param name="ticketCode"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private async Task InitializeContextFromTicketCode(string ticketCode)
    {
        // 如果 ticketCode 已经存在 证明这个 ticketCode 已经验证过可以直接初始化上下文
        if (_sessionTicketCodeUserKeyMap.TryGetValue(ticketCode, out var userKey))
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
            var ticket = await dbContext.Tickets.FirstOrDefaultAsync(t => t.TicketCode == ticketCode);
            if (ticket is null) throw new Exception("Ticket not found");

            var tenant = await dbContext.Tenants.FirstOrDefaultAsync(t => t.Id == ticket.TenantId);
            if (tenant is null) throw new Exception("Tenant not found");
            
            var application = await dbContext.Applications.FirstOrDefaultAsync(a => a.Id == ticket.ApplicationId);
            if (application is null) throw new Exception("Application not found");

            _userKey = userKey;
            _tenantContextManager.SetTenantId(tenant.Id);
            _applicationContextManager.SetApplicationId(application.Id);

            if (_sessionUserKeyClientIdentityIdMap.TryGetValue(userKey, out var clientIdentityId))
            {
                _clientIdentityId = clientIdentityId;
                _sessionUserKeyClientIdentityIdMap.AddOrUpdate(userKey, clientIdentityId, (key, oldValue) => clientIdentityId);
            }
            else
            {
                throw new Exception("ClientIdentityId not found");
            }

        }
        else
        {
            throw new Exception("UserKey not found");
        }
    }

    /// <summary>
    /// 处理客户端连接事件，初始化用户会话和身份信息
    /// </summary>
    /// <param name="connectionId">SignalR 连接ID</param>
    /// <param name="userKey">用户唯一标识</param>
    /// <returns>当前会话ID</returns>
    public async Task OnConnectedAsync(string connectionId, string ticketCode, Func<string, Task> preSendHistoryMessages)
    {
        // 更换 ticketCode 为 userKey
        await TradeUserKeyFromTicketCode(ticketCode);

        // 如果该 clientIdentityId 已经有旧的连接，则断开旧连接
        if (_sessionClientIdentityIdConnectionMap.TryGetValue(_clientIdentityId!.Value, out var oldConnectionId))
        {
            await _chatHubWrapper.DisconnectClient(oldConnectionId);
        }
        // 更新 clientIdentityId 到当前 connectionId 的映射
        _sessionClientIdentityIdConnectionMap.AddOrUpdate(_clientIdentityId!.Value, connectionId, (key, oldValue) => connectionId);

        // 获取 当前应用程序ID
        var applicationId = _applicationContextManager.GetApplicationId();
        if( applicationId is null) throw new Exception("ApplicationId is null");

        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var application = await dbContext.Applications.FirstOrDefaultAsync(a => a.Id == applicationId);
        if (application is null) throw new Exception("Application not found");

        IEnumerable<Message> history = Enumerable.Empty<Message>();

        // 获取该用户最新的会话ID
        var sessionId = await _sessionService.GetLatestChatSessionId(_clientIdentityId!.Value, applicationId.Value);
        if (sessionId == null)
        {
            // 如果没有会话，则新建一个会话
            sessionId = await _sessionService.CreateChatSession(_clientIdentityId!.Value, applicationId.Value);
            _sessionContextManager.SetSessionId(sessionId.Value);

        }
        else
        {
            // 如果已有会话，获取历史消息并发送给客户端
            _sessionContextManager.SetSessionId(sessionId.Value);
            var messages = await _messageService.GetUserAwareHistoryMessages(sessionId.Value);
            history = messages;
        }

        await preSendHistoryMessages(sessionId.ToString()!);
        await _chatHubWrapper.SendDebug($"SessionId: {sessionId}");


        var random3Prompts = application.RecommendedStartPrompts
            .OrderBy(p => Guid.NewGuid()).Take(3).ToList();

        await _chatHubWrapper.SendProfile(new ProfileVo
        {
            Key = application.Key,
            DisplayName = application.DisplayName,
            Description = application.Description,
            Icon = application.Icon,
            SignOutUrl = application.SignOutUrl,
            Theme = application.Theme,
            AiAvatar = application.AiAvatar,
            UserAvatar = application.UserAvatar,
            RecommendedStartPrompts = random3Prompts
        });

        if(history.Any())
        {
            await _chatHubWrapper.SendHistoryMessages(history.Select(m => MessageToDtoConverter.ConvertToDto(m)).ToList());
        }
    }

    /// <summary>
    /// 获取当前用户的最新会话ID
    /// </summary>
    /// <param name="userKey"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<string> GetLatestSessionId(string ticketCode)
    {
        await InitializeContextFromTicketCode(ticketCode);

        var applicationId = _applicationContextManager.GetApplicationId();
        if (applicationId is null) throw new Exception("ApplicationId is null");

        var sessionId = await _sessionService.GetLatestChatSessionId(_clientIdentityId!.Value, applicationId.Value);
        return sessionId.ToString()!;
    }

    /// <summary>
    /// 创建新的会话
    /// </summary>
    /// <param name="userKey"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<string> CreateNewSession(string ticketCode)
    {
        if (ticketCode is null) throw new Exception("ticketCode is null");
        await InitializeContextFromTicketCode(ticketCode);

        var sessionId = await _sessionService.CreateChatSession(_clientIdentityId!.Value, _applicationContextManager.GetApplicationId()!.Value);
        _sessionContextManager.SetSessionId(sessionId);
        await _chatHubWrapper.CreateNewSessionSuccess(sessionId);
        return sessionId.ToString()!;
    }

    public async Task Cancel(string? ticketCode){
        if (ticketCode is null) throw new Exception("TicketCode is null");
        await InitializeContextFromTicketCode(ticketCode);

        var clientIdentityId = GetClientIdentityId(_userKey!);

        _sessionClientIdentityIdIsBusyMap.AddOrUpdate(clientIdentityId, false, (key, oldValue) => false);

        if(_sessionClientIdentityIdCancelTokenSourceMap.TryGetValue(clientIdentityId, out var cancelTokenSource)){
            if(!cancelTokenSource.IsCancellationRequested){
                cancelTokenSource.Cancel();
            }
        }
        _sessionClientIdentityIdCancelTokenSourceMap.TryRemove(clientIdentityId, out _);
    }

    private bool AbortIfNotIdle(Guid clientIdentityId){
        if(_sessionClientIdentityIdIsBusyMap.TryGetValue(clientIdentityId, out var isBusy)){
            if(isBusy){
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 处理用户发送消息的逻辑，包括会话初始化、消息存储、Agent 执行等
    /// </summary>
    /// <param name="userKey">用户唯一标识</param>
    /// <param name="message">用户发送的消息内容</param>
    public async Task UserMessage(string? ticketCode, string message)
    {
        // 检查 userKey 是否为空
        if (ticketCode is null) throw new Exception("TicketCode is null");
        await InitializeContextFromTicketCode(ticketCode);

        // 获取当前应用ID
        var applicationId = _applicationContextManager.GetApplicationId();
        if (applicationId is null) throw new Exception("ApplicationId is null");

        // 获取当前用户在该应用下的最新会话ID
        var sessionId = await _sessionService.GetLatestChatSessionId(_clientIdentityId!.Value, applicationId.Value);

        // 检查会话ID是否存在
        if(sessionId is null) throw new Exception("Session not found");

        // 如果需要，初始化会话（如首次对话时生成系统Prompt等）
        await _sessionService.InitializeSessionIfNeeded(sessionId.Value, _agentPromptAccessor.GetPromptAsync);

        // 设置当前会话ID到上下文管理器
        _sessionContextManager.SetSessionId(sessionId.Value);

        // 如果当前用户正在进行其他操作，则中止本次处理
        if(AbortIfNotIdle(_clientIdentityId!.Value)){
            return;
        }

        // 创建新的取消令牌，用于后续操作的取消控制
        var cancellationTokenSource = new CancellationTokenSource();
        SetBusy(_clientIdentityId!.Value, cancellationTokenSource);

        // 存储用户消息到数据库
        await _messageService.CreateUserMessage(sessionId.Value, message);

        // 执行 Agent 处理用户消息
        await _agentRunner.RunAsync(ticketCode, sessionId.Value, cancellationTokenSource.Token);

    }

    public Task<string> HandleExcpetionWithUserFriendlyMessage(Exception? exception){
        
        if(exception is null){
            return Task.FromResult("连接已断开");
        }

        switch(exception){
            case Exception e:
                return Task.FromResult(e.Message);
            default:
                return Task.FromResult("系统错误，请稍后重试");
        }
    }


    private void SetBusy(Guid clientIdentityId, CancellationTokenSource cancellationTokenSource)
    {
        // 记录当前用户的取消令牌和忙碌状态
        _sessionClientIdentityIdCancelTokenSourceMap.AddOrUpdate(clientIdentityId, cancellationTokenSource, (key, oldValue) => cancellationTokenSource);
        _sessionClientIdentityIdIsBusyMap.AddOrUpdate(clientIdentityId, true, (key, oldValue) => true);

    }

    public void SetIdle()
    {
        var clientIdentityId = GetClientIdentityId(_userKey!);
        _sessionClientIdentityIdIsBusyMap.AddOrUpdate(clientIdentityId, false, (key, oldValue) => false);
        _sessionClientIdentityIdCancelTokenSourceMap.TryRemove(clientIdentityId, out _);
    }

}
