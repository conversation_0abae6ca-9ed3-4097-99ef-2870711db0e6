using Aihub.Server.EfCore;
using Aihub.Server.Entites.Tenanted;
using Aihub.Server.Scrutors;
using Microsoft.EntityFrameworkCore;

namespace Aihub.Server.Services;

public class ClientIdentityService : ITransientService
{
    private readonly IDbContextFactory<AihubDbContext> _dbContextFactory;

    public ClientIdentityService(IDbContextFactory<AihubDbContext> dbContextFactory)
    {
        _dbContextFactory = dbContextFactory;
    }


    public async Task<Guid> GetOrCreateClientIdentity(string userKey, Guid tenantId)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
        var clientIdentity = await dbContext.ClientIdentities.FirstOrDefaultAsync(x => x.UniqueUserKey == userKey);

        if (clientIdentity == null)
        {

            clientIdentity = new ClientIdentity
            {
                Id = Guid.CreateVersion7(),
                UniqueUserKey = userKey,
                TenantId = tenantId,
                CreatedAt = DateTime.UtcNow,
            };

            dbContext.ClientIdentities.Add(clientIdentity);
            await dbContext.SaveChangesAsync();
        }

        return clientIdentity.Id;
    }
}