namespace Aihub.Server.Services;

public class TicketHelper {
    public static string CreateTicketCode()
    {
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var ticketCode = new char[64];
        for (int i = 0; i < 64; i++)
        {
            ticketCode[i] = chars[random.Next(chars.Length)];
        }
        return "ah-sk-" + new string(ticketCode);
    }
}