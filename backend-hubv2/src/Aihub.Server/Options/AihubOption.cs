namespace Aihub.Server.Options;

public class AihubOption {

    public int MaxToolCallLoopTimes { get; set; } = 100;
    public int TicketExpiredMinutes { get; set; } = 30;

    //model: "qwen/qwen3-coder:free", // FREE
    //model: "google/gemini-2.5-flash", //$0.30/M input tokens $2.50/M output tokens
    //model:"openai/gpt-4.1-nano", //$0.10/M input tokens $0.40/M output tokens
    //model:"google/gemini-2.0-flash-001", //$0.10/M input tokens $0.40/M output tokens
    //model:"deepseek/deepseek-chat-v3-0324", // $0.25/M input tokens $0.85/M output tokens
    //model: "deepseek/deepseek-chat-v3-0324:free", // FREE but slow
    //model: "google/gemini-2.5-flash-lite-preview-06-17", //$0.10/M input tokens $0.40/M output tokens
    //model: "moonshotai/kimi-k2:free", // FREE 
    //model: "moonshotai/kimi-k2", // $0.14/M input tokens $2.49/M output tokens
    //model: "openai/gpt-5-nano", //$0.05/M input token $0.40/M output tokens
    //model: "qwen/qwen3-235b-a22b-2507", //$0.078/M input tokens $0.312/M output tokens
    //model: "google/gemini-2.0-flash-001" //$0.40/M output tokens $0.026/K input

    public string DefaultModel { get; set; } = ""; 
    public string OpenRouterApiKey { get; set; } = "";

}