using Aihub.Server.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Aihub.Server.Filters;

public class ApiResponseWrapper : ActionFilterAttribute
{
    public override void OnActionExecuted(ActionExecutedContext context)
    {
        if (context.Exception is not null)
        {
            var response = new ApiResponse
            {
                Success = false,
                Message = context.Exception.Message
            };
            context.Result = new ObjectResult(response);
            context.ExceptionHandled = true;
        } 
        else if (context.Result is ObjectResult objectResult)
        {
            // 如果已经是 ApiResponse 格式，就不再包装
            if (objectResult.Value is ApiResponse)
            {
                return;
            }

            var response = new ApiResponse<object>
            {
                Success = true,
                Data = objectResult.Value
            };

            context.Result = new ObjectResult(response)
            {
                StatusCode = objectResult.StatusCode
            };
        }
        else if (context.Result is EmptyResult)
        {
            context.Result = new ObjectResult(new ApiResponse { Success = true })
            {
                StatusCode = 200
            };
        }

        base.OnActionExecuted(context);
    }
}