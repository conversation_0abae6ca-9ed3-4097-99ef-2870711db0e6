import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "./components/layout/Layout";
import Dashboard from "./pages/Dashboard";
import SystemAssistant from "./pages/SystemAssistant";
import Login from "./pages/Login";
import Applications from "./pages/Applications";
import ApplicationDetail from "./pages/ApplicationDetail";
import ApplicationEdit from "./pages/ApplicationEdit";
import Agents from "./pages/Agents";
import AgentEdit from "./pages/AgentEdit";
import AgentDatabaseSettings from "./pages/AgentDatabaseSettings";
import AgentTest from "./pages/AgentTest";
import Sessions from "./pages/Sessions";
import SessionUsers from "./pages/SessionUsers";
import UserSessionHistory from "./pages/UserSessionHistory";
import Users from "./pages/Users";
import UserDetail from "./pages/UserDetail";
import UserEdit from "./pages/UserEdit";

import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* 登录页面 - 独立布局，无导航菜单 */}
          <Route path="/login" element={<Login />} />
          
          {/* 其他页面 - 使用带导航的布局 */}
          <Route path="/*" element={
            <Layout>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/assistant" element={<SystemAssistant />} />
                <Route path="/applications" element={<Applications />} />
                <Route path="/applications/:id" element={<ApplicationDetail />} />
                <Route path="/applications/:id/edit" element={<ApplicationEdit />} />
                <Route path="/agents" element={<Agents />} />
                <Route path="/agents/:id/edit" element={<AgentEdit />} />
                <Route path="/agents/:id/database" element={<AgentDatabaseSettings />} />
                <Route path="/agents/:id/test" element={<AgentTest />} />
                <Route path="/sessions" element={<Sessions />} />
                <Route path="/session-users" element={<SessionUsers />} />
                <Route path="/session-users/:userId/history" element={<UserSessionHistory />} />
                <Route path="/users" element={<Users />} />
                <Route path="/users/:id" element={<UserDetail />} />
                <Route path="/users/:id/edit" element={<UserEdit />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Layout>
          } />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
