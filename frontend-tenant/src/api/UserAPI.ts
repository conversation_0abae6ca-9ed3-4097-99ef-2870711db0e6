import request from '../utils/request';
import { UserQueryParam, UserPageResponse, CreateUserRequest, EditUserRequest, UserDetailResponse, UserStatisticResponse } from "../types/user";

const USER_BASE_URL = '/api/v1/users';



const UserAPI = {
  /**
   * 分页查询用户列表
   */
  async getUserPage(params: UserQueryParam): Promise<UserPageResponse> {
    return request<UserPageResponse>({
      url: `${USER_BASE_URL}/page`,
      method: 'GET',
      params,
    });
  },

  /**
   * 根据ID获取用户详情
   */
  async getUserById(userId: string): Promise<UserDetailResponse> {
    return request<UserDetailResponse>({
      url: USER_BASE_URL,
      method: 'GET',
      params: { userId }
    });
  },

  /**
   * 创建用户
   */
  async createUser(data: Partial<CreateUserRequest>): Promise<string> {
    return request<string>({
      url: USER_BASE_URL,
      method: 'POST',
      data,
    });
  },

  /**
   * 修改用户信息
   */
  async editUser(data: Partial<EditUserRequest>): Promise<string> {
    return request<string>({
      url: `${USER_BASE_URL}/${data.id}`,
      method: 'PUT',
      data,
    });
  },

  /**
   * 重置密码
   */
  async resetUserPassword(id: string, password: string): Promise<string> {
    return request<string>({
      url: `${USER_BASE_URL}/${id}/password/reset`,
      method: 'PUT',
      params: { password },
    })
  },

  /**
   * 删除用户信息
   */
  async deleteUserById(id: string): Promise<string> {
    return request<string>({
      url: `${USER_BASE_URL}/${id}`,
      method: "DELETE"
    })
  },

  /**
   * 修改用户状态
   */
  async changeUserStatus(id: string, status: string): Promise<string> {
    return request<string>({
      url: `${USER_BASE_URL}/${id}/status`,
      method: 'PATCH',
      params: { status }
    })
  },

  /**
   * 获取用户统计数据
   */
  async getUserStatisticInfo(): Promise<UserStatisticResponse> {
    return request<UserStatisticResponse>({
      url: `${USER_BASE_URL}/statistics`,
      method: 'GET'
    })
  }

};

export default UserAPI;
