import request from '../utils/request';
import { Auth } from '../utils/auth';
import { AxiosHeaders } from "axios";
import {
  LoginRequest,
  LoginResponse,
  RefreshTokenResponse,
  CaptchaResponse,
  OAuthLoginRequest,
} from '../types/auth';

const AUTH_BASE_URL = '/api/v1/auth';

const AuthAPI = {
  _isRefreshing: false as boolean,
  _waitingQueue: [] as Array<() => void>,

  /** 自动刷新 token 队列包装 */
  async _requestWithAutoRefresh<T>(fn: () => Promise<T>): Promise<T> {
    try {
      if (Auth.isTokenExpiringSoon()) {
        await this._enqueueTokenRefresh();
      }
      return await fn();
    } catch (err: any) {
      const code = err?.code ?? err?.response?.data?.code;
      if (err.message?.includes('ACCESS_TOKEN_INVALID') || code === 401) {
        await this._enqueueTokenRefresh();
        return fn();
      }
      throw err;
    }
  },

  async _enqueueTokenRefresh(): Promise<void> {
    return new Promise((resolve) => {
      const retryRequest = () => resolve();
      this._waitingQueue.push(retryRequest);

      if (!this._isRefreshing) {
        this._isRefreshing = true;
        this.refreshToken()
          .then(() => {
            this._waitingQueue.forEach(cb => cb());
            this._waitingQueue.length = 0;
          })
          .catch(() => {
            Auth.clearTokens();
            window.location.href = '/login';
            this._waitingQueue.forEach(cb => cb());
            this._waitingQueue.length = 0;
          })
          .finally(() => {
            this._isRefreshing = false;
          });
      }
    });
  },

  /** 登录接口 */
  async login(data: LoginRequest): Promise<LoginResponse> {
    const formData = new FormData();
    formData.append('username', data.username);
    formData.append('password', data.password);
    formData.append('captchaCode', data.captchaCode); 
    formData.append('captchaKey', data.captchaKey); 

    const result = await this._requestWithAutoRefresh(() =>
      request<LoginResponse>({
        url: `${AUTH_BASE_URL}/login`,
        method: 'post',
        data: formData,
      })
    );

    Auth.setTokens(result.accessToken, result.refreshToken, result.expiresIn, !!data.rememberMe);

    return result;
  },

  /** 刷新 token */
  async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = Auth.getRefreshToken();
    if (!refreshToken) throw new Error('No refresh token available');

    const result = await request<RefreshTokenResponse>({
      url: `${AUTH_BASE_URL}/refresh-token`,
      method: 'post',
      params: { refreshToken },
    });

    Auth.setTokens(result.accessToken, result.refreshToken, result.expiresIn, Auth.getRememberMe());

    return result;
  },

  /** 登出接口 */
  async logout(): Promise<void> {
    Auth.clearTokens();
    return request<void>({
      url: `${AUTH_BASE_URL}/logout`,
      method: 'delete',
    });
  },

  /** 获取验证码 */
  async getCaptcha(): Promise<CaptchaResponse> {
    return request<CaptchaResponse>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: 'get',
    }); 
  },

  /** OAuth 登录接口 */
  async oauthLogin(data: OAuthLoginRequest): Promise<LoginResponse> {
    const formData = new FormData();
    formData.append('provider', data.provider);
    formData.append('code', data.code);
    if (data.state) formData.append('state', data.state);

    const result = await this._requestWithAutoRefresh(() =>
      request<LoginResponse>({
        url: `${AUTH_BASE_URL}/oauth/login`,
        method: 'post',
        data: formData,
      })
    );

    Auth.setTokens(result.accessToken, result.refreshToken, result.expiresIn, true);

    return result;
  },

  /** 获取 OAuth 配置 */
  async getOAuthConfig(): Promise<any> {
    return request({
      url: `${AUTH_BASE_URL}/oauth/config`,
      method: 'get',
    });
  },
};

export default AuthAPI;
