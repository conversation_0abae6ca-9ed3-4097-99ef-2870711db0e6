import request from "@/utils/request";
import { SessionQueryParam, SessionPageResopnse, SessionDetailVO, SessionStatisticVO, SessionMessagePageResponce } from "@/types/session";
import { storedMessageQuery, StoredMessageVO } from "@/types/storedMessage";

const SESSION_BASE_URL = "/api/vi/session";

const SessionAPI = {
    /**
     * 分页查询会话列表
     */
    async getSessionPage(params: SessionQueryParam): Promise<SessionPageResopnse> {
        return request<SessionPageResopnse>({
            url: `${SESSION_BASE_URL}/page`,
            method: 'GET',
            params
        });
    },

    /**
     * 查询会话详情信息
     */
    async getSessionById(sessionId: string): Promise<SessionDetailVO> {
        return request<SessionDetailVO>({
            url: SESSION_BASE_URL,
            method: 'GET',
            params: { sessionId }
        })
    },

    /**
     * 获取会话统计信息
     */
    async getSessionStatsticInfo(): Promise<SessionStatisticVO> {
        return request<SessionStatisticVO>({
            url: `${SESSION_BASE_URL}/statistic`,
            method: 'GET'
        })
    },

    /**
     * 查询会话历史记录
     */
    async getSessionMessage(params: storedMessageQuery): Promise<SessionMessagePageResponce> {
        return request<SessionMessagePageResponce>({
            url: `${SESSION_BASE_URL}/message`,
            method: 'GET',
            params
        })
    }
}

export default SessionAPI;