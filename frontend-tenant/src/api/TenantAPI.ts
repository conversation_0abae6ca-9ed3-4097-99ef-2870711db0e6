import request from '../utils/request';
import { TenantInfoDTO, UpdateTenantInfoRequest } from '../types/tenant';

const TENANT_BASE_URL = '/api/v1/tenant';

const TenantAPI = {
  /**
   * 获取当前租户信息
   */
  async getCurrentTenantInfo(): Promise<TenantInfoDTO> {
    return request<TenantInfoDTO>({
      url: `${TENANT_BASE_URL}/info`,
      method: 'GET',
    });
  },

  /**
   * 更新租户信息
   */
  async updateTenantInfo(data: UpdateTenantInfoRequest): Promise<string> {
    return request<string>({
      url: `${TENANT_BASE_URL}/info`,
      method: 'PUT',
      data,
    });
  },
};

export default TenantAPI;