import request from '@/utils/request';
import type {
  AgentOptionsPageQuery,
  AgentOptionsPageVO,
  AgentOptionsDetailVO,
  AgentOptionsForm,
  AgentOptionsStatVO,
  PageResult
} from '@/types/agentOptions';

const AGENT_OPTIONS_API = '/api/v1/agent-options';

/**
 * 智能体选项 API 服务类
 */
class AgentOptionsAPI {

  /**
   * 获取智能体选项分页列表
   */
  static async getAgentOptionsPage(params: AgentOptionsPageQuery): Promise<PageResult<AgentOptionsPageVO>> {
    return request({
      url: AGENT_OPTIONS_API + '/page',
      method: 'GET',
      params
    });
  }

  /**
   * 获取智能体选项详情
   */
  static async getAgentOptionsDetail(id: string): Promise<AgentOptionsDetailVO> {
    return request({
      url: AGENT_OPTIONS_API + `/${id}`,
      method: 'GET'
    });
  }

  /**
   * 创建智能体选项
   */
  static async createAgentOptions(data: AgentOptionsForm): Promise<string> {
    return request({
      url: AGENT_OPTIONS_API,
      method: 'POST',
      data
    });
  }

  /**
   * 更新智能体选项
   */
  static async updateAgentOptions(data: AgentOptionsForm): Promise<void> {
    if (!data.id) {
      throw new Error('智能体选项ID不能为空');
    }
    return request({
      url: `${AGENT_OPTIONS_API}/${data.id}`,
      method: 'PUT',
      data
    });
  }

  /**
   * 删除智能体选项
   */
  static async deleteAgentOptions(id: string): Promise<void> {
    return request({
      url: AGENT_OPTIONS_API + `/${id}`,
      method: 'DELETE'
    });
  }

  /**
   * 设置归档状态
   */
  static async setArchiveStatus(id: string, isArchived: boolean): Promise<void> {
    return request({
      url: `${AGENT_OPTIONS_API}/${id}/archive?isArchived=${isArchived}`,
      method: 'PUT'
    });
  }

  /**
   * 获取智能体选项统计
   */
  static async getAgentOptionsStats(): Promise<AgentOptionsStatVO> {
    return request({
      url: AGENT_OPTIONS_API + '/statistics',
      method: 'GET'
    });
  }

  /**
   * 获取所有智能体选项（用于下拉选择）
   */
  static async getAllAgentOptions(): Promise<AgentOptionsPageVO[]> {
    return request({
      url: AGENT_OPTIONS_API + '/all',
      method: 'GET'
    });
  }
}

export default AgentOptionsAPI;