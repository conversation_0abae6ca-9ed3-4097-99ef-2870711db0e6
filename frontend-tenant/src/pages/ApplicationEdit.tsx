import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import ApplicationAPI from "@/api/ApplicationAPI";
import AgentOptionsAPI from "@/api/AgentOptionsAPI";
import type { ApplicationDetailVO, ApplicationRequest } from "@/types/application";
import type { AgentOptionsPageVO } from "@/types/agentOptions";
import {
  ArrowLeft,
  Save,
  Trash2,
  <PERSON>tings,
  AlertTriangle,
  Shield,
  Database,
  Key,
  Palette,
  Upload,
  MessageSquare,
  Plus,
  X,
  Bot,
  ChevronDown
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";


const transformToFormData = (app: ApplicationDetailVO): ApplicationRequest => {
  return {
    displayName: app.displayName || app.name || "",
    description: app.description || "",
    key: app.key || "",
    settings: app.settings || {
      autoReply: true,
      maxConcurrentSessions: 100,
      sessionTimeout: 30,
      enableLogging: true,
      enableAnalytics: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60
    },
    security: app.security || {
      requireAuth: false,
      allowedOrigins: [],
      ipWhitelist: [],
      enableCors: true
    },
    ui: app.ui || {
      icon: null,
      signOutUrl: null,
      theme: "default",
      aiAvatar: null,
      userAvatar: null
    },
    recommendedStartPrompts: app.recommendedStartPrompts || [],
    selectedAgentId: app.agentOptionId || app.selectedAgentId || ""
  };
};

// 将推荐提示词转换为 starterPrompts 格式
const transformStarterPrompts = (prompts: string[] | null) => {
  if (!prompts || !Array.isArray(prompts)) return [];
  return prompts.map((prompt, index) => ({
    id: (index + 1).toString(),
    title: `提示词 ${index + 1}`,
    content: prompt
  }));
};

export default function ApplicationEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [app, setApp] = useState<ApplicationDetailVO | null>(null);
  const [availableAgents, setAvailableAgents] = useState<AgentOptionsPageVO[]>([]);
  const [formData, setFormData] = useState<ApplicationRequest>({
    displayName: "",
    description: "",
    key: "",
    settings: {
      autoReply: true,
      maxConcurrentSessions: 100,
      sessionTimeout: 30,
      enableLogging: true,
      enableAnalytics: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60
    },
    security: {
      requireAuth: false,
      allowedOrigins: [],
      ipWhitelist: [],
      enableCors: true
    },
    ui: {
      icon: null,
      signOutUrl: null,
      theme: "default",
      aiAvatar: null,
      userAvatar: null
    },
    recommendedStartPrompts: [],
    selectedAgentId: ""
  });

  const [activeTab, setActiveTab] = useState("basic");

  // 加载应用数据
  useEffect(() => {
    const loadData = async () => {
      if (!id) {
        navigate('/applications');
        return;
      }

      try {
        setLoading(true);
        const [appData, agentsData] = await Promise.all([
          ApplicationAPI.getApplicationDetail(id),
          AgentOptionsAPI.getAllAgentOptions()
        ]);

        setApp(appData);
        setAvailableAgents(agentsData);
        setFormData(transformToFormData(appData));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '加载应用数据失败';
        toast({ title: "错误", description: errorMessage, variant: "destructive" });
        navigate('/applications');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, navigate, toast]);

  // 转换 starterPrompts 用于显示
  const starterPrompts = transformStarterPrompts(formData.recommendedStartPrompts);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载应用数据中...</p>
        </div>
      </div>
    );
  }

  if (!app) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">应用不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的应用信息</p>
          <Link to="/applications">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回应用列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleSave = async () => {
    if (!id) return;
    
    try {
      setSaving(true);
      const updateData = { 
        ...formData, 
        id,
        agentOptionId: formData.selectedAgentId || null
      };
      delete updateData.selectedAgentId;
      
      await ApplicationAPI.updateApplication(updateData);
      toast({
        title: "保存成功",
        description: "应用配置已更新",
      });
      navigate(`/applications/${id}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '保存应用失败';
      toast({ title: "错误", description: errorMessage, variant: "destructive" });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await ApplicationAPI.deleteApplication(id);
      toast({
        title: "删除成功",
        description: "应用已被删除",
        variant: "destructive"
      });
      navigate("/applications");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除应用失败';
      toast({ title: "错误", description: errorMessage, variant: "destructive" });
    }
  };

  // 处理表单字段变更
  const handleFieldChange = (field: keyof ApplicationRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理嵌套字段变更
  const handleNestedFieldChange = (section: keyof ApplicationRequest, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section] as any,
        [field]: value
      }
    }));
  };

  // 处理推荐提示词变更
  const handleStarterPromptsChange = (prompts: string[]) => {
    setFormData(prev => ({
      ...prev,
      recommendedStartPrompts: prompts
    }));
  };

  // 添加推荐提示词
  const addStarterPrompt = () => {
    const newPrompts = [...(formData.recommendedStartPrompts || []), ""];
    handleStarterPromptsChange(newPrompts);
  };

  // 删除推荐提示词
  const removeStarterPrompt = (index: number) => {
    const newPrompts = (formData.recommendedStartPrompts || []).filter((_, i) => i !== index);
    handleStarterPromptsChange(newPrompts);
  };

  // 更新推荐提示词内容
  const updateStarterPrompt = (index: number, content: string) => {
    const newPrompts = [...(formData.recommendedStartPrompts || [])];
    newPrompts[index] = content;
    handleStarterPromptsChange(newPrompts);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to={`/applications/${id}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">编辑应用</h1>
            <p className="text-muted-foreground mt-1">修改应用配置和设置</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={app.status === "运行中" ? "default" : "secondary"}
            className={app.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            {app.status}
          </Badge>
          <Button onClick={handleSave} disabled={saving} className="bg-gradient-primary hover:opacity-90">
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            {saving ? '保存中...' : '保存更改'}
          </Button>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1">
        <Button
          variant={activeTab === "basic" ? "default" : "ghost"}
          onClick={() => setActiveTab("basic")}
          className="h-9"
        >
          基本信息
        </Button>
        <Button
          variant={activeTab === "settings" ? "default" : "ghost"}
          onClick={() => setActiveTab("settings")}
          className="h-9"
        >
          应用设置
        </Button>
        <Button
          variant={activeTab === "security" ? "default" : "ghost"}
          onClick={() => setActiveTab("security")}
          className="h-9"
        >
          安全配置
        </Button>
        <Button
          variant={activeTab === "ui" ? "default" : "ghost"}
          onClick={() => setActiveTab("ui")}
          className="h-9"
        >
          界面配置
        </Button>
        <Button
          variant={activeTab === "prompts" ? "default" : "ghost"}
          onClick={() => setActiveTab("prompts")}
          className="h-9"
        >
          推荐提示词
        </Button>
        <Button
          variant={activeTab === "agent" ? "default" : "ghost"}
          onClick={() => setActiveTab("agent")}
          className="h-9"
        >
          智能体配置
        </Button>
        <Button
          variant={activeTab === "danger" ? "default" : "ghost"}
          onClick={() => setActiveTab("danger")}
          className="h-9 text-destructive"
        >
          危险操作
        </Button>
      </div>

      {/* 基本信息 */}
      {activeTab === "basic" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">基本信息</CardTitle>
            <CardDescription>应用的基本配置信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="app-name">应用名称</Label>
                <Input
                  id="app-name"
                  value={formData.displayName}
                  onChange={(e) => handleFieldChange('displayName', e.target.value)}
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-desc">应用描述</Label>
                <Textarea
                  id="app-desc"
                  value={formData.description}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  className="bg-muted border-border min-h-[100px]"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-key">应用Key</Label>
                <Input
                  id="app-key"
                  value={formData.key}
                  onChange={(e) => handleFieldChange('key', e.target.value)}
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用的唯一标识符
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-id">应用ID</Label>
                <Input
                  id="app-id"
                  value={app.id}
                  disabled
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用ID创建后不可修改
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="created-at">创建时间</Label>
                <Input
                  id="created-at"
                  value={app.createdAt}
                  disabled
                  className="bg-muted border-border"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 应用设置 */}
      {activeTab === "settings" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">应用设置</CardTitle>
            </div>
            <CardDescription>配置应用的功能和性能参数</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>自动回复</Label>
                  <p className="text-xs text-muted-foreground">
                    启用智能体自动回复功能
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.autoReply}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, autoReply: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用日志记录</Label>
                  <p className="text-xs text-muted-foreground">
                    记录所有对话和操作日志
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.enableLogging}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, enableLogging: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用数据分析</Label>
                  <p className="text-xs text-muted-foreground">
                    收集和分析使用数据
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.enableAnalytics}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, enableAnalytics: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="max-sessions">最大并发会话数</Label>
                  <Input
                    id="max-sessions"
                    type="number"
                    value={formData.settings.maxConcurrentSessions}
                    onChange={(e) => setFormData({
                      ...formData, 
                      settings: {...formData.settings, maxConcurrentSessions: parseInt(e.target.value)}
                    })}
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="session-timeout">会话超时时间（分钟）</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    value={formData.settings.sessionTimeout}
                    onChange={(e) => setFormData({
                      ...formData, 
                      settings: {...formData.settings, sessionTimeout: parseInt(e.target.value)}
                    })}
                    className="bg-muted border-border"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 安全配置 */}
      {activeTab === "security" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">安全配置</CardTitle>
            </div>
            <CardDescription>配置应用的安全和访问控制</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用身份验证</Label>
                  <p className="text-xs text-muted-foreground">
                    要求用户登录后才能访问
                  </p>
                </div>
                <Switch 
                  checked={formData.security.requireAuth}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      security: {...formData.security, requireAuth: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用CORS</Label>
                  <p className="text-xs text-muted-foreground">
                    允许跨域请求
                  </p>
                </div>
                <Switch 
                  checked={formData.security.enableCors}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      security: {...formData.security, enableCors: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="grid gap-2">
                <Label htmlFor="allowed-origins">允许的域名</Label>
                <Textarea
                  id="allowed-origins"
                  value={formData.security.allowedOrigins?.join('\n')}
                  onChange={(e) => setFormData({
                    ...formData, 
                    security: {...formData.security, allowedOrigins: e.target.value.split('\n').filter(s => s.trim())}
                  })}
                  placeholder="每行一个域名，例如：&#10;https://company.com&#10;https://support.company.com"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ip-whitelist">IP白名单</Label>
                <Textarea
                  id="ip-whitelist"
                  value={formData.security.ipWhitelist?.join('\n') || ''}
                  onChange={(e) => setFormData({
                    ...formData, 
                    security: {...formData.security, ipWhitelist: e.target.value.split('\n').filter(s => s.trim())}
                  })}
                  placeholder="每行一个IP地址或CIDR，例如：&#10;***********/24&#10;********"
                  className="bg-muted border-border"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 界面配置 */}
      {activeTab === "ui" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Palette className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">界面配置</CardTitle>
            </div>
            <CardDescription>配置应用的界面外观和用户体验</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6">
              <div className="grid gap-2">
                <Label htmlFor="app-icon">应用程序图标</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="app-icon"
                    value={formData.ui.icon || ""}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, icon: e.target.value || null}
                    })}
                    placeholder="图标URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  支持PNG、JPG、SVG格式，建议尺寸64x64px
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="signout-url">应用程序退出URL</Label>
                <Input
                  id="signout-url"
                  value={formData.ui.signOutUrl || ""}
                  onChange={(e) => setFormData({
                    ...formData, 
                    ui: {...formData.ui, signOutUrl: e.target.value || null}
                  })}
                  placeholder="https://example.com/signout"
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  用户退出应用后跳转的URL地址
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="theme">应用程序的主题</Label>
                <Input
                  id="theme"
                  value={formData.ui.theme}
                  onChange={(e) => setFormData({
                    ...formData, 
                    ui: {...formData.ui, theme: e.target.value}
                  })}
                  placeholder="default, dark, light 等"
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用的视觉主题设置
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="ai-avatar">应用程序AI头像</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="ai-avatar"
                    value={formData.ui.aiAvatar || ""}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, aiAvatar: e.target.value || null}
                    })}
                    placeholder="AI头像URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  AI智能体的头像图片
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="user-avatar">应用程序用户头像</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="user-avatar"
                    value={formData.ui.userAvatar || ""}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, userAvatar: e.target.value || null}
                    })}
                    placeholder="用户头像URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  用户的默认头像图片
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 推荐提示词 */}
      {activeTab === "prompts" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">推荐提示词</CardTitle>
            </div>
            <CardDescription>配置用户初次访问时显示的推荐问题</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {starterPrompts.map((prompt, index) => (
                <Card key={prompt.id} className="bg-muted/50 border-border">
                  <CardContent className="pt-3 pb-3">
                    <div className="grid gap-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">提示词 {index + 1}</Label>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStarterPrompt(index)}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="grid gap-1.5">
                        <Label htmlFor={`prompt-title-${index}`} className="text-xs">标题</Label>
                        <Input
                          id={`prompt-title-${index}`}
                          value={prompt.title}
                          onChange={(e) => {
                            // 标题是显示用的，不需要更新到 recommendedStartPrompts
                          }}
                          placeholder="输入提示词标题"
                          className="bg-background border-border h-8"
                          disabled
                        />
                      </div>
                      <div className="grid gap-1.5">
                        <Label htmlFor={`prompt-content-${index}`} className="text-xs">内容</Label>
                        <Textarea
                          id={`prompt-content-${index}`}
                          value={prompt.content}
                          onChange={(e) => updateStarterPrompt(index, e.target.value)}
                          placeholder="输入提示词内容"
                          className="bg-background border-border min-h-[60px] text-sm"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Button
                variant="outline"
                onClick={addStarterPrompt}
                className="w-full border-dashed border-border hover:border-primary h-9"
              >
                <Plus className="mr-2 h-3 w-3" />
                添加新的提示词
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 智能体配置 */}
      {activeTab === "agent" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">智能体配置</CardTitle>
            </div>
            <CardDescription>选择应用关联的智能体</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="agent-select">选择智能体</Label>
                <Select
                  value={formData.selectedAgentId}
                  onValueChange={(value) => setFormData({ ...formData, selectedAgentId: value })}
                >
                  <SelectTrigger className="bg-muted border-border">
                    <SelectValue placeholder="请选择一个智能体" />
                  </SelectTrigger>
                  <SelectContent className="bg-card border-border shadow-lg z-50">
                    {availableAgents.map((agent) => {
                      const getStatusText = (isArchived: boolean) => {
                        return isArchived ? "已停止" : "运行中";
                      };

                      const statusText = getStatusText(agent.isArchived);

                      return (
                        <SelectItem key={agent.id} value={agent.id} className="hover:bg-muted">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                              <Bot className="w-4 h-4 text-primary-foreground" />
                            </div>
                            <div className="flex flex-col">
                              <span className="font-medium text-foreground">{agent.name}</span>
                              <span className="text-xs text-muted-foreground">{agent.description}</span>
                            </div>
                            <div className="ml-auto">
                              <Badge
                                variant={!agent.isArchived ? "default" : "secondary"}
                                className={!agent.isArchived ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                              >
                                {statusText}
                              </Badge>
                            </div>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  选择的智能体将处理此应用的所有对话请求
                </p>
              </div>

              {formData.selectedAgentId && (
                <Card className="bg-muted/50 border-border">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Bot className="h-4 w-4 text-primary" />
                        <Label className="text-sm font-medium">当前选择的智能体</Label>
                      </div>
                      {(() => {
                        const selectedAgent = availableAgents.find(agent => agent.id === formData.selectedAgentId);
                        if (!selectedAgent) return null;

                        const statusText = selectedAgent.isArchived ? "已停止" : "运行中";

                        return (
                          <div className="grid gap-2">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-foreground">{selectedAgent.name}</p>
                                <p className="text-sm text-muted-foreground">{selectedAgent.description}</p>
                              </div>
                              <Badge
                                variant={!selectedAgent.isArchived ? "default" : "secondary"}
                                className={!selectedAgent.isArchived ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                              >
                                {statusText}
                              </Badge>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 危险操作 */}
      {activeTab === "danger" && (
        <Card className="bg-card border-border shadow-card border-destructive/50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <CardTitle className="text-foreground">危险操作</CardTitle>
            </div>
            <CardDescription>
              这些操作将对应用产生不可逆的影响，请谨慎操作
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">删除应用</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  删除应用将同时删除所有相关的智能体、会话记录和配置。此操作不可撤销。
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除应用
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-card border-border">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-foreground">确认删除应用</AlertDialogTitle>
                      <AlertDialogDescription>
                        您即将删除应用 "{app.name}"。此操作将：
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          <li>删除所有智能体配置</li>
                          <li>删除所有会话记录</li>
                          <li>删除所有相关数据</li>
                        </ul>
                        <br />
                        <strong>此操作不可撤销，请确认是否继续？</strong>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}