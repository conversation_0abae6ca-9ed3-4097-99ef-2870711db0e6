import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ArrowLeft,
  Search, 
  MoreHorizontal, 
  Eye, 
  MessageSquare,
  Calendar,
  Clock,
  User,
  Bot,
  TrendingUp
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON>rollA<PERSON> } from "@/components/ui/scroll-area";

// 模拟用户数据
const userData = {
  "user_001": {
    id: "user_001",
    clientId: "用户001",
    nickname: "张先生",
    status: "活跃",
    firstSessionAt: "2024-01-15 09:30:00",
    lastSessionAt: "2024-01-20 14:35:00",
    totalSessions: 8,
    totalMessages: 45,
    favoriteAgent: "通用客服助手",
    mostUsedApp: "客服机器人"
  }
};

// 模拟会话历史数据
const sessionHistory = [
  {
    id: "session_001",
    application: "客服机器人",
    agent: "通用客服助手",
    startTime: "2024-01-20 14:30:00",
    endTime: "2024-01-20 14:35:00",
    duration: "5分钟",
    messageCount: 12,
    status: "已结束"
  },
  {
    id: "session_002",
    application: "客服机器人",
    agent: "通用客服助手",
    startTime: "2024-01-19 16:20:00",
    endTime: "2024-01-19 16:25:00",
    duration: "5分钟",
    messageCount: 8,
    status: "已结束"
  },
  {
    id: "session_003",
    application: "销售助手",
    agent: "销售顾问",
    startTime: "2024-01-18 10:15:00",
    endTime: "2024-01-18 10:20:00",
    duration: "5分钟",
    messageCount: 6,
    status: "已结束"
  },
  {
    id: "session_004",
    application: "技术支持",
    agent: "技术诊断专家",
    startTime: "2024-01-17 15:45:00",
    endTime: "2024-01-17 15:52:00",
    duration: "7分钟",
    messageCount: 15,
    status: "已结束"
  },
  {
    id: "session_005",
    application: "客服机器人",
    agent: "通用客服助手",
    startTime: "2024-01-16 11:30:00",
    endTime: "2024-01-16 11:33:00",
    duration: "3分钟",
    messageCount: 4,
    status: "已结束"
  }
];

// 模拟消息数据
const sampleMessages = [
  { category: "User", content: "你好，我想咨询一下产品价格", timestamp: "14:30:15" },
  { category: "Assistant", content: "您好！很高兴为您服务。请问您想了解哪款产品的价格呢？", timestamp: "14:30:18" },
  { category: "User", content: "我想了解企业版的价格", timestamp: "14:30:45" },
  { category: "Assistant", content: "企业版目前的价格是每月999元，包含以下功能：\n- 无限制API调用\n- 专属客服支持\n- 定制化配置\n- 数据备份服务", timestamp: "14:30:50" },
];

export default function UserSessionHistory() {
  const { userId } = useParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedApp, setSelectedApp] = useState("");
  const [selectedSession, setSelectedSession] = useState<typeof sessionHistory[0] | null>(null);

  const user = userData[userId as keyof typeof userData];

  const filteredSessions = sessionHistory.filter(session => {
    const matchesSearch = session.application.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.agent.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesApp = !selectedApp || selectedApp === "all" || session.application === selectedApp;
    
    return matchesSearch && matchesApp;
  });

  const applications = ["客服机器人", "销售助手", "技术支持"];

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">用户不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的用户信息</p>
          <Link to="/session-users">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回用户列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和返回按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/session-users">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">会话历史</h1>
              <p className="text-muted-foreground mt-1">{user.nickname}（{user.clientId}）的完整会话记录</p>
            </div>
          </div>
        </div>
      </div>

      {/* 用户信息概览 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{user.totalSessions}</div>
            <p className="text-xs text-muted-foreground">
              累计会话
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总消息数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{user.totalMessages}</div>
            <p className="text-xs text-muted-foreground">
              发送消息
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              偏好智能体
            </CardTitle>
            <Bot className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-foreground">{user.favoriteAgent}</div>
            <p className="text-xs text-muted-foreground">
              最常使用
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              用户状态
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <Badge 
              variant={user.status === "活跃" ? "default" : user.status === "一般" ? "secondary" : "outline"}
              className={
                user.status === "活跃" ? "bg-green-500/20 text-green-500 border-green-500/30" : 
                user.status === "一般" ? "bg-yellow-500/20 text-yellow-600 border-yellow-500/30" :
                "bg-gray-500/20 text-gray-500 border-gray-500/30"
              }
            >
              {user.status}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              当前状态
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 会话历史列表 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-foreground">会话历史</CardTitle>
              <CardDescription>
                查看用户与AI智能体的历史对话记录
              </CardDescription>
            </div>
          </div>
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索应用或智能体..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
            <Select value={selectedApp} onValueChange={setSelectedApp}>
              <SelectTrigger className="w-40 bg-muted border-border">
                <SelectValue placeholder="应用" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部应用</SelectItem>
                {applications.map((app) => (
                  <SelectItem key={app} value={app}>{app}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">应用/智能体</TableHead>
                <TableHead className="text-muted-foreground">开始时间</TableHead>
                <TableHead className="text-muted-foreground">结束时间</TableHead>
                <TableHead className="text-muted-foreground">时长</TableHead>
                <TableHead className="text-muted-foreground">消息数</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session.id} className="border-border hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-foreground">{session.application}</div>
                      <div className="text-sm text-muted-foreground flex items-center">
                        <Bot className="w-3 h-3 mr-1" />
                        {session.agent}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="mr-1 h-3 w-3" />
                      {session.startTime}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="mr-1 h-3 w-3" />
                      {session.endTime}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-foreground">
                      <Clock className="mr-1 h-3 w-3" />
                      {session.duration}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{session.messageCount} 条</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {session.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => setSelectedSession(session)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看对话
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 对话详情对话框 */}
      <Dialog open={!!selectedSession} onOpenChange={() => setSelectedSession(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-foreground">对话记录</DialogTitle>
            <DialogDescription>
              {selectedSession && `${selectedSession.application} - ${selectedSession.agent} | ${selectedSession.startTime}`}
            </DialogDescription>
          </DialogHeader>
          {selectedSession && (
            <div className="space-y-4">
              {/* 会话信息 */}
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
                    <Bot className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{selectedSession.application}</div>
                    <div className="text-sm text-muted-foreground">{selectedSession.agent}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">时长: {selectedSession.duration}</div>
                  <div className="text-sm text-muted-foreground">消息: {selectedSession.messageCount} 条</div>
                </div>
              </div>

              {/* 对话记录 */}
              <ScrollArea className="h-[400px] bg-muted/20 rounded-lg p-4">
                <div className="space-y-4">
                  {sampleMessages.map((message, index) => (
                    <div 
                      key={index} 
                      className={`flex ${message.category === 'User' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div 
                        className={`max-w-[75%] rounded-lg p-3 ${
                          message.category === 'User' 
                            ? 'bg-primary text-primary-foreground' 
                            : 'bg-card border border-border'
                        }`}
                      >
                        <div className="text-sm whitespace-pre-wrap">
                          {message.content}
                        </div>
                        <div className={`text-xs mt-1 ${
                          message.category === 'User' 
                            ? 'text-primary-foreground/70' 
                            : 'text-muted-foreground'
                        }`}>
                          {message.timestamp}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}