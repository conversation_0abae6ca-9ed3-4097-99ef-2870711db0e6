import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Calendar,
  User,
  Shield,
  Clock,
  UserCheck,
  UserX,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { UserDetailResponse } from "@/types/user";
import UserAPI from "@/api/UserAPI";
import { useToast } from "@/hooks/use-toast";

const genderMap = { 0: "保密", 1: "男", 2: "女" };
const statusMap = { Active: "正常", Inactive: "禁用" };
enum RoleEnum {
  ADMIN = "管理员",
  OPERATOR = "操作员",
  SUPPORT = "技术支持",
  VIEWER = "查看者",
  TESTER = "测试员",
}
enum UserStatusEnum {
  Active,
  Inactive,
}

export default function UserDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [user, setUser] = useState<UserDetailResponse>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const getSpecificUser = useCallback(async () => {
    if (!id) {
      toast({ title: "错误", description: "id不能为空" });
      return;
    }

    try {
      setLoading(true);
      const resp = await UserAPI.getUserById(id);
      setUser(resp);
    } catch (error) {
      toast({ title: "错误", description: "获取用户信息失败..." });
      setError("获取用户信息失败");
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    getSpecificUser();
  }, [getSpecificUser]);

  // 禁用/启用用户
  const handleChangeUserStatus = async (id: string, status: string) => {
    if (status === UserStatusEnum[0]) {
      status = UserStatusEnum[1];
    } else {
      status = UserStatusEnum[0];
    }
    try {
      await UserAPI.changeUserStatus(id, status);
      toast({ title: "成功", description: "操作成功..." });
      getSpecificUser();
    } catch (error) {
      toast({ title: "失败", description: "操作失败..." });
    }
  };

  if (!user || error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground">成员未找到</h2>
          <p className="text-muted-foreground mt-2">请检查链接是否正确</p>
          <Button onClick={() => navigate("/users")} className="mt-4">
            返回成员列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-foreground">加载中...</h2>
          </div>
        </div>
      ) : (
        <>
          {/* 页面标题和操作 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/users")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回列表
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                  成员详情
                </h1>
                <p className="text-muted-foreground mt-1">查看和管理成员信息</p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => navigate(`/users/${id}/edit`)}
              >
                <Edit className="mr-2 h-4 w-4" />
                编辑成员
              </Button>
              <Button
                disabled={user.uniqueUserKey === "admin"}
                variant={user.status === "Active" ? "destructive" : "default"}
                onClick={() => {
                  handleChangeUserStatus(user.id, user.status);
                }}
              >
                {user.status === "Active" ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    禁用成员
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    启用成员
                  </>
                )}
              </Button>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            {/* 基本信息 */}
            <div className="md:col-span-2 space-y-6">
              <Card className="bg-card border-border shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-foreground">
                    <User className="mr-2 h-5 w-5" />
                    基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center">
                      <span className="text-xl font-bold text-primary-foreground">
                        {user.displayName?.charAt(0) ||
                          user.uniqueUserKey.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground">
                        {user.displayName || user.uniqueUserKey}
                      </h3>
                      <p className="text-muted-foreground">
                        @{user.uniqueUserKey}
                      </p>
                      <Badge
                        variant={
                          user.status === "Active" ? "default" : "secondary"
                        }
                        className={
                          user.status === "Active"
                            ? "bg-green-500/20 text-green-500 border-green-500/30"
                            : "bg-red-500/20 text-red-500 border-red-500/30"
                        }
                      >
                        {statusMap[user.status as keyof typeof statusMap]}
                      </Badge>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Mail className="mr-2 h-4 w-4" />
                        邮箱地址
                      </div>
                      <p className="text-foreground">{user.email}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Phone className="mr-2 h-4 w-4" />
                        联系电话
                      </div>
                      <p className="text-foreground">
                        {user.phone || "未设置"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <User className="mr-2 h-4 w-4" />
                        性别
                      </div>
                      <p className="text-foreground">
                        {genderMap[user.gender as keyof typeof genderMap]}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="mr-2 h-4 w-4" />
                        部门职位
                      </div>
                      <p className="text-foreground">
                        {user.department} - {user.position}
                      </p>
                    </div>
                  </div>

                  {user.description && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">
                          描述信息
                        </div>
                        <p className="text-foreground">{user.description}</p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* 角色权限 */}
              <Card className="bg-card border-border shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-foreground">
                    <Shield className="mr-2 h-5 w-5" />
                    角色权限
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {user.roleKeyList &&
                      user.roleKeyList.length > 0 &&
                      user.roleKeyList.map((role, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="px-3 py-1"
                        >
                          {RoleEnum[role]}
                        </Badge>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 统计信息 */}
            <div className="space-y-6">
              <Card className="bg-card border-border shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-foreground">
                    <Clock className="mr-2 h-5 w-5" />
                    活动统计
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      登录次数
                    </div>
                    <div className="text-2xl font-bold text-foreground">
                      {user.loginCount}
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      最后登录
                    </div>
                    <div className="text-sm text-foreground">
                      {user.lastLoginAt}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-card border-border shadow-card">
                <CardHeader>
                  <CardTitle className="flex items-center text-foreground">
                    <Calendar className="mr-2 h-5 w-5" />
                    时间信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      创建时间
                    </div>
                    <div className="text-sm text-foreground">
                      {user.createdAt}
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      更新时间
                    </div>
                    <div className="text-sm text-foreground">
                      {user.updatedAt}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
