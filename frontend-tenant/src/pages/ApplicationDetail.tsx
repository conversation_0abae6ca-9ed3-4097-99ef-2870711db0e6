import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import {
  ArrowLeft,
  Edit,
  Activity,
  Bot,
  MessageSquare,
  Calendar,
  Users,
  TrendingUp,
  BarChart3,
  Settings,
  Shield,
  Palette,
  Eye,
  Info,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { ApplicationDetailVO } from '@/types/application';
import { AgentOptionsDetailVO } from '@/types/agentOptions';
import ApplicationAPI from '@/api/ApplicationAPI';
import AgentOptionsAPI from '@/api/AgentOptionsAPI';

export default function ApplicationDetail() {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('overview');
  const [app, setApp] = useState<ApplicationDetailVO | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [agentData, setAgentData] = useState<AgentOptionsDetailVO | null>(null);
  const [agentLoading, setAgentLoading] = useState(false);

  const transformStarterPrompts = (recommendedStartPrompts: string[] | null) => {
    if (!recommendedStartPrompts || !Array.isArray(recommendedStartPrompts)) return [];

    return recommendedStartPrompts.map((prompt, index) => ({
      id: String(index + 1),
      title: `提示词 ${index + 1}`,
      content: prompt
    }));
  };

  // 加载智能体数据
  const loadAgentData = async (agentOptionId: string) => {
    try {
      setAgentLoading(true);
      const agentDetail = await AgentOptionsAPI.getAgentOptionsDetail(agentOptionId);
      setAgentData(agentDetail);
    } catch (err: any) {
      console.error('Failed to load agent data:', err);
    } finally {
      setAgentLoading(false);
    }
  };

  // 加载应用详情数据
  const loadApplicationDetail = async () => {
    if (!id) {
      setError('应用ID不能为空');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await ApplicationAPI.getApplicationDetail(id);
      const appData = response;

      const transformedApp: ApplicationDetailVO = {
        ...appData,
        starterPrompts: transformStarterPrompts(appData.recommendedStartPrompts),
        // 模拟统计数据（后端暂无这些字段）
        agentCount: 0,
        sessionCount: 0,
        monthlyRequests: 0,
        successRate: 0,
        avgResponseTime: 0,
        totalUsers: 0,
        usage: {
          current: 0,
          limit: 100000,
          percentage: 0
        },
        agents: [],
        recentSessions: [],
        // 模拟配置数据（后端暂无这些字段）
        settings: {
          autoReply: false,
          maxConcurrentSessions: 100,
          sessionTimeout: 30,
          enableLogging: true,
          enableAnalytics: true,
          rateLimitEnabled: false,
          rateLimitRequests: 1000,
          rateLimitWindow: 60
        },
        security: {
          requireAuth: false,
          allowedOrigins: [],
          enableCors: true,
          ipWhitelist: []
        },
        ui: {
          icon: null,
          signOutUrl: null,
          theme: 'light',
          aiAvatar: null,
          userAvatar: null
        }
      };

      setApp(transformedApp);
      
      // 如果有agentOptionId，加载智能体数据
      if (appData.agentOptionId) {
        await loadAgentData(appData.agentOptionId);
      }
    } catch (err: any) {
      console.error('Failed to load application detail:', err);
      setError(err.message || '加载应用详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApplicationDetail();
  }, [id]);

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="text-muted-foreground">加载中...</span>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-foreground">加载失败</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
          <Button onClick={loadApplicationDetail} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  // 空状态
  if (!app) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-foreground">应用不存在</h3>
            <p className="text-muted-foreground">未找到指定的应用</p>
          </div>
          <Link to="/applications">
            <Button variant="outline">返回应用列表</Button>
          </Link>
        </div>
      </div>
    );
  }



  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/applications">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">{app.displayName}</h1>
            <p className="text-muted-foreground mt-1">{app.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge
            variant={app.status === "运行中" ? "default" : "secondary"}
            className={app.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            {app.status}
          </Badge>
          <Link to={`/applications/${id}/edit`}>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Edit className="mr-2 h-4 w-4" />
              编辑应用
            </Button>
          </Link>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              智能体数量
            </CardTitle>
            <Bot className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.agentCount}</div>
            <p className="text-xs text-muted-foreground">
              活跃智能体
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              月度请求
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.monthlyRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% 较上月
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              成功率
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.successRate}%</div>
            <p className="text-xs text-muted-foreground">
              响应成功率
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              响应时间
            </CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.avgResponseTime}</div>
            <p className="text-xs text-muted-foreground">
              平均响应时间
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1">
        <Button
          variant={activeTab === "overview" ? "default" : "ghost"}
          onClick={() => setActiveTab("overview")}
          className="h-9"
        >
          概览
        </Button>
        <Button
          variant={activeTab === "details" ? "default" : "ghost"}
          onClick={() => setActiveTab("details")}
          className="h-9"
        >
          详细信息
        </Button>
        <Button
          variant={activeTab === "agents" ? "default" : "ghost"}
          onClick={() => setActiveTab("agents")}
          className="h-9"
        >
          智能体
        </Button>
        <Button
          variant={activeTab === "sessions" ? "default" : "ghost"}
          onClick={() => setActiveTab("sessions")}
          className="h-9"
        >
          会话记录
        </Button>
        <Button
          variant={activeTab === "analytics" ? "default" : "ghost"}
          onClick={() => setActiveTab("analytics")}
          className="h-9"
        >
          数据分析
        </Button>
      </div>

      {/* 标签页内容 */}
      {activeTab === "overview" && (
        <div className="grid gap-6 lg:grid-cols-2">
          {/* 基本信息 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">应用ID</Label>
                  <p className="text-foreground font-mono">{app.id}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">应用Key</Label>
                  <p className="text-foreground font-mono">{app.key}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">创建时间</Label>
                  <p className="text-foreground">{app.createdAt}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">最后活动</Label>
                  <p className="text-foreground">{app.lastActivity}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">总用户数</Label>
                  <p className="text-foreground">{app.totalUsers}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">智能体数量</Label>
                  <p className="text-foreground">{app.agentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 使用量统计 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">本月使用量</CardTitle>
              <CardDescription>API请求使用情况</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">已使用</span>
                  <span className="text-sm font-medium">{app.usage.current.toLocaleString()} / {app.usage.limit.toLocaleString()}</span>
                </div>
                <Progress value={app.usage.percentage} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  已使用 {app.usage.percentage.toFixed(1)}% 的月度配额
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "details" && (
        <div className="grid gap-6">
          {/* 应用设置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">应用设置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">自动回复</Label>
                  <p className="text-foreground">{app.settings.autoReply ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">最大并发会话数</Label>
                  <p className="text-foreground">{app.settings.maxConcurrentSessions}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">会话超时时间</Label>
                  <p className="text-foreground">{app.settings.sessionTimeout} 分钟</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">日志记录</Label>
                  <p className="text-foreground">{app.settings.enableLogging ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">数据分析</Label>
                  <p className="text-foreground">{app.settings.enableAnalytics ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">频率限制</Label>
                  <p className="text-foreground">{app.settings.rateLimitEnabled ? `${app.settings.rateLimitRequests}次/${app.settings.rateLimitWindow}分钟` : "已禁用"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 安全配置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">安全配置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">身份验证</Label>
                  <p className="text-foreground">{app.security.requireAuth ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">CORS支持</Label>
                  <p className="text-foreground">{app.security.enableCors ? "已启用" : "已禁用"}</p>
                </div>
                <div className="col-span-2">
                  <Label className="text-sm text-muted-foreground">允许的域名</Label>
                  <div className="mt-1">
                    {app.security.allowedOrigins.map((origin, index) => (
                      <p key={index} className="text-foreground text-sm font-mono">{origin}</p>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 界面配置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Palette className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">界面配置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">应用图标</Label>
                  <p className="text-foreground">{app.ui.icon || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">退出URL</Label>
                  <p className="text-foreground">{app.ui.signOutUrl || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">主题</Label>
                  <p className="text-foreground">{app.ui.theme}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">AI头像</Label>
                  <p className="text-foreground">{app.ui.aiAvatar || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">用户头像</Label>
                  <p className="text-foreground">{app.ui.userAvatar || "未设置"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 推荐提示词 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">推荐提示词</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {app.starterPrompts && app.starterPrompts.length > 0 ? (
                app.starterPrompts.map((prompt, index) => (
                  <Card key={prompt.id} className="bg-muted/50 border-border">
                    <CardContent className="pt-3 pb-3">
                      <div className="space-y-2">
                        <div>
                          <Label className="text-sm font-medium text-foreground">{prompt.title}</Label>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">{prompt.content}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无推荐提示词</p>
                  <p className="text-sm text-muted-foreground mt-1">您可以在编辑应用时添加推荐提示词</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "agents" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">智能体列表</CardTitle>
            <CardDescription>该应用下的所有智能体</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow className="border-border">
                  <TableHead className="text-muted-foreground">智能体名称</TableHead>
                  <TableHead className="text-muted-foreground">状态</TableHead>
                  <TableHead className="text-muted-foreground">请求数</TableHead>
                  <TableHead className="text-muted-foreground text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {agentLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                        <span className="text-muted-foreground">加载智能体信息中...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : agentData ? (
                  <TableRow className="border-border">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                          <Bot className="w-4 h-4 text-primary-foreground" />
                        </div>
                        <span className="text-foreground">{agentData.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={!agentData.isArchived ? "default" : "secondary"}
                        className={!agentData.isArchived ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                      >
                        {!agentData.isArchived ? "运行中" : "已归档"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{agentData.performance?.totalRequests?.toLocaleString() || '0'}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <Link to={`/agent-options/${agentData.id}`}>
                        <Button variant="ghost" size="sm">
                          <Settings className="mr-2 h-4 w-4" />
                          配置
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      <div className="text-center space-y-2">
                        <Bot className="h-8 w-8 text-muted-foreground mx-auto" />
                        <p className="text-muted-foreground">暂无智能体数据</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {activeTab === "sessions" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">最近会话</CardTitle>
            <CardDescription>该应用的最近对话记录</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow className="border-border">
                  <TableHead className="text-muted-foreground">用户</TableHead>
                  <TableHead className="text-muted-foreground">智能体</TableHead>
                  <TableHead className="text-muted-foreground">消息数</TableHead>
                  <TableHead className="text-muted-foreground">状态</TableHead>
                  <TableHead className="text-muted-foreground">时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {app.recentSessions.map((session) => (
                  <TableRow key={session.id} className="border-border">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-gradient-primary rounded-full flex items-center justify-center">
                          <Users className="w-3 h-3 text-primary-foreground" />
                        </div>
                        <span className="text-foreground">{session.user}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{session.agent}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{session.messages} 条</span>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={session.status === "进行中" ? "default" : "secondary"}
                        className={session.status === "进行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                      >
                        {session.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">{session.time}</span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {activeTab === "analytics" && (
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">性能指标</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">成功率</span>
                  <span className="text-sm font-medium">{app.successRate}%</span>
                </div>
                <Progress value={app.successRate} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">响应速度</span>
                  <span className="text-sm font-medium">优秀</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">用户满意度</span>
                  <span className="text-sm font-medium">92%</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">使用趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">今日请求</span>
                  <span className="text-sm font-medium text-foreground">342</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">昨日请求</span>
                  <span className="text-sm font-medium text-foreground">298</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">本周平均</span>
                  <span className="text-sm font-medium text-foreground">315</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">增长率</span>
                  <span className="text-sm font-medium text-green-500">+14.8%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

function Label({ className, children }: { className?: string; children: React.ReactNode }) {
  return <div className={className}>{children}</div>;
}