import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Save, User, Shield, Settings, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { EditUserRequest } from "../types/user";
import UserAPI from "../api/UserAPI";

const availableRoles = [
  { id: "ADMIN", name: "管理员", description: "系统最高权限" },
  { id: "OPERATOR", name: "操作员", description: "基础操作权限" },
  { id: "SUPPORT", name: "技术支持", description: "技术支持权限" },
  { id: "VIEWER", name: "查看者", description: "只读权限" },
  { id: "TESTER", name: "测试员", description: "测试相关权限" },
];
const roleMap = new Map(availableRoles.map((role) => [role.id, role.name]));

export default function UserEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const DEFAULT_USER_FORM_DATA: EditUserRequest = {
    id: "",
    uniqueUserKey: "",
    displayName: "",
    gender: 0,
    status: "",
    email: "",
    phone: "",
    department: "",
    position: "",
    description: "",
    password: "",
    confirmPassword: "",
    roleKeyList: [],
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [userFormData, setUserFormData] = useState<EditUserRequest>(
    DEFAULT_USER_FORM_DATA
  );

  const getSpecificUser = useCallback(async () => {
    if (!id) {
      toast({
        title: "错误",
        description: "id不能为空",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const resp = await UserAPI.getUserById(id);
      setUserFormData((prev) => ({
        ...prev,
        id: resp.id,
        uniqueUserKey: resp.uniqueUserKey,
        displayName: resp.displayName ?? prev.displayName,
        gender: resp.gender ?? prev.gender,
        status: resp.status ?? prev.status,
        email: resp.email ?? prev.email,
        phone: resp.phone ?? prev.phone,
        department: resp.department ?? prev.department,
        position: resp.position ?? prev.position,
        description: resp.description ?? prev.description,
        roleKeyList: resp.roleKeyList ?? prev.roleKeyList,
      }));
    } catch (error) {
      toast({
        title: "错误",
        description: "获取用户信息失败...",
        variant: "destructive",
      });
      setError("获取用户信息失败");
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    getSpecificUser();
  }, [getSpecificUser]);

  const handleInputChange = (field: string, value: string) => {
    setUserFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleRoleToggle = (roleId: string, checked: boolean) => {
    setUserFormData((prev) => ({
      ...prev,
      roleKeyList: checked
        ? [...prev.roleKeyList, roleId]
        : prev.roleKeyList.filter((role) => role !== roleId),
    }));
  };

  const handleRemoveRole = (roleName: string) => {
    setUserFormData((prev) => ({
      ...prev,
      roleKeyList: prev.roleKeyList.filter((role) => role !== roleName),
    }));
  };

  const handleSubmit = async () => {
    // 验证必填字段
    if (!userFormData.uniqueUserKey || !userFormData.email) {
      toast({
        title: "保存失败",
        description: "请填写必填字段",
        variant: "destructive",
      });
      return;
    }

    // 验证密码
    if (
      userFormData.password &&
      userFormData.password !== userFormData.confirmPassword
    ) {
      toast({
        title: "保存失败",
        description: "两次输入的密码不一致",
        variant: "destructive",
      });
      return;
    }

    try {
      await UserAPI.editUser(userFormData);
      if (userFormData.password) {
        await UserAPI.resetUserPassword(userFormData.id, userFormData.password);
      }
    } catch (error) {
      toast({
        title: "保存失败",
        description: "成员信息更新失败...",
        variant: "destructive",
      });
    }

    toast({
      title: "保存成功",
      description: "成员信息已更新",
    });

    navigate(`/users/${id}`);
  };

  if (!userFormData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground">成员未找到</h2>
          <p className="text-muted-foreground mt-2">请检查链接是否正确</p>
          <Button onClick={() => navigate("/users")} className="mt-4">
            返回成员列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/users/${id}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回详情
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">
              编辑成员
            </h1>
            <p className="text-muted-foreground mt-1">修改成员基本信息和权限</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => navigate(`/users/${id}`)}>
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-gradient-primary hover:opacity-90"
          >
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* 基本信息 */}
        <div className="md:col-span-2 space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <User className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名 *</Label>
                  <Input
                    id="username"
                    value={userFormData.uniqueUserKey}
                    onChange={(e) =>
                      handleInputChange("uniqueUserKey", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="输入用户名"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nickname">显示昵称</Label>
                  <Input
                    id="nickname"
                    value={userFormData.displayName}
                    onChange={(e) =>
                      handleInputChange("nickname", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="输入显示昵称"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱地址 *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={userFormData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="bg-muted border-border"
                    placeholder="输入邮箱地址"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">联系电话</Label>
                  <Input
                    id="phone"
                    value={userFormData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="bg-muted border-border"
                    placeholder="输入联系电话"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gender">性别</Label>
                  <Select
                    value={userFormData.gender.toString()}
                    onValueChange={(value) =>
                      handleInputChange("gender", value)
                    }
                  >
                    <SelectTrigger className="bg-muted border-border">
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      <SelectItem value="0">保密</SelectItem>
                      <SelectItem value="1">男</SelectItem>
                      <SelectItem value="2">女</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">账户状态</Label>
                  <Select
                    value={userFormData.status}
                    onValueChange={(value) =>
                      handleInputChange("status", value)
                    }
                  >
                    <SelectTrigger className="bg-muted border-border">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      <SelectItem value="Active">正常</SelectItem>
                      <SelectItem value="Inactive">禁用</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">所属部门</Label>
                  <Input
                    id="department"
                    value={userFormData.department}
                    onChange={(e) =>
                      handleInputChange("department", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="输入所属部门"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">职位</Label>
                  <Input
                    id="position"
                    value={userFormData.position}
                    onChange={(e) =>
                      handleInputChange("position", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="输入职位"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述信息</Label>
                <Textarea
                  id="description"
                  value={userFormData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  className="bg-muted border-border"
                  placeholder="输入描述信息"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* 密码设置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Settings className="mr-2 h-5 w-5" />
                密码设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                留空表示不修改密码
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="password">新密码</Label>
                  <Input
                    id="password"
                    type="password"
                    value={userFormData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="输入新密码"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">确认密码</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={userFormData.confirmPassword}
                    onChange={(e) =>
                      handleInputChange("confirmPassword", e.target.value)
                    }
                    className="bg-muted border-border"
                    placeholder="再次输入新密码"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 角色权限 */}
        <div className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Shield className="mr-2 h-5 w-5" />
                角色权限
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 当前角色 */}
              <div className="space-y-2">
                <Label>当前角色</Label>
                <div className="flex flex-wrap gap-2">
                  {!userFormData.roleKeyList ||
                  userFormData.roleKeyList.length === 0 ? (
                    <span className="text-sm text-muted-foreground">
                      暂无角色
                    </span>
                  ) : (
                    userFormData.roleKeyList.map((role, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="px-2 py-1"
                      >
                        {roleMap.get(role)}
                        <button
                          onClick={() => handleRemoveRole(role)}
                          className="ml-2 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))
                  )}
                </div>
              </div>

              <Separator />

              {/* 可选角色 */}
              <div className="space-y-3">
                <Label>分配角色</Label>
                {availableRoles.map((role) => (
                  <div key={role.id} className="flex items-start space-x-3">
                    <Checkbox
                      id={role.id}
                      checked={userFormData.roleKeyList.includes(role.id)}
                      onCheckedChange={(checked) =>
                        handleRoleToggle(role.id, checked as boolean)
                      }
                    />
                    <div className="space-y-1">
                      <Label
                        htmlFor={role.id}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {role.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {role.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
