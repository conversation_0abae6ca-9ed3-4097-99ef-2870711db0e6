import { useState, useRef, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ArrowLeft,
  Send,
  Bot,
  User,
  Settings,
  Trash2,
  Download,
  Zap,
  Clock,
  MessageSquare
} from "lucide-react";

// 模拟智能体数据
const agents = [
  {
    id: "1",
    name: "通用客服助手",
    description: "处理常见客户咨询和问题解答",
    key: "general-support",
    model: "gpt-4",
    application: "客服机器人",
    status: "运行中"
  },
  {
    id: "2",
    name: "销售顾问",
    description: "产品推荐和销售流程指导",
    key: "sales-advisor",
    model: "gpt-3.5-turbo",
    application: "销售助手",
    status: "运行中"
  },
  {
    id: "3",
    name: "技术诊断专家",
    description: "技术问题诊断和解决方案",
    key: "tech-expert",
    model: "gpt-4",
    application: "技术支持",
    status: "维护中"
  }
];

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

export default function AgentTest() {
  const { id } = useParams();
  const [selectedAgentId, setSelectedAgentId] = useState(id || "1");
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const selectedAgent = agents.find(agent => agent.id === selectedAgentId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    // 模拟AI回复
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: `您好！我是${selectedAgent?.name}。我收到了您的消息："${userMessage.content}"。这是一个模拟回复，展示了智能体的对话能力。实际应用中，这里会调用相应的AI模型进行真实对话。`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const clearConversation = () => {
    setMessages([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/agents">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">智能体测试对话</h1>
            <p className="text-muted-foreground mt-1">测试智能体的对话能力和响应效果</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={clearConversation}>
            <Trash2 className="mr-2 h-4 w-4" />
            清空对话
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出对话
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
        {/* 左侧：智能体选择和配置 */}
        <div className="col-span-3 space-y-4">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-lg text-foreground">选择智能体</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select value={selectedAgentId} onValueChange={setSelectedAgentId}>
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      <div className="flex items-center space-x-2">
                        <Bot className="h-4 w-4" />
                        <span>{agent.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedAgent && (
                <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">状态</span>
                    <Badge 
                      variant={selectedAgent.status === "运行中" ? "default" : "secondary"}
                      className={selectedAgent.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                    >
                      {selectedAgent.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">模型</span>
                    <span className="text-sm font-mono text-foreground">{selectedAgent.model}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">应用</span>
                    <span className="text-sm text-foreground">{selectedAgent.application}</span>
                  </div>
                  <div className="pt-2">
                    <p className="text-sm text-muted-foreground">{selectedAgent.description}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 测试统计 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-lg text-foreground">本次会话</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-muted-foreground">消息数</span>
                </div>
                <span className="text-sm font-semibold text-foreground">{messages.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-muted-foreground">平均响应</span>
                </div>
                <span className="text-sm font-semibold text-foreground">1.2s</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-muted-foreground">状态</span>
                </div>
                <Badge variant="outline" className="border-green-500/30 text-green-500">
                  在线
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧：对话界面 */}
        <div className="col-span-9">
          <Card className="bg-card border-border shadow-card h-full flex flex-col">
            <CardHeader className="border-b border-border">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                    <Bot className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <CardTitle className="text-lg text-foreground">
                      {selectedAgent?.name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {selectedAgent?.description}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>

            {/* 消息区域 */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-12">
                    <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">开始对话</h3>
                    <p className="text-muted-foreground">
                      向智能体发送消息来测试其响应能力
                    </p>
                  </div>
                )}

                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted border border-border"
                      }`}
                    >
                      <div className="flex items-start space-x-2">
                        {message.role === "assistant" && (
                          <Bot className="w-5 h-5 mt-0.5 text-primary flex-shrink-0" />
                        )}
                        {message.role === "user" && (
                          <User className="w-5 h-5 mt-0.5 text-primary-foreground flex-shrink-0" />
                        )}
                        <div className="flex-1">
                          <p className={`text-sm ${
                            message.role === "user" ? "text-primary-foreground" : "text-foreground"
                          }`}>
                            {message.content}
                          </p>
                          <p className={`text-xs mt-1 ${
                            message.role === "user" 
                              ? "text-primary-foreground/70" 
                              : "text-muted-foreground"
                          }`}>
                            {message.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex justify-start">
                    <div className="max-w-[80%] rounded-lg px-4 py-3 bg-muted border border-border">
                      <div className="flex items-center space-x-2">
                        <Bot className="w-5 h-5 text-primary" />
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            <Separator />

            {/* 输入区域 */}
            <div className="p-4">
              <div className="flex space-x-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="输入消息测试智能体..."
                  className="flex-1 bg-muted border-border"
                  onKeyPress={handleKeyPress}
                  disabled={isLoading || selectedAgent?.status !== "运行中"}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isLoading || selectedAgent?.status !== "运行中"}
                  className="bg-gradient-primary hover:opacity-90"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              {selectedAgent?.status !== "运行中" && (
                <p className="text-sm text-muted-foreground mt-2">
                  当前智能体处于维护状态，无法进行对话测试
                </p>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}