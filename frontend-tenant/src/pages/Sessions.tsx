import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  MoreHorizontal,
  Eye,
  MessageSquare,
  Calendar,
  Clock,
  User,
  Bot,
  Filter,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import useDebounce from "@/hooks/useDebounce";
import {
  SessionPageResopnse,
  SessionPageVO,
  SessionQueryParam,
  SessionStatisticVO,
} from "@/types/session";
import { useToast } from "@/hooks/use-toast";
import SessionAPI from "@/api/SessionAPI";
import { ApplicationSelectedVO } from "@/types/application";
import ApplicationAPI from "@/api/ApplicationAPI";
import { StoredMessageVO, storedMessageQuery } from "@/types/storedMessage";
import { DateUtils } from "@/utils/dateUtil";

export default function Sessions() {
  const [selectedStatus, setSelectedStatus] = useState("");

  // new code
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const searchDebouncedValue = useDebounce(searchTerm, 500);
  const [sessionPage, setSessionPage] = useState<Array<SessionPageVO>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [pageNum, setPageNum] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [selectedSession, setSelectedSession] = useState<SessionPageVO | null>(
    null
  );
  const [applicationList, setApplicationList] = useState<
    Array<ApplicationSelectedVO>
  >([]);
  const [selectedApp, setSelectedApp] = useState("");
  const [statisticInfo, setStatisticInfo] = useState<SessionStatisticVO>({
    totalSessionCount: 0,
    processingCount: 0,
    avgDuration: 0,
    totalMessageCount: 0,
  });

  // 对话记录相关状态
  const [sessionMessages, setSessionMessages] = useState<
    Array<StoredMessageVO>
  >([]);
  const [messagesLoading, setMessagesLoading] = useState<boolean>(false);
  const [messagesPageNum, setMessagesPageNum] = useState<number>(1);
  const [messagesPageSize] = useState<number>(20);
  const [messagesTotal, setMessagesTotal] = useState<number>(0);
  const [hasMoreMessages, setHasMoreMessages] = useState<boolean>(false);

  const getApplicationSelectedList = async () => {
    const resp = await ApplicationAPI.getApplicationSelectedList();
    setApplicationList(resp);
  };

  const loadStatisticInfo = async () => {
    const resp = await SessionAPI.getSessionStatsticInfo();
    setStatisticInfo(resp);
  };

  // 加载会话对话记录
  const loadSessionMessages = useCallback(
    async (sessionId: string, pageNum: number = 1) => {
      try {
        setMessagesLoading(true);
        const queryParam: storedMessageQuery = {
          sessionId,
          pageNum,
          pageSize: messagesPageSize,
        };

        const response = await SessionAPI.getSessionMessage(queryParam);
        const newMessages = response.list || [];
        const total = response.total || 0;

        if (pageNum === 1) {
          setSessionMessages(newMessages);
        } else {
          setSessionMessages((prev) => [...prev, ...newMessages]);
        }

        setMessagesTotal(total);
        setMessagesPageNum(pageNum);

        const currentTotal =
          pageNum === 1
            ? newMessages.length
            : sessionMessages.length + newMessages.length;
        setHasMoreMessages(currentTotal < total);
      } catch (error) {
        toast({
          title: "错误",
          description: "获取对话记录失败",
          variant: "destructive",
        });
      } finally {
        setMessagesLoading(false);
      }
    },
    [messagesPageSize, sessionMessages.length, toast]
  );

  // 滚动事件
  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;

      if (
        isNearBottom &&
        selectedSession &&
        hasMoreMessages &&
        !messagesLoading
      ) {
        loadSessionMessages(selectedSession.id, messagesPageNum + 1);
      }
    },
    [
      selectedSession,
      hasMoreMessages,
      messagesLoading,
      messagesPageNum,
      loadSessionMessages,
    ]
  );

  const loadSessionData = useCallback(
    async (params?: Partial<SessionQueryParam>) => {
      try {
        setLoading(true);
        const queryParam: SessionQueryParam = {
          pageNum: params?.pageNum || pageNum,
          pageSize: params?.pageSize || pageSize,
          searchKeyword: searchDebouncedValue || undefined,
          applicationId:
            selectedApp && selectedApp !== "all" ? selectedApp : undefined,
        };

        const response: SessionPageResopnse = await SessionAPI.getSessionPage(
          queryParam
        );
        setSessionPage(response.list);
        setTotal(response.total);
      } catch (error) {
        toast({ title: "错误", description: "获取会话列表失败" });
      } finally {
        setLoading(false);
      }
    },
    [pageNum, pageSize, searchDebouncedValue, selectedApp, toast]
  );

  // 初始化数据
  useEffect(() => {
    loadSessionData();
    getApplicationSelectedList();
    loadStatisticInfo();
  }, [loadSessionData]);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            会话管理
          </h1>
          <p className="text-muted-foreground mt-1">
            监控和管理用户与AI的对话会话
          </p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {statisticInfo.totalSessionCount}
            </div>
            <p className="text-xs text-muted-foreground">今日新增</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              进行中
            </CardTitle>
            <Clock className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {statisticInfo.processingCount}
            </div>
            <p className="text-xs text-muted-foreground">实时对话</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              平均时长
            </CardTitle>
            <Clock className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {statisticInfo.avgDuration}分钟
            </div>
            <p className="text-xs text-muted-foreground">平均会话时长</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总消息数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {statisticInfo.totalMessageCount}
            </div>
            <p className="text-xs text-muted-foreground">累计消息</p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索用户ID、应用或智能体..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
            {/* <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32 bg-muted border-border">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部状态</SelectItem>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select> */}
            <Select value={selectedApp} onValueChange={setSelectedApp}>
              <SelectTrigger className="w-40 bg-muted border-border">
                <SelectValue placeholder="应用" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部应用</SelectItem>
                {applicationList.map((app) => (
                  <SelectItem key={app.id} value={app.id}>
                    {app.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">
                  用户信息
                </TableHead>
                <TableHead className="text-muted-foreground">
                  应用/智能体
                </TableHead>
                {/* <TableHead className="text-muted-foreground">状态</TableHead> */}
                <TableHead className="text-muted-foreground">消息数</TableHead>
                <TableHead className="text-muted-foreground">时长</TableHead>
                <TableHead className="text-muted-foreground">
                  开始时间
                </TableHead>
                <TableHead className="text-muted-foreground text-right">
                  操作
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-muted-foreground">加载中...</p>
                  </TableCell>
                </TableRow>
              ) : !sessionPage || sessionPage.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <p className="text-muted-foreground">暂无数据</p>
                  </TableCell>
                </TableRow>
              ) : (
                sessionPage.map((session) => (
                  <TableRow
                    key={session.id}
                    className="border-border hover:bg-muted/50"
                  >
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-primary-foreground" />
                        </div>
                        <div>
                          <div className="font-semibold text-foreground">
                            {session.uniqueUserKey}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {session.userIp}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium text-foreground">
                          {session.applicationName}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <Bot className="w-3 h-3 mr-1" />
                          {session.agentName}
                        </div>
                      </div>
                    </TableCell>
                    {/* <TableCell>
                    <Badge
                      variant={
                        session.status === "进行中" ? "default" : "secondary"
                      }
                      className={
                        session.status === "进行中"
                          ? "bg-green-500/20 text-green-500 border-green-500/30"
                          : ""
                      }
                    >
                      {session.status}
                    </Badge>
                  </TableCell> */}
                    <TableCell>
                      <span className="text-foreground">
                        {session.messageCount} 条
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">
                        {session.duration}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-muted-foreground">
                        <Calendar className="mr-1 h-3 w-3" />
                        {session.startTime.split(" ")[1]}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="bg-popover border-border"
                        >
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedSession(session);
                              // 重置对话记录状态并加载新的对话记录
                              setSessionMessages([]);
                              setMessagesPageNum(1);
                              setMessagesTotal(0);
                              setHasMoreMessages(false);
                              loadSessionMessages(session.id, 1);
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* 分页 */}
          {!loading && total > 0 && (
            <div className="flex items-center justify-between mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => pageNum > 1 && setPageNum(pageNum - 1)}
                      className={
                        pageNum <= 1
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>

                  {/* 页码 */}
                  {Array.from(
                    { length: Math.ceil(total / pageSize) },
                    (_, i) => i + 1
                  )
                    .filter((page) => {
                      const totalPages = Math.ceil(total / pageSize);
                      if (totalPages <= 7) return true;
                      if (page === 1 || page === totalPages) return true;
                      if (Math.abs(page - pageNum) <= 2) return true;
                      return false;
                    })
                    .map((page, index, array) => {
                      const showEllipsis =
                        index > 0 && page - array[index - 1] > 1;
                      return (
                        <div key={page} style={{ display: "contents" }}>
                          {showEllipsis && (
                            <PaginationItem key={`ellipsis-${page}`}>
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          )}
                          <PaginationItem key={`page-${page}`}>
                            <PaginationLink
                              onClick={() => setPageNum(page)}
                              isActive={pageNum === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        </div>
                      );
                    })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        pageNum < Math.ceil(total / pageSize) &&
                        setPageNum(pageNum + 1)
                      }
                      className={
                        pageNum >= Math.ceil(total / pageSize)
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
              <div className="text-sm text-muted-foreground">
                共 {total} 条记录，{pageNum}/{pageSize} 条/页
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 会话详情对话框 */}
      <Dialog
        open={!!selectedSession}
        onOpenChange={() => {
          setSelectedSession(null);
          // 清理对话记录状态
          setSessionMessages([]);
          setMessagesPageNum(1);
          setMessagesTotal(0);
          setHasMoreMessages(false);
        }}
      >
        <DialogContent className="max-w-4xl max-h-[80vh] bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-foreground">会话详情</DialogTitle>
            <DialogDescription>查看完整的对话记录和会话信息</DialogDescription>
          </DialogHeader>
          {selectedSession && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 会话信息 */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-foreground">会话信息</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">用户ID:</span>
                      <span className="text-foreground">
                        {selectedSession.uniqueUserKey}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">应用:</span>
                      <span className="text-foreground">
                        {selectedSession.applicationName}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">智能体:</span>
                      <span className="text-foreground">
                        {selectedSession.agentName}
                      </span>
                    </div>
                    {/* <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">状态:</span>
                      <Badge
                        variant={
                          selectedSession.status === "进行中"
                            ? "default"
                            : "secondary"
                        }
                        className="text-xs"
                      >
                        {selectedSession.status}
                      </Badge>
                    </div> */}
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">消息数:</span>
                      <span className="text-foreground">
                        {selectedSession.messageCount}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">时长:</span>
                      <span className="text-foreground">
                        {selectedSession.duration}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">IP地址:</span>
                      <span className="text-foreground">
                        {selectedSession.userIp}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">浏览器:</span>
                      <span className="text-foreground">
                        {selectedSession.userAgent}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 对话记录 */}
              <div className="lg:col-span-2">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-foreground">对话记录</h4>
                  {messagesTotal > 0 && (
                    <span className="text-sm text-muted-foreground">
                      共 {messagesTotal} 条消息
                    </span>
                  )}
                </div>
                <div className="h-96 bg-muted/30 rounded-lg overflow-hidden">
                  <div
                    className="h-full overflow-y-auto p-4"
                    onScroll={handleScroll}
                  >
                    {messagesLoading && sessionMessages.length === 0 ? (
                      <div className="flex items-center justify-center h-32">
                        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                        <span className="ml-2 text-muted-foreground">
                          加载对话记录中...
                        </span>
                      </div>
                    ) : sessionMessages.length === 0 ? (
                      <div className="flex items-center justify-center h-32">
                        <span className="text-muted-foreground">
                          暂无对话记录
                        </span>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {sessionMessages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${
                              message.category === "User"
                                ? "justify-end"
                                : "justify-start"
                            }`}
                          >
                            <div
                              className={`max-w-[75%] rounded-lg p-3 shadow-sm ${
                                message.category === "User"
                                  ? "bg-primary text-primary-foreground"
                                  : "bg-card border border-border"
                              }`}
                            >
                              <div className="text-sm leading-relaxed break-words">
                                {/* 处理换行和格式化显示 */}
                                {message.content
                                  .split("\n")
                                  .map((line, index) => (
                                    <div
                                      key={index}
                                      className={index > 0 ? "mt-2" : ""}
                                    >
                                      {line || "\u00A0"}{" "}
                                      {/* 空行显示为不间断空格 */}
                                    </div>
                                  ))}
                              </div>
                              <div
                                className={`text-xs mt-2 ${
                                  message.category === "User"
                                    ? "text-primary-foreground/70"
                                    : "text-muted-foreground"
                                }`}
                              >
                                {DateUtils.formatSmartDateTime(
                                  message.createdAt
                                )}
                              </div>
                            </div>
                          </div>
                        ))}

                        {/* 加载状态指示器 */}
                        {messagesLoading && sessionMessages.length > 0 && (
                          <div className="flex justify-center py-4">
                            <div className="flex items-center text-muted-foreground">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              <span className="text-sm">加载更多消息中...</span>
                            </div>
                          </div>
                        )}

                        {/* 没有更多消息的提示 */}
                        {!hasMoreMessages &&
                          sessionMessages.length > 0 &&
                          messagesTotal > messagesPageSize && (
                            <div className="flex justify-center py-4">
                              <span className="text-xs text-muted-foreground">
                                已显示全部 {messagesTotal} 条消息
                              </span>
                            </div>
                          )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
