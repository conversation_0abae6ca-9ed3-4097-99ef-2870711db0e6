import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Settings as SettingsIcon,
  Save,
  Building2,
  Key,
  Bell,
  Shield,
  Monitor,
  Upload,
  Image as ImageIcon,
  X
} from "lucide-react";
import TenantAPI from "@/api/TenantAPI";
import { TenantInfoDTO, UpdateTenantInfoRequest } from "@/types/tenant";
import { useToast } from "@/hooks/use-toast";

export default function Settings() {
  const { toast } = useToast();
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  
  // 租户信息状态
  const [tenantInfo, setTenantInfo] = useState<TenantInfoDTO | null>(null);
  const [tenantForm, setTenantForm] = useState<UpdateTenantInfoRequest>({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  
  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  const removeLogo = () => {
    setLogoPreview(null);
  };

  // 获取租户信息
  const fetchTenantInfo = async () => {
    setLoading(true);
    try {
      const data = await TenantAPI.getCurrentTenantInfo();
      setTenantInfo(data);
      setTenantForm({
        name: data.name || '',
        description: data.description || ''
      });
    } catch (error) {
      console.error('获取租户信息失败:', error);
      toast({
        title: "获取租户信息失败",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 更新租户信息
  const handleUpdateTenantInfo = async () => {
    if (!tenantForm.name.trim()) {
      toast({
        title: "验证失败",
        description: "租户名称不能为空",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      const updateRequest: UpdateTenantInfoRequest = {
        name: tenantForm.name.trim(),
        description: tenantForm.description.trim()
      };

      await TenantAPI.updateTenantInfo(updateRequest);
      toast({
        title: "更新成功",
        description: "租户信息已更新",
      });
      // 重新获取租户信息
      await fetchTenantInfo();
    } catch (error) {
      console.error('更新租户信息失败:', error);
      toast({
        title: "更新失败",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };
  const handleTenantFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTenantForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  useEffect(() => {
    fetchTenantInfo();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">系统设置</h1>
          <p className="text-muted-foreground mt-1">配置您的租户系统参数和偏好设置</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* 品牌设置 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <ImageIcon className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">品牌设置</CardTitle>
            </div>
            <CardDescription>
              管理您的公司LOGO和品牌标识
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="company-logo">公司LOGO</Label>
              <div className="flex items-start space-x-4">
                {/* LOGO 预览区域 */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 border-2 border-dashed border-border rounded-lg flex items-center justify-center bg-muted/30 overflow-hidden">
                    {logoPreview ? (
                      <div className="relative w-full h-full">
                        <img 
                          src={logoPreview} 
                          alt="Company Logo Preview" 
                          className="w-full h-full object-contain"
                        />
                        <button
                          onClick={removeLogo}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center hover:bg-destructive/80 transition-colors"
                          type="button"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <ImageIcon className="w-8 h-8 text-muted-foreground mx-auto mb-1" />
                        <p className="text-xs text-muted-foreground">暂无LOGO</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 上传控制区域 */}
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <Label
                      htmlFor="logo-upload"
                      className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      上传LOGO
                    </Label>
                    {logoPreview && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={removeLogo}
                        type="button"
                      >
                        移除
                      </Button>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>• 支持 PNG、JPG、SVG 格式</p>
                    <p>• 建议尺寸：200x200 像素</p>
                    <p>• 文件大小不超过 2MB</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="brand-name">品牌名称</Label>
              <Input
                id="brand-name"
                defaultValue="AI Hub"
                className="bg-muted border-border"
                placeholder="输入品牌名称"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="brand-slogan">品牌标语</Label>
              <Input
                id="brand-slogan"
                defaultValue="让AI赋能每一个创意"
                className="bg-muted border-border"
                placeholder="输入品牌标语"
              />
            </div>
            
            <Button className="bg-gradient-primary hover:opacity-90">
              <Save className="mr-2 h-4 w-4" />
              保存品牌设置
            </Button>
          </CardContent>
        </Card>

        {/* 租户信息 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Building2 className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">租户信息</CardTitle>
            </div>
            <CardDescription>
              管理您的租户基本信息和标识
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="tenant-name">租户名称</Label>
              <Input
                id="tenant-name"
                name="name"
                value={tenantForm.name}
                onChange={handleTenantFormChange}
                className="bg-muted border-border"
                disabled={loading || saving}
                placeholder="请输入租户名称"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tenant-key">租户标识</Label>
              <Input
                id="tenant-key"
                value={tenantInfo?.key || ''}
                className="bg-muted border-border"
                disabled
              />
              <p className="text-xs text-muted-foreground">
                租户标识创建后不可修改
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tenant-desc">租户描述</Label>
              <Textarea
                  id="tenant-desc"
                  name="description"
                  value={tenantForm.description}
                  onChange={handleTenantFormChange}
                  className="bg-muted border-border"
                  disabled={loading || saving}
                  placeholder="请输入租户描述"
                  rows={3}
                />
            </div>
            <Button 
              className="bg-gradient-primary hover:opacity-90"
              onClick={handleUpdateTenantInfo}
              disabled={loading || saving}
            >
              <Save className="mr-2 h-4 w-4" />
              {saving ? '保存中...' : '保存租户信息'}
            </Button>
          </CardContent>
        </Card>

        {/* API 配置 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Key className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">API 配置</CardTitle>
            </div>
            <CardDescription>
              管理您的API密钥和访问配置
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="api-key">API 密钥</Label>
              <div className="flex space-x-2">
                <Input
                  id="api-key"
                  type="password"
                  defaultValue="sk-***************"
                  className="bg-muted border-border"
                  disabled
                />
                <Button variant="outline" size="sm">
                  重新生成
                </Button>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="api-endpoint">API 端点</Label>
              <Input
                id="api-endpoint"
                defaultValue="https://api.aihub.com/v1"
                className="bg-muted border-border"
                disabled
              />
              <p className="text-xs text-muted-foreground">
                API端点由系统管理，无法修改
              </p>
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="api-debug">启用调试模式</Label>
              <Switch id="api-debug" />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="api-rate-limit">启用频率限制</Label>
              <Switch id="api-rate-limit" defaultChecked />
            </div>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Save className="mr-2 h-4 w-4" />
              保存API配置
            </Button>
          </CardContent>
        </Card>


        {/* 通知设置 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">通知设置</CardTitle>
            </div>
            <CardDescription>
              配置系统通知和警报偏好
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>系统错误通知</Label>
                <p className="text-xs text-muted-foreground">
                  当系统发生错误时发送通知
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>会话异常警报</Label>
                <p className="text-xs text-muted-foreground">
                  当会话出现异常时发送警报
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>资源使用提醒</Label>
                <p className="text-xs text-muted-foreground">
                  当资源使用接近限制时提醒
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>每日报告</Label>
                <p className="text-xs text-muted-foreground">
                  发送每日使用统计报告
                </p>
              </div>
              <Switch />
            </div>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Save className="mr-2 h-4 w-4" />
              保存通知设置
            </Button>
          </CardContent>
        </Card>

        {/* 安全设置 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">安全设置</CardTitle>
            </div>
            <CardDescription>
              配置安全策略和访问控制
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>启用双因素认证</Label>
                <p className="text-xs text-muted-foreground">
                  为管理员账户启用2FA
                </p>
              </div>
              <Switch />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>IP 白名单</Label>
                <p className="text-xs text-muted-foreground">
                  限制特定IP地址访问
                </p>
              </div>
              <Switch />
            </div>
            <Separator />
            <div className="grid gap-2">
              <Label htmlFor="session-timeout">会话超时时间（分钟）</Label>
              <Input
                id="session-timeout"
                type="number"
                defaultValue="30"
                className="bg-muted border-border"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password-policy">密码复杂度要求</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" defaultChecked />
                  <span className="text-sm text-muted-foreground">至少8个字符</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" defaultChecked />
                  <span className="text-sm text-muted-foreground">包含大小写字母</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" defaultChecked />
                  <span className="text-sm text-muted-foreground">包含数字和特殊字符</span>
                </div>
              </div>
            </div>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Save className="mr-2 h-4 w-4" />
              保存安全设置
            </Button>
          </CardContent>
        </Card>

      </div>
    </div>
  );
}