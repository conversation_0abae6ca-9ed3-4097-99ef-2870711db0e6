import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  Users, 
  Bot, 
  MessageSquare, 
  ArrowUpRight,
  Zap
} from "lucide-react";
import { cn } from "@/lib/utils";

// 模拟数据
const stats = [
  {
    title: "总应用数",
    value: "12",
    change: "+2",
    icon: Zap,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
  },
  {
    title: "活跃智能体",
    value: "24",
    change: "+5",
    icon: Bot,
    color: "text-green-500",
    bgColor: "bg-green-500/10",
  },
  {
    title: "本月对话",
    value: "8,423",
    change: "+1.2k",
    icon: MessageSquare,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
  },
  {
    title: "在线用户",
    value: "156",
    change: "+23",
    icon: Users,
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
  },
];

const recentApps = [
  { name: "客服机器人", status: "启用", requests: 1234, lastActive: "2分钟前" },
  { name: "销售助手", status: "启用", requests: 892, lastActive: "5分钟前" },
  { name: "技术支持", status: "禁用", requests: 445, lastActive: "1小时前" },
  { name: "产品咨询", status: "启用", requests: 678, lastActive: "刚刚" },
];

const recentSessions = [
  { user: "用户001", app: "客服机器人", messages: 12, duration: "5分钟", status: "进行中" },
  { user: "用户002", app: "销售助手", messages: 8, duration: "3分钟", status: "已结束" },
  { user: "用户003", app: "技术支持", messages: 15, duration: "8分钟", status: "进行中" },
  { user: "用户004", app: "客服机器人", messages: 6, duration: "2分钟", status: "已结束" },
];

export default function Dashboard() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-foreground">仪表板</h1>
        <p className="text-muted-foreground mt-1">欢迎回来，查看您的AI平台运营概况</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title} className="bg-card border-border shadow-card hover:shadow-glow transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={cn("w-8 h-8 rounded-lg flex items-center justify-center", stat.bgColor)}>
                <stat.icon className={cn("h-4 w-4", stat.color)} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                <span className="text-green-500">{stat.change}</span>
                <span className="ml-1">较上月</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* 最近应用状态 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-foreground">应用状态</CardTitle>
                <CardDescription>各应用的实时运行状态</CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                查看全部
                <ArrowUpRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentApps.map((app) => (
              <div key={app.name} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                  <div>
                    <p className="font-medium text-foreground">{app.name}</p>
                    <p className="text-xs text-muted-foreground">{app.requests} 次请求</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge 
                    variant={app.status === "启用" ? "default" : "secondary"}
                    className={`mb-1 ${app.status === "启用" ? "bg-green-500 text-white" : "bg-red-500 text-white"}`}
                  >
                    {app.status}
                  </Badge>
                  <p className="text-xs text-muted-foreground">{app.lastActive}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 最近会话 */}
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-foreground">最近会话</CardTitle>
                <CardDescription>用户与AI的最新交互记录</CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                查看全部
                <ArrowUpRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentSessions.map((session, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-primary flex items-center justify-center">
                    <span className="text-xs font-semibold text-primary-foreground">
                      {session.user.slice(-2)}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-foreground">{session.user}</p>
                    <p className="text-xs text-muted-foreground">{session.app}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2">
                    <Badge variant={session.status === "进行中" ? "default" : "secondary"}>
                      {session.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {session.messages}条消息 · {session.duration}
                  </p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* 使用量分析 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <CardTitle className="text-foreground">本月使用量分析</CardTitle>
          <CardDescription>各项资源的使用情况和配额</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">API调用</span>
                <span className="text-sm font-medium">8,423 / 10,000</span>
              </div>
              <Progress value={84} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">存储空间</span>
                <span className="text-sm font-medium">2.1GB / 5GB</span>
              </div>
              <Progress value={42} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">并发连接</span>
                <span className="text-sm font-medium">156 / 500</span>
              </div>
              <Progress value={31} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}