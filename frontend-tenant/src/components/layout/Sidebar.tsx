import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { 
  LayoutDashboard, 
  Zap, 
  Bot, 
  MessageSquare, 
  Users, 
  UserCheck,
  Settings,
  ChevronLeft,
  Building2,
  MessageCircle
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const menuItems = [
  {
    title: "仪表板",
    icon: LayoutDashboard,
    path: "/",
  },
  {
    title: "系统助手",
    icon: MessageCircle,
    path: "/assistant",
  },
  {
    title: "应用管理",
    icon: Zap,
    path: "/applications",
  },
  {
    title: "智能体",
    icon: Bot,
    path: "/agents",
  },
  {
    title: "会话管理",
    icon: MessageSquare,
    path: "/sessions",
  },
  {
    title: "会话用户",
    icon: UserCheck,
    path: "/session-users",
  },
  {
    title: "登录账号",
    icon: Users,
    path: "/users",
  },
  {
    title: "系统设置",
    icon: Settings,
    path: "/settings",
  },
];

export function Sidebar({ collapsed, onCollapse }: SidebarProps) {
  const location = useLocation();

  return (
    <aside
      className={cn(
        "relative flex flex-col bg-card border-r border-border transition-all duration-300 ease-in-out h-screen",
        collapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Building2 className="w-5 h-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-semibold text-foreground">Aihub</span>
              <span className="text-xs text-muted-foreground">租户管理</span>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onCollapse(!collapsed)}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className={cn("h-4 w-4 transition-transform", collapsed && "rotate-180")} />
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-2 space-y-1">
        {menuItems.map((item) => {
          const isActive = location.pathname === item.path;
          return (
            <NavLink key={item.path} to={item.path}>
              <Button
                variant={isActive ? "default" : "ghost"}
                className={cn(
                  "w-full justify-start h-10 px-3",
                  collapsed && "px-0 justify-center",
                  isActive && "bg-primary text-primary-foreground shadow-glow"
                )}
              >
                <item.icon className={cn("h-4 w-4", !collapsed && "mr-3")} />
                {!collapsed && <span className="text-sm">{item.title}</span>}
              </Button>
            </NavLink>
          );
        })}
      </nav>
    </aside>
  );
}