/**
 * 智能体选项相关类型定义
 */

// 智能体选项类型枚举
export type AgentOptionsType = 'conversational' | 'other';

// 分页查询参数
export interface AgentOptionsPageQuery {
  pageNum: number;
  pageSize: number;
  keywords?: string;
  type?: AgentOptionsType;
  isArchived?: boolean;
}

// 智能体选项分页查询结果
export interface AgentOptionsPageVO {
  id: string;
  parentId?: string;
  name: string;
  description?: string;
  type: AgentOptionsType;
  parentName?: string;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  key: string;
  application?: string;
  sessionCount?: number;
  model?: string;
}

// 智能体选项详情
export interface AgentOptionsDetailVO {
  id: string;
  name: string;
  description?: string;
  type: AgentOptionsType;
  parentId?: string;
  parentName?: string;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  key: string;
  model: string;
  application: string;
  applicationId?: string;
  status?: string;
  sessionCount?: number;
  settings?: {
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    presencePenalty?: number;
    frequencyPenalty?: number;
    enableStreaming?: boolean;
    enableFunctionCalling?: boolean;
  };
  extra?: string; // 数据库连接字符串，格式如："Server=host; Port=port; Database=db; User=user; Password=pass"
  performance?: {
    avgResponseTime?: string;
    successRate?: number;
    totalRequests?: number;
    errorCount?: number;
  };
}

// 智能体选项表单
export interface AgentOptionsForm {
  id?: string;
  name: string;
  description?: string;
  key?: string;
  model?: string;
  application?: string;
  applicationId?: string;
  status?: string;
  type?: AgentOptionsType;
  parentId?: string;
  isArchived?: boolean;
  extra?: string; // 数据库连接字符串，格式如："Server=host; Port=port; Database=db; User=user; Password=pass"
  settings?: {
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    presencePenalty?: number;
    frequencyPenalty?: number;
    enableStreaming?: boolean;
    enableFunctionCalling?: boolean;
    systemPrompt?: string;
  };
}

// 智能体选项统计
export interface AgentOptionsStatVO {
  totalCount: number;
  activeCount: number;
}

// 分页结果
export interface PageResult<T> {
  records: T[];
  total: number;
}

// API响应结果
export interface ApiResult<T = any> {
  code: number;
  data: T;
  msg: string;
  success: boolean;
}