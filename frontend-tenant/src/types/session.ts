import { StoredMessageVO } from "./storedMessage";

export interface SessionQueryParam {
    pageNum: number;
    pageSize: number;
    searchKeyword?: string;
    applicationId?: string;
}

export interface SessionPageResopnse {
    list?: SessionPageVO[];
    total?: number;
}


export interface SessionPageVO {
    id: string;
    uniqueUserKey: string;
    userIp: string;
    userAgent: string,
    applicationName: string;
    agentName: string;
    duration: number;
    messageCount: number;
    startTime: string;
}

export interface SessionDetailVO {
    id: string;
    uniqueUserKey: string;
    userIp: string;
    userAgent: string;
    applicationName: string;
    agentName: string;
    duration: number;
    messageCount: number;
    startTime: string;
    messageList: Array<StoredMessageVO>
}

export interface SessionStatisticVO {
    totalSessionCount: number;
    processingCount: number;
    avgDuration: number;
    totalMessageCount: number;
}

export interface SessionMessagePageResponce {
    list: Array<StoredMessageVO>,
    total: number,
}
