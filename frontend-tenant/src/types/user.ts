// 分页查询参数
export interface UserQueryParam {
  pageNum: number;
  pageSize: number;
  keywords?: string;
}

export interface UserPageResponse {
  list: UserPageVo[];
  total: number;
}

export interface UserPageVo {
  id: string;
  uniqueUserKey: string;
  displayName?: string;
  gender: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  roleKey: string;
}

export interface CreateUserRequest {
  uniqueUserKey: string;
  displayName: string;
  password: string;
  gender: number;
}

export interface EditUserRequest {
  id: string,
  uniqueUserKey: string,
  displayName: string,
  gender: number,
  status: string,
  email: string,
  phone: string,
  department: string,
  position: string,
  description: string,
  password: string,
  confirmPassword: string,
  roleKeyList: Array<string>,
}


export interface UserDetailResponse {
  id: string,
  uniqueUserKey: string,
  displayName: string,
  gender: number,
  status: string,
  email: string,
  phone: string,
  department: string,
  position: string,
  description: string,
  loginCount: number,
  lastLoginAt: string,
  createdAt: string,
  updatedAt: string,
  roleKeyList: Array<string>,
}

export interface UserStatisticResponse {
  totalCount: number,
  activeCount: number,
  inactiveCount: number,
  adminCount: number,
}