export interface LoginRequest {
  username: string;
  password: string;
  captchaCode: string;
  captchaKey: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user?: {
    id: string;
    username: string;
    email: string;
    role: string;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface CaptchaResponse {
  captchaKey: string;  
  captchaBase64: string; 
}

export interface OAuthLoginRequest {
  provider: 'google' | 'github' | 'microsoft';
  code: string;
  state?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

export interface ApiError {
  message: string;
  code: number;
  details?: any;
}