export class DateUtils {
    /**
     * Date format 
     * current: HH:mm:ss，
     * else: yyyy-MM-dd HH:mm:ss
     * 
     */
    static formatSmartDateTime(dateStr: string): string {
        if (!dateStr) return '';

        const date = new Date(dateStr);
        const today = new Date();

        const isSameDay = date.toDateString() === today.toDateString();

        return isSameDay
            ? date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
            : date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
    }
}