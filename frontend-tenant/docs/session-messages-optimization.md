# 会话对话记录功能优化

## 优化内容

### 1. 分页数量优化
- **调整**: 从50条改为20条每页
- **原因**: 考虑到对话内容长度不可预知，20条能更好地平衡加载速度和用户体验
- **优势**: 
  - 减少单次加载时间
  - 避免长内容导致的页面卡顿
  - 提供更流畅的滚动体验

### 2. 内容格式显示优化
- **问题**: 原始内容换行和格式显示不正确
- **解决方案**:
  - 使用 `split('\n')` 处理换行符
  - 为每行添加适当的间距 (`mt-2`)
  - 空行显示为不间断空格 (`\u00A0`)
  - 添加 `break-words` 类处理长单词换行
  - 调整最大宽度为75%，提供更好的阅读体验

### 3. 加载方式优化
- **改进**: 从按钮点击改为滚动自动加载
- **实现**: 
  - 监听滚动事件
  - 距离底部50px时自动触发加载
  - 添加加载状态指示器
  - 显示"已显示全部消息"提示

### 4. 用户体验改进
- **时间显示**: 简化时间格式，只显示月日时分秒
- **加载状态**: 区分首次加载和追加加载的状态显示
- **视觉优化**: 添加阴影效果，调整间距和颜色
- **状态管理**: 添加 `hasMoreMessages` 状态精确控制加载逻辑

## 技术实现

### 状态管理
```typescript
const [messagesPageSize] = useState<number>(20); // 每页20条
const [hasMoreMessages, setHasMoreMessages] = useState<boolean>(false);
```

### 滚动监听
```typescript
const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
  const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
  const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;
  
  if (isNearBottom && selectedSession && hasMoreMessages && !messagesLoading) {
    loadSessionMessages(selectedSession.id, messagesPageNum + 1);
  }
}, [selectedSession, hasMoreMessages, messagesLoading, messagesPageNum, loadSessionMessages]);
```

### 内容格式化
```typescript
{message.content.split('\n').map((line, index) => (
  <div key={index} className={index > 0 ? 'mt-2' : ''}>
    {line || '\u00A0'}
  </div>
))}
```

## 使用说明

1. **查看对话记录**: 点击会话列表中的"查看详情"
2. **自动加载**: 滚动到接近底部时自动加载更多消息
3. **状态提示**: 显示加载状态和消息总数
4. **格式显示**: 正确显示换行和长文本内容

## 性能考虑

- 每页20条消息，平衡加载速度和内容量
- 使用 `useCallback` 优化滚动监听性能
- 精确的状态管理避免不必要的API调用
- 滚动触发阈值设置为50px，提供良好的响应性
