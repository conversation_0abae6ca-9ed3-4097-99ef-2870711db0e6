name: 测试环境部署前端(frontend-chat)
on:
  workflow_dispatch:
  push:
    branches: [ staging ]
    paths:
      - .github/workflows/frontend-chat-staging.yml
      - "frontend-chat/**"

jobs:
  deploy:
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash
        working-directory: frontend-chat

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Npm install
      run: npm install

    - name: Build project
      run: npm run build

    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        source: "frontend-chat/dist/"
        target: "/opt/aihub-staging/upload-frontend-chat"

    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        script: |
          cd /opt/aihub-staging
          rm -rf frontend-chat
          cp -r upload-frontend-chat/frontend-chat/dist .
          mv dist frontend-chat
          rm -rf upload-frontend-chat
          docker compose down frontend-chat && docker compose up -d frontend-chat