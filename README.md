# 探数者智能体SaaS平台

![Java](https://img.shields.io/badge/Java-17-orange.svg)
![.NET](https://img.shields.io/badge/.NET-9.0-purple.svg)
![React](https://img.shields.io/badge/React-18.3.1-blue.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)

一个基于多Agent架构的智能聊天平台，支持实时对话、数据库查询、专业工具调用等功能。

## ✨ 核心特性

- 🤖 **多代理架构**: 主代理协调多个专业子代理完成复杂任务
- 💬 **实时聊天**: 基于 SignalR 的实时双向通信
- 🗄️ **智能数据查询**: 自然语言转换为数据库查询
- 🛠️ **工具调用系统**: 可扩展的工具插件架构
- 🌐 **多模型支持**: 集成 OpenRouter API，支持多种 AI 模型
- 🎨 **现代化 UI**: 基于 shadcn/ui 的响应式界面设计
- 🔐 **SaaS 架构**: 支持多租户和会话管理

## 🏗️ 技术架构

### Java 后端技术栈
- **待启动**

### C# 后端技术栈
- **框架**: ASP.NET Core 9.0
- **数据库**: PostgreSQL + Entity Framework Core
- **实时通信**: SignalR
- **AI 集成**: OpenRouter API
- **依赖注入**: Scrutor

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI 库**: shadcn/ui + Tailwind CSS
- **状态管理**: TanStack Query
- **实时通信**: SignalR Client
- **路由**: React Router

### 架构模式

```
用户消息 → SignalR Hub → Agent Runner → 主代理 → 子代理 → 工具执行 → 返回结果
```

## 🚀 快速开始

### 环境要求

- **Java 17+**
- **.NET 9.0 SDK**
- **Node.js 18+**
- **PostgreSQL 12+**
- **OpenRouter API Key** (可选)

### 1. 克隆项目

```bash
git clone <repository-url>
cd aihub
```

### 2. 配置数据库

创建 PostgreSQL 数据库，并配置连接字符串：

```json
// backend-hubv2/src/Aihub.Server/appsettings.Development.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=aihub;Username=postgres;Password=your_password"
  }
}
```

### 3. 配置 AI 模型

```json
// backend-hubv2/src/Aihub.Server/appsettings.Development.json
{
  "Aihub": {
    "DefaultModel": "google/gemini-2.0-flash-001",
    "OpenRouterApiKey": "your_openrouter_api_key",
    "MaxToolCallLoopTimes": 10
  }
}
```

### 4. 启动后端服务

```bash
cd backend-hubv2
dotnet restore
dotnet run --project src/Aihub.Server
```

后端服务将在 `http://localhost:5000` 启动

### 5. 启动前端应用

```bash
cd frontend-chat
npm install
npm run dev
```

前端应用将在 `http://localhost:8080` 启动

## 📁 项目结构

```
aihub/
├── backend-hubv2/              # 后端 API 服务
│   └── src/
│       └── Aihub.Server/
│           ├── Agent/          # 多代理系统
│           │   ├── ChatAgents/ # 主聊天代理
│           │   └── SubAgents/  # 专业子代理
│           ├── Hubs/          # SignalR 通信中心
│           ├── EfCore/        # 数据库上下文
│           └── Llm/           # AI 模型集成
├── frontend-chat/             # 主聊天界面
│   └── src/
│       ├── components/        # React 组件
│       ├── hooks/            # 自定义 Hook
│       └── types/            # TypeScript 类型定义
└── frontend-tester/          # 测试环境
```

## 🤖 代理系统

### 主代理 (Primary Agent)
负责理解用户意图，协调各个子代理完成任务。

### 子代理类型

- **数据库代理 (DatabaseSubAgent)**: 处理数据查询和分析
- **手册指导代理 (ManualGuideSubAgent)**: 提供操作指导和帮助

### 自定义子代理

创建新的子代理只需：

1. 继承 `SubAgent` 基类
2. 实现 `GetToolTypes()` 方法
3. 添加 `SubAgentAttribute` 注解

```csharp
[SubAgent("custom_agent", 
    Key = "custom-key",
    Description = "自定义代理描述",
    DisplayName = "自定义代理")]
public class CustomSubAgent : SubAgent
{
    public override IEnumerable<Type> GetToolTypes()
    {
        return new[] { typeof(CustomTool) };
    }
}
```

## 🛠️ 工具系统

工具是代理执行具体任务的基础组件：

```csharp
[Tool("tool_name", Description = "工具描述")]
public class CustomTool : ITool
{
    public async Task<string> ExecuteAsync(string parameter)
    {
        // 工具逻辑实现
        return "执行结果";
    }
}
```

## 🔧 配置选项

### AihubOption 配置

| 配置项 | 描述 | 默认值 |
|--------|------|--------|
| `DefaultModel` | 默认 AI 模型 | "" |
| `OpenRouterApiKey` | OpenRouter API 密钥 | "" |
| `MaxToolCallLoopTimes` | 最大工具调用循环次数 | 10 |
| `TicketExpiredMinutes` | 票据过期时间(分钟) | 30 |

### 支持的 AI 模型

- `google/gemini-2.0-flash-001`
- `openai/gpt-4.1-nano`
- `deepseek/deepseek-chat-v3-0324`
- `qwen/qwen3-coder:free` - 免费模型
- 更多模型请参考 [OpenRouter 文档](https://openrouter.ai/models)

---

**Made with ❤️ by Toosean**