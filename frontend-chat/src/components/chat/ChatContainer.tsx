import { useEffect, useRef, useState, useCallback } from "react";
import { Chat<PERSON>eader } from "./ChatHeader";
import { ChatMessage } from "./ChatMessage";
import { ChatInput } from "./ChatInput";
import { InformationBanner } from "./InformationBanner";
import { DebugPanel } from "../debug/DebugPanel";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useSignalR } from '@/hooks/use-signalr';
import { useDebugSignalR } from '@/hooks/use-debug-signalr';
import { errorHandler } from '@/lib/error-handler';
import type { Message } from '@/types/chat';
import { HubConnectionState } from "@microsoft/signalr";

interface ChatContainerProps {
  signalR: ReturnType<typeof useSignalR>;
}

/**
 * Main chat container component that manages the entire chat interface
 * Handles message display, user input, and tool call interactions
 * 
 * @param signalR - SignalR hook instance for chat functionality
 */
export const ChatContainer = ({ signalR }: ChatContainerProps) => {
  const { 
    connection,
    messages, 
    profile,
    isLoading,
    informationBanner,
    sendMessage, 
    createNewSession, 
    confirmToolCall,
    closeInformationBanner
  } = signalR;

  // Debug hook (only active in development)
  const debugSignalR = useDebugSignalR(signalR, (signalR as ReturnType<typeof useSignalR> & { _debugSetters?: unknown })._debugSetters);
  
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const scrollBottomDetectorRef = useRef<HTMLDivElement>(null);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  /**
   * Setup intersection observer to detect if user is at bottom
   */
  useEffect(() => {
    if (!scrollBottomDetectorRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        setIsUserAtBottom(entries[0].isIntersecting);
      },
      {
        threshold: 0,
        rootMargin: '0px'
      }
    );

    observer.observe(scrollBottomDetectorRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  /**
   * Auto-scroll to bottom when new messages arrive
   * Only scrolls if the user is currently at the bottom
   */
  useEffect(() => {
    if (isUserAtBottom && scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [messages, isUserAtBottom]);

  /**
   * Handle user action on tool calls (confirm/reject)
   * @param toolCallId - ID of the tool call to act on
   * @param action - Whether to confirm or reject the tool call
   */
  const handleToolCallAction = async (toolCallId: string, action: 'confirm' | 'reject') => {
    // Find the message that contains this tool call
    const messageWithTool = messages.find(message => 
      message.toolCalls?.some(tool => tool.id === toolCallId)
    );
    
    if (messageWithTool) {
      try {
        await confirmToolCall(messageWithTool.id, toolCallId, action === 'confirm');
      } catch (error) {
        // Error already handled in useSignalR hook
        // This catch block prevents unhandled promise rejection
      }
    }
  };

  /**
   * Handle chat reset by creating a new session
   */
  const handleReset = async () => {
    try {
      await createNewSession();
    } catch (error) {
      errorHandler.handleError({
        message: (error as Error).message || 'Unknown error',
        context: 'Session Reset',
        error: error as Error,
        severity: 'low'
      });
    }
  };

  /**
   * Handle sending a new message
   * @param content - Message content to send
   */
  const handleSendMessage = async (content: string) => {
    if (connection.state !== HubConnectionState.Connected) {
      errorHandler.handleError({
        message: 'Not connected to server',
        context: 'Message Send',
        severity: 'medium'
      });
      return;
    }
    
    try {
      await sendMessage(content);
    } catch (error) {
      // Error already handled in useSignalR hook
      // This catch block prevents unhandled promise rejection
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-card shadow-lg overflow-hidden">
      <InformationBanner
        message={informationBanner.message}
        isVisible={informationBanner.isVisible}
        onClose={closeInformationBanner}
      />
      
      <ChatHeader 
        profile={profile}
        onReset={handleReset} 
        connectionStatus={connection?.state}
      />

      {connection?.state === HubConnectionState.Disconnected && profile?.fetched && (
        <div className="flex py-1 justify-center">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg px-4 py-3 text-center">
            <div className="text-sm text-red-700 dark:text-red-200 font-semibold flex items-center gap-2">
              <span>🚫</span>
              <span>未连接到服务器</span>
            </div>
          </div>
        </div>
      )} 

      {connection?.state === HubConnectionState.Reconnecting && (
        <div className="flex py-1 justify-center">
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg px-4 py-3 text-center">
            <div className="text-sm text-orange-700 dark:text-orange-200 font-semibold flex items-center gap-2">
              <span>⏳</span>
              <span>服务器正在开小差，稍等片刻</span>
            </div>
          </div>
        </div>
      )}
      
      <ScrollArea ref={scrollAreaRef} className="flex-1 scroll-smooth overflow-hidden">
        <div className="space-y-1">
          {/* Connection status message */}
          {connection?.state === HubConnectionState.Connecting && (
            <div className="flex gap-3 p-4 justify-center">
              <div className="bg-orange-100 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg px-4 py-3 text-center">
                <div className="text-sm text-orange-800 dark:text-orange-200">
                  🔌 连接到到服务器中…
                </div>
              </div>
            </div>
          )}

          {/* Messages */}
          {connection?.state === HubConnectionState.Connected && profile?.fetched && messages.length === 0 && (
            <div className="flex items-center justify-center h-full min-h-[200px]">
              <div className="text-center text-muted-foreground">
                <div className="mb-4">
                  {profile?.aiAvatar ? (
                    <img 
                      src={profile.aiAvatar} 
                      alt="AI Avatar" 
                      className="w-16 h-16 mx-auto rounded-full object-cover"
                    />
                  ) : (
                    <div className="text-6xl">🤖</div>
                  )}
                </div>
                <p className="text-lg font-medium mb-2">{profile?.displayName}已就绪</p>
                <p className="text-sm">发送消息，开始我们的对话吧！</p>
              </div>
            </div>
          )}

          {messages.map((message) => (
            <ChatMessage 
              key={message.id} 
              message={message} 
              profile={profile}
              onToolCallAction={handleToolCallAction}
            />
          ))}

          {/* Loading indicator */}
          {isLoading && (
            <div className="flex gap-3 p-4 justify-start">
              <div className="bg-chat-ai-bg text-chat-ai-fg rounded-2xl rounded-bl-sm px-4 py-3 message-bubble">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              </div>
            </div>
          )}

          {/* Invisible element to detect if user is at bottom */}
          <div ref={scrollBottomDetectorRef} className="w-1 h-1 opacity-0 pointer-events-none" />
        </div>
      </ScrollArea>

      {/* Recommended prompts - positioned directly above input */}
      {connection?.state === HubConnectionState.Connected && profile?.fetched && messages.length === 0 && 
       profile?.recommendedStartPrompts && profile.recommendedStartPrompts.length > 0 && (
        <div className="bg-chat-header-bg px-4 py-3">
          <div className="max-w-6xl mx-auto">
            <p className="text-xs font-medium mb-2 text-center text-muted-foreground">试试这些提示词</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1.5">
              {profile.recommendedStartPrompts.map((prompt, index) => (
                <button
                  key={index}
                  onClick={() => handleSendMessage(prompt)}
                  className="p-2 text-left bg-muted hover:bg-muted/80 border border-border rounded-md transition-all duration-200 text-xs text-foreground hover:shadow-sm hover:scale-[1.01] active:scale-[0.99] line-clamp-2"
                  disabled={isLoading}
                >
                  {prompt}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      <ChatInput 
        onSendMessage={handleSendMessage}
        isLoading={isLoading || connection?.state !== HubConnectionState.Connected || !profile?.fetched}
        placeholder={connection?.state === HubConnectionState.Connected && profile?.fetched ? "询问任何问题" : "连接中..."}
      />

      {/* Debug Panel (only in development) */}
      <DebugPanel
        onTriggerInformation={debugSignalR.triggerInformation}
        onTriggerError={debugSignalR.triggerError}
        onTriggerBusy={debugSignalR.triggerBusy}
        onTriggerIdle={debugSignalR.triggerIdle}
        onTriggerCriticalError={debugSignalR.triggerCriticalError}
        connectionState={debugSignalR.connectionState}
        isLoading={debugSignalR.isLoading}
        messagesCount={debugSignalR.messagesCount}
      />
    </div>
  );
};