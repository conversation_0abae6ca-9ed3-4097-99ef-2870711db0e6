import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
  ArcElement,
  RadialLinearScale,
} from 'chart.js';
import { Bar, Line, Pie, Doughnut, Radar, PolarArea } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
  ArcElement,
  RadialLinearScale,
);

interface ChartData {
  labels?: string[];
  datasets: {
    label?: string;
    data: number[];
    [key: string]: unknown;
  }[];
}

interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    [key: string]: unknown;
  };
  scales?: {
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'radar' | 'polarArea';
  data: ChartData;
  options?: ChartOptions;
}

interface ChartRendererProps {
  config: ChartConfig;
}

export const ChartRenderer: React.FC<ChartRendererProps> = ({ config }) => {
  const { type, data, options } = config;

  // Calculate dynamic width based on data content
  const calculateDynamicWidth = (): number => {

    const labelCount = data.labels?.length || 0;
    const datasetCount = data.datasets?.length || 0;
    
    switch (type) {
      case 'bar':
      case 'line':
        return Math.max(400, labelCount * 40 + 100);
      case 'pie':
      case 'doughnut':
      case 'radar':
      case 'polarArea':
      default:
        return 400;
    }
  };

  const dynamicWidth = calculateDynamicWidth();
  const dynamicHeight = 320;

  // Default options for better appearance
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    ...options,
  };

  try {
    switch (type) {
      case 'bar':
        return (
          <div className="w-full" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: '100%', margin: '0 auto' }}>
              <Bar data={data} options={defaultOptions} />
            </div>
          </div>
        );
      case 'line':
        return (
          <div className="w-full" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: '100%', margin: '0 auto' }}>
              <Line data={data} options={defaultOptions} />
            </div>
          </div>
        );
      case 'pie':
        return (
          <div className="w-full flex justify-center items-center" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: dynamicWidth, maxHeight: '100%' }}>
              <Pie data={data} options={defaultOptions} />
            </div>
          </div>
        );
      case 'doughnut':
        return (
          <div className="w-full flex justify-center items-center" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: dynamicWidth, maxHeight: '100%' }}>
              <Doughnut data={data} options={defaultOptions} />
            </div>
          </div>
        );
      case 'radar':
        return (
          <div className="w-full flex justify-center items-center" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: dynamicWidth, maxHeight: '100%' }}>
              <Radar data={data} options={defaultOptions} />
            </div>
          </div>
        );
      case 'polarArea':
        return (
          <div className="w-full flex justify-center items-center" style={{ height: dynamicHeight }}>
            <div style={{ width: dynamicWidth, height: dynamicWidth, maxHeight: '100%' }}>
              <PolarArea data={data} options={defaultOptions} />
            </div>
          </div>
        );
      default:
        return (
          <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20">
            <p className="text-red-600 dark:text-red-400">
              Unsupported chart type: {type}
            </p>
          </div>
        );
    }
  } catch (error) {
    return (
      <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20">
        <p className="text-red-600 dark:text-red-400">
          Error rendering chart: {error instanceof Error ? error.message : 'Unknown error'}
        </p>
      </div>
    );
  }
};