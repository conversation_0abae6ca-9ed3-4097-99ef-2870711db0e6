@tailwind base;
@tailwind components;
@tailwind utilities;

/* AI Chat UI Design System - Modern & Compact */

@layer base {
  :root {
    /* Light theme - Modern blue gradient palette */
    --background: 220 15% 97%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 91% 70%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 215 25% 15%;

    --muted: 220 14% 96%;
    --muted-foreground: 215 15% 45%;

    --accent: 217 91% 95%;
    --accent-foreground: 217 91% 40%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    /* Chat specific colors */
    --chat-user-bg: 217 91% 60%;
    --chat-user-fg: 0 0% 100%;
    --chat-ai-bg: 220 14% 96%;
    --chat-ai-fg: 215 25% 15%;
    --chat-input-bg: 0 0% 100%;
    --chat-header-bg: 0 0% 100%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(217 91% 70%));
    --gradient-bg: linear-gradient(180deg, hsl(220 15% 97%), hsl(220 14% 96%));
    --gradient-chat: linear-gradient(135deg, hsl(217 91% 95%), hsl(220 14% 96%));

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 91% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 91% / 0.1), 0 2px 4px -2px hsl(220 13% 91% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 91% / 0.1), 0 4px 6px -4px hsl(220 13% 91% / 0.1);
    --shadow-glow: 0 0 20px hsl(217 91% 60% / 0.15);

    /* Transitions */
    --transition-theme: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark theme - Modern dark with blue accents */
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 91% 70%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 91% 15%;
    --accent-foreground: 217 91% 60%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 217 91% 60%;

    /* Dark chat specific colors */
    --chat-user-bg: 217 91% 60%;
    --chat-user-fg: 0 0% 100%;
    --chat-ai-bg: 217 32% 17%;
    --chat-ai-fg: 210 40% 98%;
    --chat-input-bg: 222 47% 11%;
    --chat-header-bg: 222 47% 11%;

    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(217 91% 70%));
    --gradient-bg: linear-gradient(180deg, hsl(222 84% 5%), hsl(222 47% 8%));
    --gradient-chat: linear-gradient(135deg, hsl(217 91% 10%), hsl(217 32% 17%));

    /* Dark shadows */
    --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.2);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -2px hsl(0 0% 0% / 0.3);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.4), 0 4px 6px -4px hsl(0 0% 0% / 0.4);
    --shadow-glow: 0 0 20px hsl(217 91% 60% / 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
    transition: var(--transition-theme);
  }

  html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  /* Mobile viewport fix for browser UI */
  @supports (-webkit-touch-callout: none) {
    html, body {
      height: -webkit-fill-available;
    }
  }

  /* Ensure proper mobile viewport handling */
  body {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-bg);
    transition: var(--transition-theme);
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    padding: 0;
  }

  html {
    transition: var(--transition-theme);
  }
}

@layer components {
  /* Chat message animations */
  .message-enter {
    @apply animate-[fade-in_0.3s_ease-out,scale-in_0.2s_ease-out];
  }

  .message-bubble {
    @apply shadow-[var(--shadow-md)] backdrop-blur-sm;
    transition: var(--transition-smooth);
  }

  .message-bubble:hover {
    @apply shadow-[var(--shadow-lg)];
    transform: translateY(-1px);
  }

  /* Theme toggle animation */
  .theme-toggle {
    @apply shadow-[var(--shadow-md)];
    transition: var(--transition-smooth);
  }

  .theme-toggle:hover {
    @apply shadow-[var(--shadow-glow)];
    transform: scale(1.05);
  }

  /* Chat input enhancement */
  .chat-input {
    @apply shadow-[var(--shadow-md)] backdrop-blur-sm;
    transition: var(--transition-smooth);
  }

  .chat-input:focus-within {
    @apply shadow-[var(--shadow-glow)];
    transform: translateY(-1px);
  }

  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
    transition: var(--transition-smooth);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Scroll animation */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Markdown content styles */
  .markdown-content {
    @apply text-sm leading-relaxed;
  }

  .markdown-content h1 {
    @apply text-xl font-bold mt-4 mb-2 first:mt-0;
  }

  .markdown-content h2 {
    @apply text-lg font-semibold mt-3 mb-2 first:mt-0;
  }

  .markdown-content h3 {
    @apply text-base font-medium mt-3 mb-2 first:mt-0;
  }

  .markdown-content p {
    @apply mb-2 last:mb-0;
  }

  .markdown-content ul, .markdown-content ol {
    @apply mb-2 ml-4 last:mb-0;
  }

  .markdown-content li {
    @apply mb-1 last:mb-0;
  }

  .markdown-content blockquote {
    @apply border-l-4 border-muted-foreground/30 pl-4 my-2 italic text-muted-foreground;
  }

  .markdown-content code {
    @apply bg-muted px-1.5 py-0.5 rounded text-xs font-mono;
  }

  .markdown-content pre {
    @apply bg-muted p-3 rounded-md overflow-x-auto my-2;
  }

  .markdown-content pre code {
    @apply bg-transparent p-0 text-sm;
  }

  .markdown-content table {
    @apply w-full my-2 text-xs;
    border-collapse: collapse;
    border: 1px solid hsl(var(--muted-foreground) / 0.4);
    font-size: 11px;
    line-height: 1.2;
  }

  .markdown-content th, .markdown-content td {
    @apply px-2 py-1 text-left;
    border: 1px solid hsl(var(--muted-foreground) / 0.4);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }

  .markdown-content th {
    @apply bg-muted/50 font-semibold text-muted-foreground;
    background: hsl(var(--muted) / 0.5);
    font-size: 10px;
    padding: 4px 6px;
  }

  .markdown-content tr:nth-child(even) td {
    @apply bg-muted/20;
  }

  .markdown-content tr:hover td {
    @apply bg-muted/30;
    transition: background-color 0.2s ease;
  }

  .markdown-content tbody tr:hover {
    @apply shadow-sm;
  }

  .markdown-content a {
    @apply text-primary underline hover:no-underline;
  }

  .markdown-content strong {
    @apply font-semibold;
  }

  .markdown-content em {
    @apply italic;
  }

  .markdown-content hr {
    @apply border-t border-muted my-4;
  }
}