import { ChatContainer } from "@/components/chat/ChatContainer";
import { useSignalR } from '@/hooks/use-signalr';
import { useToast } from '@/components/ui/use-toast';
import { useCallback, useEffect, useState } from 'react';
import NoTicket from './NoTicket';
import { HubConnectionState } from "@microsoft/signalr";

const Index = () => {
  const { toast } = useToast();
  const [ticket, setTicket] = useState<string | null>(null);
  const [isTicketLoaded, setIsTicketLoaded] = useState(false);
  
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const ticketParam = urlParams.get('ticket');
    setTicket(ticketParam);
    setIsTicketLoaded(true);
  }, []);

  const onCriticalError = useCallback((error: string) => {
    toast({
      title: "错误",
      description: error,
      variant: "destructive",
    });
  }, [toast]);
  
  const signalR = useSignalR({
    onCriticalError,
    ticket: ticket
  });

  // Show loading while checking ticket
  if (!isTicketLoaded) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 supports-[height:100dvh]:h-dvh">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">正在检查访问权限...</p>
        </div>
      </div>
    );
  }

  // Show NoTicket page if no ticket provided
  if (!ticket) {
    return <NoTicket />;
  }

  return (
    <div className="h-screen w-screen overflow-hidden bg-gradient-to-br from-background to-muted/20 supports-[height:100dvh]:h-dvh">
      <ChatContainer signalR={signalR} />
    </div>
  );
};

export default Index;
