import { AlertCircle  } from "lucide-react";

const NoTicket = () => {
 

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="max-w-md w-full bg-card rounded-lg shadow-lg p-6 border">
        <div className="text-center mb-6">
          <AlertCircle className="w-16 h-16 text-orange-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-foreground mb-2">
            缺少访问凭证
          </h1>
          <p className="text-muted-foreground">
            需要有效的 ticket 参数才能访问智能体页面
          </p>
        </div>
      </div>
    </div>
  );
};

export default NoTicket;