/**
 * Application constants and default values
 * Values are read from environment variables with fallback defaults
 */

// Helper function to get environment variables with defaults
const getEnvVar = (key: string, defaultValue: string): string => {
  return import.meta.env[key] ?? defaultValue;
};

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key];
  return value ? parseInt(value, 10) : defaultValue;
};

const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[key];
  return value ? value.toLowerCase() === 'true' : defaultValue;
};

export const APP_CONFIG = {
  // SignalR connection settings
  SIGNALR: {
    DEFAULT_URL: getEnvVar('VITE_SIGNALR_HUB_URL', 'http://localhost:5000/chathub'),
    DEFAULT_TICKET: null,
    RECONNECT_ATTEMPTS: getEnvNumber('VITE_SIGNALR_RECONNECT_ATTEMPTS', 3),
    CONNECTION_TIMEOUT: getEnvNumber('VITE_SIGNALR_CONNECTION_TIMEOUT', 30000), // 30 seconds
    // Exponential backoff settings
    BASE_RETRY_DELAY: getEnvNumber('VITE_SIGNALR_BASE_RETRY_DELAY', 1000), // 1 second
    MAX_RETRY_DELAY: getEnvNumber('VITE_SIGNALR_MAX_RETRY_DELAY', 30000), // 30 seconds
    EXPONENTIAL_BASE: getEnvNumber('VITE_SIGNALR_EXPONENTIAL_BASE', 2),
    MAX_EXPONENTIAL_RETRIES: getEnvNumber('VITE_SIGNALR_MAX_EXPONENTIAL_RETRIES', 3),
  },
  
  // UI settings
  UI: {
    STREAMING_CHAR_DELAY: getEnvNumber('VITE_UI_STREAMING_CHAR_DELAY', 30), // milliseconds between characters when streaming
    AUTO_SCROLL_DELAY: getEnvNumber('VITE_UI_AUTO_SCROLL_DELAY', 100), // milliseconds delay for auto scroll
    TOAST_DURATION: getEnvNumber('VITE_UI_TOAST_DURATION', 5000), // milliseconds
  },
  
  // Message settings
  MESSAGE: {
    MAX_CONTENT_LENGTH: getEnvNumber('VITE_MESSAGE_MAX_CONTENT_LENGTH', 8000),
    ID_PREFIX: getEnvVar('VITE_MESSAGE_ID_PREFIX', 'msg_'),
  },
  
  // Development settings
  DEBUG: {
    ENABLED: getEnvBoolean('VITE_DEBUG_MODE', false),
    LOG_LEVEL: getEnvVar('VITE_LOG_LEVEL', 'info'),
  },
  
  // Default profile settings
  PROFILE: {
    DEFAULT: {
      key: 'UNKNOWN',
      displayName: '…',
      description: '…',
      fetched: false,
    }
  },
} as const;

/**
 * Tool call status values
 */
export const TOOL_CALL_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed', 
  REJECTED: 'rejected',
  COMPLETED: 'completed',
} as const;

/**
 * Message categories
 */
export const MESSAGE_CATEGORIES = {
  USER: 'User',
  ASSISTANT: 'Assistant',
} as const;