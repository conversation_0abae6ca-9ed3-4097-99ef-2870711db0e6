/**
 * Chat-related type definitions
 * Centralized type definitions for all chat components and hooks
 */

import type { HubConnection, HubConnectionState } from '@microsoft/signalr';

/**
 * Tool call data transfer object from server
 */
export interface ToolCallDto {
  id: string;
  toolName: string;
  toolDescription: string;
  needConfirm: boolean;
  isConfirm: boolean;
  severity: 'Low' | 'Medium' | 'High';
}

/**
 * Message data transfer object from server
 */
export interface MessageDto {
  id: string;
  content: string;
  category: 'User' | 'Assistant';
  timestamp: string;
  toolCalls?: ToolCallDto[];
}

/**
 * Streaming message events from server
 */
export interface StreamingMessage {
  messageId: string;
  content: string;
}

export interface BeginStreamingMessage {
  messageId: string;
}

export interface EndStreamingMessage {
  messageId: string;
  content: string;
  toolCalls: ToolCallDto[];
}

export interface BlockMessage {
  messageId: string;
  content: string;
  toolCalls: ToolCallDto[];
}

/**
 * Session management events
 */
export interface CreateNewSessionSuccess {
  sessionId: string;
}

/**
 * Client-side message representation
 */
export interface Message {
  id: string;
  content: string;
  category: 'User' | 'Assistant' | 'Error';
  timestamp: Date;
  isFinished: boolean;
  toolCalls?: ToolCallDto[];
  isStreaming?: boolean;
}

/**
 * Profile data transfer object from server
 */
export interface Profile {
  key: string;
  displayName: string;
  description: string;
  icon?: string;
  signOutUrl?: string;
  theme?: string;
  aiAvatar?: string;
  userAvatar?: string;
  recommendedStartPrompts?: string[];
  fetched: boolean;
}

/**
 * SignalR hook options
 */
export interface UseSignalROptions {
  url?: string;
  ticket?: string;
  onConnectionStateChange?: (state: HubConnectionState) => void;
  onCriticalError?: (error: string) => void;
}

/**
 * Information banner state
 */
export interface InformationBanner {
  isVisible: boolean;
  message: string;
}

/**
 * SignalR hook return value
 */
export interface UseSignalRReturn {
  connection: HubConnection | null;
  messages: Message[];
  profile: Profile | null;
  isLoading: boolean;
  informationBanner: InformationBanner;
  sendMessage: (message: string) => Promise<void>;
  createNewSession: () => Promise<void>;
  confirmToolCall: (messageId: string, toolCallId: string, isConfirm: boolean) => Promise<void>;
  clearMessages: () => void;
  closeInformationBanner: () => void;
}

/**
 * Chat component props
 */
export interface ChatHeaderProps {
  onReset?: () => void;
  profile: Profile | null;
  connectionStatus?: HubConnectionState;
}

export interface ChatMessageProps {
  message: Message;
  profile?: Profile | null;
  onToolCallAction?: (toolCallId: string, action: 'confirm' | 'reject') => void;
}