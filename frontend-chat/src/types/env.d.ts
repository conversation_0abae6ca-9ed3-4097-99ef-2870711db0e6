/// <reference types="vite/client" />

/**
 * Environment variables type definitions for Vite
 * These extend the ImportMetaEnv interface to provide type safety
 */
interface ImportMetaEnv {
  // SignalR Configuration
  readonly VITE_SIGNALR_HUB_URL: string
  readonly VITE_SIGNALR_RECONNECT_ATTEMPTS: string
  readonly VITE_SIGNALR_CONNECTION_TIMEOUT: string
  readonly VITE_SIGNALR_BASE_RETRY_DELAY: string
  readonly VITE_SIGNALR_MAX_RETRY_DELAY: string
  readonly VITE_SIGNALR_EXPONENTIAL_BASE: string
  readonly VITE_SIGNALR_MAX_EXPONENTIAL_RETRIES: string

  // UI Configuration
  readonly VITE_UI_STREAMING_CHAR_DELAY: string
  readonly VITE_UI_AUTO_SCROLL_DELAY: string
  readonly VITE_UI_TOAST_DURATION: string

  // Message Configuration
  readonly VITE_MESSAGE_MAX_CONTENT_LENGTH: string
  readonly VITE_MESSAGE_ID_PREFIX: string

  // Development Settings
  readonly VITE_DEBUG_MODE: string
  readonly VITE_LOG_LEVEL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}