/**
 * Improved error handling patterns and utilities
 * Provides consistent error handling across the application
 */

import { errorH<PERSON><PERSON>, type ErrorInfo } from './error-handler';

/**
 * Async operation wrapper with consistent error handling
 */
export async function safeAsyncOperation<T>(
  operation: () => Promise<T>,
  errorInfo: Omit<ErrorInfo, 'error'>,
  onError?: (error: string) => void
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    errorHandler.handleError({
      ...errorInfo,
      error: error as Error,
    }, onError);
    return null;
  }
}

/**
 * Promise wrapper that doesn't throw but returns result or null
 */
export async function safePromise<T>(
  promise: Promise<T>,
  errorInfo: Omit<ErrorInfo, 'error'>,
  onError?: (error: string) => void
): Promise<T | null> {
  try {
    return await promise;
  } catch (error) {
    errorHandler.handleError({
      ...errorInfo,
      error: error as Error,
    }, onError);
    return null;
  }
}

/**
 * Safe JSON parse with error handling
 */
export function safeJsonParse<T>(
  jsonString: string,
  context: string = 'JSON Parsing'
): T | null {
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    errorHandler.handleError({
      message: 'Failed to parse JSON',
      context,
      error: error as Error,
      severity: 'medium'
    });
    return null;
  }
}

/**
 * Safe function execution wrapper
 */
export function safeExecute<T>(
  fn: () => T,
  errorInfo: Omit<ErrorInfo, 'error'>,
  onError?: (error: string) => void
): T | null {
  try {
    return fn();
  } catch (error) {
    errorHandler.handleError({
      ...errorInfo,
      error: error as Error,
    }, onError);
    return null;
  }
}

/**
 * Retry mechanism with exponential backoff
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number;
    baseDelay?: number;
    maxDelay?: number;
    exponentialBase?: number;
    context?: string;
    onError?: (error: string) => void;
  } = {}
): Promise<T | null> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    exponentialBase = 2,
    context = 'Retry Operation',
    onError
  } = options;

  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        Math.pow(exponentialBase, attempt) * baseDelay,
        maxDelay
      );
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // All retries failed
  errorHandler.handleError({
    message: `Operation failed after ${maxRetries + 1} attempts`,
    context,
    error: lastError!,
    severity: 'high'
  }, onError);
  
  return null;
}

/**
 * Circuit breaker pattern implementation
 */
export class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime: number | null = null;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private readonly threshold: number = 5,
    private readonly timeout: number = 60000, // 1 minute
    private readonly context: string = 'Circuit Breaker'
  ) {}

  async execute<T>(
    operation: () => Promise<T>,
    onError?: (error: string) => void
  ): Promise<T | null> {
    if (this.state === 'open') {
      if (this.shouldAttemptReset()) {
        this.state = 'half-open';
      } else {
        errorHandler.handleError({
          message: 'Circuit breaker is open',
          context: this.context,
          severity: 'medium',
          userFriendlyMessage: 'Service is temporarily unavailable. Please try again later.'
        }, onError);
        return null;
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      errorHandler.handleError({
        message: (error as Error).message || 'Circuit breaker operation failed',
        context: this.context,
        error: error as Error,
        severity: 'medium'
      }, onError);
      return null;
    }
  }

  private shouldAttemptReset(): boolean {
    return this.lastFailureTime !== null && 
           Date.now() - this.lastFailureTime >= this.timeout;
  }

  private onSuccess(): void {
    this.failureCount = 0;
    this.state = 'closed';
    this.lastFailureTime = null;
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.threshold) {
      this.state = 'open';
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.state = 'closed';
  }
}

/**
 * Debounced error handler to prevent spam
 */
export class DebouncedErrorHandler {
  private timeouts = new Map<string, NodeJS.Timeout>();

  handleError(
    key: string,
    errorInfo: ErrorInfo,
    onError?: (error: string) => void,
    debounceMs: number = 1000
  ): void {
    // Clear existing timeout for this key
    const existingTimeout = this.timeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      errorHandler.handleError(errorInfo, onError);
      this.timeouts.delete(key);
    }, debounceMs);

    this.timeouts.set(key, timeout);
  }

  clear(): void {
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();
  }
}

// Export singleton instances
export const debouncedErrorHandler = new DebouncedErrorHandler();
export const connectionCircuitBreaker = new CircuitBreaker(3, 30000, 'Connection');
export const messageCircuitBreaker = new CircuitBreaker(5, 60000, 'Message Operations');