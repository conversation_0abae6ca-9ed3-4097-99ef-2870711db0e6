/**
 * Centralized error handling utilities
 */

export interface ErrorInfo {
  message: string;
  context?: string;
  error?: Error;
  severity?: 'low' | 'medium' | 'high';
}

/**
 * Error handler class for consistent error management
 */
export class ErrorHandler {
  private static instance: <PERSON>rrorHandler;

  private constructor() {}

  public static getInstance(): <PERSON>rrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle errors with consistent logging and user notification
   */
  handleError(errorInfo: ErrorInfo, onError?: (message: string) => void): void {
    const { message, context, error, severity = 'medium' } = errorInfo;
    
    // Format the error message
    const formattedMessage = context ? `${context}: ${message}` : message;
    
    // Log error for development/debugging
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${severity.toUpperCase()}] ${formattedMessage}`, error);
    }
    
    // Notify user if callback provided
    if (onError) {
      onError(this.getUserFriendlyMessage(message, context));
    }
  }

  /**
   * Convert technical error messages to user-friendly ones
   */
  private getUserFriendlyMessage(message: string, context?: string): string {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
      return 'Connection problem. Please check your internet connection and try again.';
    }
    
    if (lowerMessage.includes('timeout')) {
      return 'The operation timed out. Please try again.';
    }
    
    if (lowerMessage.includes('unauthorized') || lowerMessage.includes('auth')) {
      return 'Authentication failed. Please refresh the page and try again.';
    }
    
    if (context === 'SignalR Connection') {
      return 'Unable to connect to the AI service. Please try again in a moment.';
    }
    
    if (context === 'Message Send') {
      return 'Failed to send message. Please try again.';
    }
    
    if (context === 'Tool Confirmation') {
      return 'Failed to process tool confirmation. Please try again.';
    }
    
    // Default user-friendly message
    return 'Something went wrong. Please try again.';
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();