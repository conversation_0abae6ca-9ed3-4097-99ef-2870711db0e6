import { useState, useEffect, useRef, useCallback } from 'react';
import { HubConnectionBuilder, HubConnection } from '@microsoft/signalr';
import { APP_CONFIG, CONNECTION_STATES } from '@/config/constants';
import { errorHandler } from '@/lib/error-handler';

export interface UseSignalRConnectionOptions {
  url?: string;
  ticket?: string;
  onConnectionStateChange?: (connected: boolean) => void;
  onError?: (error: string) => void;
}

export interface UseSignalRConnectionReturn {
  connection: HubConnection | null;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  registerEventHandler: (eventName: string, handler: (...args: unknown[]) => void) => void;
  unregisterEventHandler: (eventName: string, handler: (...args: unknown[]) => void) => void;
}

/**
 * Hook for managing SignalR connection lifecycle
 * Focused on connection management only
 */
export const useSignalRConnection = (
  options: UseSignalRConnectionOptions = {}
): UseSignalRConnectionReturn => {
  const {
    url = APP_CONFIG.SIGNALR.DEFAULT_URL,
    ticket = APP_CONFIG.SIGNALR.DEFAULT_TICKET,
    onConnectionStateChange,
    onError
  } = options;

  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const connectionRef = useRef<HubConnection | null>(null);
  const eventHandlersRef = useRef<Map<string, ((...args: unknown[]) => void)[]>>(new Map());


  /**
   * Create SignalR connection instance
   */
  const createConnection = useCallback(() => {
    if (connectionRef.current) {
      return connectionRef.current;
    }



    const newConnection = new HubConnectionBuilder()
      .withUrl(`${url}?ticket=${ticket}`)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: retryContext => {
          const { EXPONENTIAL_BASE, BASE_RETRY_DELAY, MAX_EXPONENTIAL_RETRIES, MAX_RETRY_DELAY } = APP_CONFIG.SIGNALR;
          
          if (retryContext.previousRetryCount < MAX_EXPONENTIAL_RETRIES) {
            return Math.min(
              Math.pow(EXPONENTIAL_BASE, retryContext.previousRetryCount + 1) * BASE_RETRY_DELAY,
              MAX_RETRY_DELAY
            );
          }
          return MAX_RETRY_DELAY;
        }
      })
      .build();

    // Setup connection event handlers
    newConnection.onclose((error) => {
      setIsConnected(false);
      onConnectionStateChange?.(false);
      if (error && process.env.NODE_ENV === 'development') {
        console.warn('Connection closed with error:', error);
      }
    });

    newConnection.onreconnecting((error) => {
      setIsConnected(false);
      onConnectionStateChange?.(false);
      if (error && process.env.NODE_ENV === 'development') {
        console.info('Reconnecting due to error:', error);
      }
    });

    newConnection.onreconnected((connectionId) => {
      setIsConnected(true);
      onConnectionStateChange?.(true);
      
      // Re-register all event handlers
      eventHandlersRef.current.forEach((handlers, eventName) => {
        handlers.forEach(handler => {
          newConnection.on(eventName, handler);
        });
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.info('Reconnected with ID:', connectionId);
      }
    });

    connectionRef.current = newConnection;
    setConnection(newConnection);
    return newConnection;
  }, [url, ticket, onConnectionStateChange]);

  /**
   * Connect to SignalR hub
   */
  const connect = useCallback(async () => {
    const conn = createConnection();
    
    if (conn.state === CONNECTION_STATES.DISCONNECTED) {
      try {
        await conn.start();
        setIsConnected(true);
        onConnectionStateChange?.(true);
      } catch (error) {
        setIsConnected(false);
        onConnectionStateChange?.(false);
        errorHandler.handleError({
          message: (error as Error).message || 'Unknown connection error',
          context: 'SignalR Connection',
          error: error as Error,
          severity: 'high'
        }, onError);
        throw error;
      }
    }
  }, [createConnection, onConnectionStateChange, onError]);

  /**
   * Disconnect from SignalR hub
   */
  const disconnect = useCallback(async () => {
    const conn = connectionRef.current;
    if (conn && conn.state !== CONNECTION_STATES.DISCONNECTED) {
      try {
        // Clear all event handlers
        eventHandlersRef.current.forEach((handlers, eventName) => {
          handlers.forEach(handler => {
            conn.off(eventName, handler);
          });
        });
        eventHandlersRef.current.clear();
        
        await conn.stop();
        setIsConnected(false);
        onConnectionStateChange?.(false);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Error during disconnect:', error);
        }
      } finally {
        connectionRef.current = null;
        setConnection(null);
      }
    }
  }, [onConnectionStateChange]);

  /**
   * Register event handler
   */
  const registerEventHandler = useCallback((eventName: string, handler: (...args: unknown[]) => void) => {
    const conn = connectionRef.current;
    if (conn) {
      conn.on(eventName, handler);
    }
    
    // Store handler for re-registration after reconnect
    const handlers = eventHandlersRef.current.get(eventName) || [];
    handlers.push(handler);
    eventHandlersRef.current.set(eventName, handlers);
  }, []);

  /**
   * Unregister event handler
   */
  const unregisterEventHandler = useCallback((eventName: string, handler: (...args: unknown[]) => void) => {
    const conn = connectionRef.current;
    if (conn) {
      conn.off(eventName, handler);
    }
    
    // Remove handler from storage
    const handlers = eventHandlersRef.current.get(eventName) || [];
    const updatedHandlers = handlers.filter(h => h !== handler);
    if (updatedHandlers.length > 0) {
      eventHandlersRef.current.set(eventName, updatedHandlers);
    } else {
      eventHandlersRef.current.delete(eventName);
    }
  }, []);

  // Initialize connection on mount
  useEffect(() => {
    createConnection();
    
    return () => {
      disconnect();
    };
  }, [createConnection, disconnect]);

  // Auto-connect on mount
  useEffect(() => {
    connect();
  }, [connect]);

  return {
    connection,
    isConnected,
    connect,
    disconnect,
    registerEventHandler,
    unregisterEventHandler,
  };
};