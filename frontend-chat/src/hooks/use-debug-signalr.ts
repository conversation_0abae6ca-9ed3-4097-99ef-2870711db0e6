import { useCallback } from 'react';
import { generateMessageId } from '@/lib/message-utils';
import type { UseSignalRReturn, Message } from '@/types/chat';

interface DebugSignalRMethods {
  triggerInformation: (message: string) => void;
  triggerError: (message: string) => void;
  triggerBusy: () => void;
  triggerIdle: () => void;
  triggerCriticalError: (message: string) => void;
}

interface DebugSignalRReturn extends DebugSignalRMethods {
  connectionState: string;
  isLoading: boolean;
  messagesCount: number;
}

/**
 * Debug hook for SignalR testing (development only)
 * Provides methods to trigger various SignalR events for testing purposes
 * 
 * This hook exposes internal state setters only in development mode
 * to allow testing of different SignalR event scenarios
 */
export const useDebugSignalR = (
  signalR: UseSignalRReturn,
  // Pass internal setters for testing (only used in development)
  debugSetters?: {
    setInformationBanner?: (banner: { isVisible: boolean; message: string }) => void;
    setMessages?: (updater: (prev: Message[]) => Message[]) => void;
    setIsLoading?: (loading: boolean) => void;
    handleCriticalError?: (errorData: { content: string }) => void;
  }
): DebugSignalRReturn => {
  const { 
    messages,
    isLoading,
    connection,
  } = signalR;

  /**
   * Trigger an Information message for testing
   */
  const triggerInformation = useCallback((message: string) => {
    if (process.env.NODE_ENV !== 'development') return;
    
    debugSetters?.setInformationBanner?.({
      isVisible: true,
      message: message
    });
  }, [debugSetters]);

  /**
   * Trigger an Error message for testing
   */
  const triggerError = useCallback((message: string) => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const errorMessage: Message = {
      id: generateMessageId(),
      content: message,
      category: 'Error',
      timestamp: new Date(),
      isFinished: true
    };
    
    debugSetters?.setMessages?.((prev: Message[]) => [...prev, errorMessage]);
  }, [debugSetters]);

  /**
   * Trigger Busy state for testing
   */
  const triggerBusy = useCallback(() => {
    if (process.env.NODE_ENV !== 'development') return;
    debugSetters?.setIsLoading?.(true);
  }, [debugSetters]);

  /**
   * Trigger Idle state for testing
   */
  const triggerIdle = useCallback(() => {
    if (process.env.NODE_ENV !== 'development') return;
    debugSetters?.setIsLoading?.(false);
  }, [debugSetters]);

  /**
   * Trigger Critical Error for testing
   * Uses the actual handleCriticalError from useSignalR
   */
  const triggerCriticalError = useCallback((message: string) => {
    if (process.env.NODE_ENV !== 'development') return;
    
    console.error('Debug Critical Error:', message);
    
    // Get the real handleCriticalError from debugSetters
    const handleCriticalError = debugSetters?.handleCriticalError;
    if (handleCriticalError) {
      // Call the real function with the correct parameter format
      handleCriticalError({ content: message });
    } else {
      console.warn('handleCriticalError not available in debug setters');
    }
  }, [debugSetters]);

  // Only return debug methods in development
  if (process.env.NODE_ENV !== 'development') {
    return {
      triggerInformation: () => {},
      triggerError: () => {},
      triggerBusy: () => {},
      triggerIdle: () => {},
      triggerCriticalError: () => {},
      connectionState: connection?.state || 'Unknown',
      isLoading,
      messagesCount: messages.length,
    };
  }

  return {
    triggerInformation,
    triggerError,
    triggerBusy,
    triggerIdle,
    triggerCriticalError,
    connectionState: connection?.state || 'Unknown',
    isLoading,
    messagesCount: messages.length,
  };
};