import { useState, useCallback } from 'react';
import { generateMessageId } from '@/lib/message-utils';
import { errorHandler } from '@/lib/error-handler';
import type {
  Message,
  MessageDto,
  StreamingMessage,
  BeginStreamingMessage,
  EndStreamingMessage,
  BlockMessage,
} from '@/types/chat';

export interface UseChatMessagesReturn {
  messages: Message[];
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  addUserMessage: (content: string) => Message;
  handleBeginStreamingMessage: (beginStreamingData: string) => void;
  handleStreamingMessage: (streamingData: string) => void;
  handleEndStreamingMessage: (endStreamingData: string) => void;
  handleBlockMessage: (blockMessageData: string) => void;
  handleHistoryMessages: (historyData: string) => void;
  updateToolCallStatus: (messageId: string, toolCallId: string, isConfirm: boolean) => void;
  clearMessages: () => void;
}

/**
 * Hook for managing chat messages state
 * Focused on message management and updates
 */
export const useChatMessages = (): UseChatMessagesReturn => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Add a user message to the chat
   */
  const addUserMessage = useCallback((content: string): Message => {
    const userMessage: Message = {
      id: generateMessageId(),
      content,
      isUser: true,
      timestamp: new Date(),
      isFinished: true
    };

    setMessages(prev => [...prev, userMessage]);
    return userMessage;
  }, []);

  /**
   * Handle beginning of streaming message
   */
  const handleBeginStreamingMessage = useCallback((beginStreamingData: string) => {
    try {
      const beginStreamingMessage: BeginStreamingMessage = JSON.parse(beginStreamingData);
      setMessages(prev => [...prev, {
        id: beginStreamingMessage.messageId,
        content: "",
        isUser: false,
        timestamp: new Date(),
        isFinished: false,
        isStreaming: true
      }]);
    } catch (error) {
      errorHandler.handleError({
        message: 'Failed to parse begin streaming message',
        context: 'Message Parsing',
        error: error as Error,
        severity: 'medium'
      });
    }
  }, []);

  /**
   * Handle streaming message content
   */
  const handleStreamingMessage = useCallback((streamingData: string) => {
    try {
      const streamingMessage: StreamingMessage = JSON.parse(streamingData);
      setMessages(prev => {
        const messageIndex = prev.findIndex(msg => msg.id === streamingMessage.messageId);
        if (messageIndex !== -1) {
          const updatedMessages = [...prev];
          updatedMessages[messageIndex] = {
            ...updatedMessages[messageIndex],
            content: updatedMessages[messageIndex].content + streamingMessage.content
          };
          return updatedMessages;
        }
        return prev;
      });
    } catch (error) {
      errorHandler.handleError({
        message: 'Failed to parse streaming message',
        context: 'Message Parsing',
        error: error as Error,
        severity: 'medium'
      });
    }
  }, []);

  /**
   * Handle end of streaming message
   */
  const handleEndStreamingMessage = useCallback((endStreamingData: string) => {
    try {
      const endStreamingMessage: EndStreamingMessage = JSON.parse(endStreamingData);
      setMessages(prev => prev.map(msg => 
        msg.id === endStreamingMessage.messageId 
          ? {
              ...msg,
              content: endStreamingMessage.content,
              isFinished: true,
              isStreaming: false,
              toolCalls: endStreamingMessage.toolCalls
            }
          : msg
      ));
    } catch (error) {
      errorHandler.handleError({
        message: 'Failed to parse end streaming message',
        context: 'Message Parsing',
        error: error as Error,
        severity: 'medium'
      });
    }
  }, []);

  /**
   * Handle block message (non-streaming)
   */
  const handleBlockMessage = useCallback((blockMessageData: string) => {
    try {
      const blockMessage: BlockMessage = JSON.parse(blockMessageData);
      setMessages(prev => [...prev, {
        id: blockMessage.messageId,
        content: blockMessage.content,
        isUser: false,
        timestamp: new Date(),
        isFinished: true,
        toolCalls: blockMessage.toolCalls
      }]);
    } catch (error) {
      errorHandler.handleError({
        message: 'Failed to parse block message',
        context: 'Message Parsing',
        error: error as Error,
        severity: 'medium'
      });
    }
  }, []);

  /**
   * Handle history messages
   */
  const handleHistoryMessages = useCallback((historyData: string) => {
    try {
      const historyMessages: MessageDto[] = JSON.parse(historyData);
      const convertedMessages: Message[] = historyMessages.map(msg => ({
        id: msg.id,
        content: msg.content,
        isUser: msg.category === 'User',
        timestamp: new Date(msg.timestamp),
        isFinished: true,
        toolCalls: msg.toolCalls
      }));
      setMessages(convertedMessages);
    } catch (error) {
      errorHandler.handleError({
        message: 'Failed to parse history messages',
        context: 'Message Parsing',
        error: error as Error,
        severity: 'medium'
      });
    }
  }, []);

  /**
   * Update tool call confirmation status
   */
  const updateToolCallStatus = useCallback((messageId: string, toolCallId: string, isConfirm: boolean) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId && msg.toolCalls) {
        return {
          ...msg,
          toolCalls: msg.toolCalls.map(tool => 
            tool.id === toolCallId 
              ? { ...tool, isConfirm }
              : tool
          )
        };
      }
      return msg;
    }));
  }, []);

  /**
   * Clear all messages
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    messages,
    isLoading,
    setIsLoading,
    addUserMessage,
    handleBeginStreamingMessage,
    handleStreamingMessage,
    handleEndStreamingMessage,
    handleBlockMessage,
    handleHistoryMessages,
    updateToolCallStatus,
    clearMessages,
  };
};