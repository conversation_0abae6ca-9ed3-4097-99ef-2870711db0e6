
<div align="center">

   <h2>tsz-java-admin</h2>

</div>


## 📁 项目目录

<details>
<summary> 目录结构 </summary>

<br>

```
youlai-boot
├── docker                              # Docker 目录
│   ├── docker-compose.yml              # docker-compose 脚本
├── sql                                 # SQL脚本
│   ├── mysql                           # MySQL 脚本
├── src                                 # 源码目录
│   ├── common                          # 公共模块
│   │   ├── annotation                  # 注解定义
│   │   ├── base                        # 基础类
│   │   ├── constant                    # 常量
│   │   ├── enums                       # 枚举类型
│   │   ├── exception                   # 异常处理
│   │   ├── model                       # 数据模型
│   │   ├── result                      # 结果封装
│   │   └── util                        # 工具类
│   ├── config                          # 自动装配配置
│   │   └── property                    # 配置属性目录
│   ├── core                            # 核心功能
│   │   ├── aspect                      # 切面(日志、防重提交)
│   │   ├── filter                      # 过滤器(请求日志、限流)
│   │   ├── handler                     # 处理器(数据权限、数据填充)
│   │   └── security                    # Spring Security 安全模块
│   ├── modules                         # 业务模块
│   │   ├── member                      # 会员模块【业务模块演示】
│   │   ├── order                       # 订单模块【业务模块演示】
│   │   ├── product                     # 商品模块【业务模块演示】
│   ├── shared                          # 共享模块
│   │   ├── auth                        # 认证模块
│   │   ├── file                        # 文件模块
│   │   ├── codegen                     # 代码生成模块
│   │   ├── mail                        # 邮件模块
│   │   ├── sms                         # 短信模块
│   │   └── websocket                   # WebSocket 模块
│   ├── system                          # 系统模块
│   │   ├── controller                  # 控制层
│   │   ├── converter                   # MapStruct 转换器
│   │   ├── event                       # 事件处理
│   │   ├── handler                     # 处理器
│   │   ├── listener                    # 监听器
│   │   ├── model                       # 模型层
│   │   │   ├── bo                      # 业务对象
│   │   │   ├── dto                     # 数据传输对象
│   │   │   ├── entity                  # 实体对象
│   │   │   ├── form                    # 表单对象
│   │   │   ├── query                   # 查询参数对象
│   │   │   └── vo                      # 视图对象
│   │   ├── mapper                      # 数据库访问层
│   │   └── service                     # 业务逻辑层
│   └── YouLaiBootApplication           # 启动类
└── end                             
```
</details>



## 🚀 项目启动

📚 完整流程参考: [项目启动](https://www.youlai.tech/youlai-boot/1.%E9%A1%B9%E7%9B%AE%E5%90%AF%E5%8A%A8/)

1. **克隆项目**

   ```bash
   git clone https://gitee.com/youlaiorg/youlai-boot-flex.git
   ```

2. **数据库初始化**

   **MySQL 数据库：**
   执行 [youlai_boot.sql](sql/mysql/youlai_boot.sql) 脚本完成数据库创建、表结构和基础数据的初始化。

   **PostgreSQL 数据库：**
   手动创建数据库 `tsz_java_admin`，然后根据 MySQL 脚本手动创建对应的 PostgreSQL 表结构和数据。

3. **修改配置**

   项目现在支持 MySQL 和 PostgreSQL 两种数据库：

   **使用 MySQL：**
   - 修改 [application-dev.yml](src/main/resources/application-dev.yml) 中的 MySQL 和 Redis 连接信息
   - 或者使用 Docker Compose 启动 MySQL：`docker-compose up mysql redis`

   **使用 PostgreSQL：**
   - 修改 [application-dev-postgresql.yml](src/main/resources/application-dev-postgresql.yml) 中的 PostgreSQL 和 Redis 连接信息
   - 启动时指定 profile：`--spring.profiles.active=dev-postgresql`
   - 或者使用 Docker Compose 启动 PostgreSQL：`docker-compose up postgresql redis`

4. **启动项目**

   执行 [YoulaiBootApplication.java](src/main/java/com/youlai/boot/YoulaiBootApplication.java) 的 main 方法完成后端项目启动；

   访问接口文档地址 [http://localhost:8989/doc.html](http://localhost:8989/doc.html) 验证项目启动是否成功。


## �️ 数据库支持

项目支持 MySQL 和 PostgreSQL 两种数据库：

### MySQL 配置
- 配置文件：`application-dev.yml` / `application-prod.yml`
- 驱动：`com.mysql.cj.jdbc.Driver`
- 连接示例：`******************************************`

### PostgreSQL 配置
- 配置文件：`application-dev-postgresql.yml` / `application-prod-postgresql.yml`
- 驱动：`org.postgresql.Driver`
- 连接示例：`***********************************************`

### 切换数据库
通过修改 `application.yml` 中的 `spring.profiles.active` 来切换：
- MySQL：`spring.profiles.active=dev`
- PostgreSQL：`spring.profiles.active=dev-postgresql`

### Docker 支持
```bash
# 启动 MySQL + Redis
docker-compose up mysql redis

# 启动 PostgreSQL + Redis
docker-compose up postgresql redis
```

## �🚀 项目部署

参考官方文档: [项目部署指南](https://www.youlai.tech/youlai-boot/5.%E9%A1%B9%E7%9B%AE%E9%83%A8%E7%BD%B2/)
