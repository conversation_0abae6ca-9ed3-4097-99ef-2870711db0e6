<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.RoleMenuMapper">

    <!-- 获取角色拥有的菜单ID集合 -->
    <select id="listMenuIdsByRoleId" resultType="java.lang.Long">
        SELECT
            rm.menu_id
        FROM
            sys_role_menu rm
                INNER JOIN sys_menu m ON rm.menu_id = m.id
        WHERE
            rm.role_id = #{roleId}
    </select>

    <!-- 权限和拥有权限的角色的映射 -->
    <resultMap id="PremRolesMap" type="com.hntsz.boot.system.model.bo.RolePermsBO">
        <result property="roleCode" column="role_code"/>
        <collection property="perms" ofType="string" javaType="java.util.Set">
            <result column="perm"/>
        </collection>
    </resultMap>

    <!-- 获取权限和拥有权限的角色列表 -->
    <select id="getRolePermsList" resultMap="PremRolesMap">
        SELECT
            t3.perm,
            t2.`code` role_code
        FROM
            `sys_role_menu` t1
                INNER JOIN sys_role t2 ON t1.role_id = t2.id AND t2.is_deleted = 0 AND t2.`status` = 1
                INNER JOIN sys_menu t3 ON t1.menu_id = t3.id
        WHERE
            type = '${@<EMAIL>()}'
            <if test="roleCode!=null and roleCode.trim() neq ''">
                AND t2.`code` = #{roleCode}
            </if>
    </select>

    <!-- 获取角色拥有的权限列表 -->
    <select id="listRolePerms" resultType="java.lang.String">
        SELECT
            DISTINCT t2.perm
        FROM
            sys_role_menu t1
                INNER JOIN sys_menu t2 ON t2.id = t1.menu_id
                INNER JOIN sys_role t3 ON t3.id = t1.role_id
        WHERE
            t2.type = '${@<EMAIL>()}'
            AND t2.perm IS NOT NULL
            AND t3.CODE IN
            <foreach collection="roles" item="role" separator="," open="(" close=")">
                #{role}
            </foreach>
    </select>
</mapper>
