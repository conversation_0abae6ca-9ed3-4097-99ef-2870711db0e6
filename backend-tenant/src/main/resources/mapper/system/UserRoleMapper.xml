<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.UserRoleMapper">

    <!-- 根据用户ID获取角色ID集合 -->
    <select id="listRoleIdsByUserId" resultType="java.lang.String">
        SELECT
            "RoleId"
        FROM
            "UserRoles"
        WHERE
            "UserId" = #{userId}::uuid
    </select>

    <!-- 获取角色绑定的用户数 -->
    <select id="countUsersForRole" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            "UserRoles" t1
            INNER JOIN "Roles" t2 ON t1."RoleId" = t2."Id" AND t2."IsDeleted" = false
            INNER JOIN "Users" t3 ON t1."UserId" = t3."Id"
            AND t3."IsArchived" = false
        WHERE
            t1."RoleId" = #{roleId}::uuid
    </select>
</mapper>
