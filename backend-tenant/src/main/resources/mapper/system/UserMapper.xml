<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.UserMapper">

    <!-- 根据用户ID获取用户详情 -->
    <select id="getUserFormData" resultType="com.hntsz.boot.system.model.form.UserForm">
        SELECT
            "Id" as id,
            "Username" as username,
            "Nickname" as nickname,
            "Gender" as gender,
            "Status" as status
        FROM
            "Users"
        WHERE
            "Id" = #{userId}::uuid AND "IsArchived" = false
    </select>

    <!-- 根据用户唯一标识获取用户的认证信息 -->
    <select id="getAuthCredentialsByUniqueUserKey" resultType="com.hntsz.boot.core.security.model.UserAuthCredentials">
        SELECT
            "Id" as userId,
            "UniqueUserKey" as uniqueUserKey,
            "DisplayName" as displayName,
            "PasswordHash" as password,
            "Status" as status,
            "RoleKey" as roleKey,
            "TenantId" as tenantId
        FROM
            "TenantUsers"
        WHERE
            "UniqueUserKey" = #{uniqueUserKey}
    </select>

    <!-- 移除微信openid和手机号登录相关查询，简化认证逻辑 -->

    <!-- 获取用户导出列表 -->
    <select id="listExportUsers" resultType="com.hntsz.boot.system.model.dto.UserExportDTO">
        SELECT
            u."Username" as username,
            u."Nickname" as nickname,
            u."Phone" as mobile,
            u."Email" as email,
            u."Gender" as gender,
            d."Name" AS dept_name,
            u."CreateTime" as create_time
        FROM
            "Users" u
                LEFT JOIN "Depts" d ON u."DeptId" = d."Id"
        <where>
            u."IsArchived" = false
            <if test="!isRoot">
            AND NOT EXISTS (
                SELECT
                    1
                FROM "UserRoles" sur
                    INNER JOIN "Roles" r ON sur."RoleId" = r."Id"
                WHERE
                    sur."UserId" = u."Id"
                    AND r."Code" = '${@com.hntsz.boot.common.constant.SystemConstants@ROOT_ROLE_CODE}'
            )
            </if>
            <if test='keywords!=null and keywords.trim() neq ""'>
                AND (u."Username" LIKE '%' || #{keywords} || '%'
                OR u."Nickname" LIKE '%' || #{keywords} || '%'
                OR u."Phone" LIKE '%' || #{keywords} || '%')
            </if>
            <if test='status!=null'>
                AND u."Status" = #{status}
            </if>
            <if test='deptId!=null'>
                AND (',' || d."TreePath" || ',' || d."Id" || ',') LIKE '%,' || #{deptId} || ',%'
            </if>
        </where>
        GROUP BY u."Id"
    </select>

    <!-- 根据微信openid获取用户的认证信息 -->
    <select id="getAuthCredentialsByOpenId" resultType="com.hntsz.boot.core.security.model.UserAuthCredentials">
        SELECT
            "Id" as userId,
            "UniqueUserKey" as uniqueUserKey,
            "DisplayName" as displayName,
            "Password" as password,
            "Status" as status,
            "RoleKey" as roleKey,
            "TenantId" as tenantId
        FROM
            "TenantUsers"
        WHERE
            "WechatOpenId" = #{openid} AND "IsArchived" = false
    </select>

    <!-- 根据手机号获取用户的认证信息 -->
    <select id="getAuthCredentialsByMobile" resultType="com.hntsz.boot.core.security.model.UserAuthCredentials">
        SELECT
            "Id" as userId,
            "UniqueUserKey" as uniqueUserKey,
            "DisplayName" as displayName,
            "Password" as password,
            "Status" as status,
            "RoleKey" as roleKey,
            "TenantId" as tenantId
        FROM
            "Users"
        WHERE
            "Phone" = #{mobile}
    </select>

    <!-- 更新用户登录信息 -->
    <update id="updateLoginInfo">
        UPDATE "TenantUsers"
        SET
            "LoginCount" = COALESCE("LoginCount", 0) + 1,
            "LastLoginAt" = NOW()
        WHERE
            "Id" = #{userId}::uuid
    </update>

    <select id="getStatisticsInfo" resultType="com.hntsz.boot.system.model.vo.UserStatisticsVO">
        SELECT
            COUNT(*) AS totalCount,
            COUNT(CASE WHEN "Status" = 'Active' THEN 1 END) AS activeCount,
            COUNT(CASE WHEN "Status" = 'Inactive' THEN 1 END) AS inactiveCount,
            COUNT(CASE WHEN 'ADMIN' = ANY(string_to_array("RoleKey", ',')) THEN 1 END) AS adminCount
        FROM
            "TenantUsers"
    </select>
</mapper>
