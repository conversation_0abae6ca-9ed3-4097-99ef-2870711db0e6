<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.DictItemMapper">

    <!-- 获取字典项分页列表 -->
    <select id="getDictItemPage" resultType="com.hntsz.boot.system.model.vo.DictItemPageVO">
        SELECT
            id,
            dict_code,
            label,
            value,
            sort,
            status
        FROM
            sys_dict_item
        <where>
            <if test="queryParams.keywords!=null and queryParams.keywords.trim() neq ''">
               AND (
                value LIKE CONCAT('%', #{queryParams.keywords} ,'%')
                OR
                label LIKE CONCAT('%', #{queryParams.keywords} ,'%')
                )
            </if>
            <if test="queryParams.dictCode!=null and queryParams.dictCode.trim() neq ''">
                AND dict_code = #{queryParams.dictCode}
            </if>
        </where>
    </select>
</mapper>
