<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.sessionuser.mapper.SessionUserMapper">

    <select id="getSessionUserPage" resultType="com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO">
        SELECT
            C."UniqueUserKey" AS "uniqueUserKey",
            COALESCE(stats."sessionCount", 0) AS "sessionCount",
            COALESCE(stats."messageCount", 0) AS "messageCount",
            stats."lastActiveTime",
            C."IpAddress" AS "address"
        FROM
            "ClientIdentities" C
        LEFT JOIN (
            SELECT
                s."ClientIdentityId",
                COUNT(DISTINCT s."Id") AS "sessionCount",
                COUNT(sm."Id") AS "messageCount",
                MAX(CASE WHEN sm."Category" = 'User' THEN sm."CreatedAt" END) AS "lastActiveTime"
            FROM
                "Sessions" s
            LEFT JOIN
                "StoredMessages" sm ON sm."SessionId" = s."Id"
            WHERE
                s."Type" = 'Chat'
            GROUP BY s."ClientIdentityId"
        ) stats ON stats."ClientIdentityId" = C."Id";
    </select>
</mapper>