<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.session.mapper.SessionMapper">

    <select id="getSessionPage" resultType="com.hntsz.boot.modules.session.model.vo.SessionPageVO">
        SELECT
            s."Id" AS "id",
            ap."DisplayName" AS "applicationName",
            ao."Name" AS "agentName",
            ci."UniqueUserKey" AS "uniqueUserKey",
            ci."IpAddress" AS "userIp",
            ci."UserAgent" AS "userAgent",
            COALESCE(sm."messageCount", 0) AS "messageCount",
            ROUND(EXTRACT(EPOCH FROM (s."UpdatedAt" - s."CreatedAt")) / 60) AS "duration",
            s."CreatedAt" AS "startTime"
        FROM
            "Sessions" s
            JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
            JOIN "Applications" ap ON s."ApplicationId" = ap."Id"
            JOIN "ClientIdentities" ci ON s."ClientIdentityId" = ci."Id"
            LEFT JOIN (
                SELECT
                "SessionId",
                COUNT(*) AS "messageCount"
                FROM "StoredMessages"
                GROUP BY "SessionId"
            ) sm ON s."Id" = sm."SessionId"
        WHERE s."Type" = 'Chat'
        <if test="queryParam.searchKeyword != null and queryParam.searchKeyword != ''">
            AND ci."UniqueUserKey" LIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
            OR ap."DisplayName" LIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
            OR ao."Name" LIKE CONCAT('%', #{queryParam.searchKeyword}, '%')
        </if>
        <if test="queryParam.applicationId != null and queryParam.applicationId != ''">
            AND ap."Id" = #{queryParam.applicationId}::uuid
        </if>
    </select>

    <select id="getSessionById" resultType="com.hntsz.boot.modules.session.model.vo.SessionVO">
        SELECT
            s."Id" AS "id",
            ap."DisplayName" AS "applicationName",
            ao."Name" AS "agentName",
            ci."UniqueUserKey" AS "uniqueUserKey",
            ci."IpAddress" AS "userIp",
            COALESCE(sm."messageCount", 0) AS "messageCount",
            ROUND(EXTRACT(EPOCH FROM (s."UpdatedAt" - s."CreatedAt")) / 60) AS "duration",
            s."CreatedAt" AS "startTime"
        FROM
            "Sessions" s
            JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
            JOIN "Applications" ap ON s."ApplicationId" = ap."Id"
            JOIN "ClientIdentities" ci ON s."ClientIdentityId" = ci."Id"
            LEFT JOIN (
                SELECT
                "SessionId",
                COUNT(*) AS "messageCount"
                FROM "StoredMessages"
                GROUP BY "SessionId"
            ) sm ON s."Id" = sm."SessionId"
        WHERE s."Type" = 'Chat' AND s."Id" = #{sessionId}::uuid
    </select>

    <select id="getSessionStatisticInfo"
            resultType="com.hntsz.boot.modules.session.model.vo.SessionStatisticVO">
        SELECT
            (SELECT COUNT(*) FROM "Sessions" WHERE "Type"='Chat') AS totalSessionCount,
            (SELECT ROUND(SUM(EXTRACT(EPOCH FROM ("UpdatedAt" - "CreatedAt")) / 60), 2) FROM "Sessions" WHERE "Type"='Chat') AS avgDuration,
            (SELECT COUNT(*) FROM "Sessions" s JOIN "StoredMessages" sm ON sm."SessionId" = s."Id" WHERE s."Type"='Chat') AS totalMessageCount;
    </select>
</mapper>