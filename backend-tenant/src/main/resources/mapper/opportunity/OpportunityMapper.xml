<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.opportunity.mapper.OpportunityMapper">

    <!-- 基础字段映射 -->
    <resultMap id="OpportunityVOMap" type="com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO">
        <id column="id" property="id" />
        <result column="opportunity_code" property="opportunityCode" />
        <result column="opportunity_name" property="opportunityName" />
        <result column="opportunity_type" property="opportunityType" />
        <result column="opportunity_source" property="opportunitySource" />
        <result column="partner_id" property="partnerId" />
        <result column="partner_name" property="partnerName" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_email" property="contactEmail" />
        <result column="opportunity_stage" property="opportunityStage" />
        <result column="win_probability" property="winProbability" />
        <result column="estimated_amount" property="estimatedAmount" />
        <result column="estimated_close_date" property="estimatedCloseDate" />
        <result column="actual_close_date" property="actualCloseDate" />
        <result column="opportunity_status" property="opportunityStatus" />
        <result column="lost_reason" property="lostReason" />
        <result column="priority" property="priority" />
        <result column="product_interest" property="productInterest" />
        <result column="requirements" property="requirements" />
        <result column="competition_info" property="competitionInfo" />
        <result column="next_action" property="nextAction" />
        <result column="next_follow_date" property="nextFollowDate" />
        <result column="responsible_user_id" property="responsibleUserId" />
        <result column="responsible_user_name" property="responsibleUserName" />
        <result column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="tags" property="tags" />
        <result column="remark" property="remark" />
        <result column="follow_count" property="followCount" />
        <result column="last_follow_date" property="lastFollowDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        o.id,
        o.opportunity_code,
        o.opportunity_name,
        o.opportunity_type,
        o.opportunity_source,
        o.partner_id,
        o.contact_person,
        o.contact_phone,
        o.contact_email,
        o.opportunity_stage,
        o.win_probability,
        o.estimated_amount,
        o.estimated_close_date,
        o.actual_close_date,
        o.opportunity_status,
        o.lost_reason,
        o.priority,
        o.product_interest,
        o.requirements,
        o.competition_info,
        o.next_action,
        o.next_follow_date,
        o.responsible_user_id,
        o.dept_id,
        o.tags,
        o.remark,
        o.create_time,
        o.update_time
    </sql>

    <!-- 关联查询字段 -->
    <sql id="joinColumns">
        <include refid="baseColumns"/>,
        p.partner_name,
        u.nickname as responsible_user_name,
        d.name as dept_name,
        COALESCE(fc.follow_count, 0) as follow_count,
        fc.last_follow_date
    </sql>

    <!-- 基础查询表 -->
    <sql id="baseTables">
        opportunity o
        LEFT JOIN partner p ON o.partner_id = p.id AND p.is_deleted = 0
        LEFT JOIN sys_user u ON o.responsible_user_id = u.id AND u.is_deleted = 0
        LEFT JOIN sys_dept d ON o.dept_id = d.id AND d.is_deleted = 0
        LEFT JOIN (
            SELECT 
                opportunity_id,
                COUNT(*) as follow_count,
                MAX(follow_date) as last_follow_date
            FROM opportunity_follow 
            GROUP BY opportunity_id
        ) fc ON o.id = fc.opportunity_id
    </sql>

    <!-- 查询条件 -->
    <sql id="whereConditions">
        <where>
            o.is_deleted = 0
            <if test="query.opportunityCode != null and query.opportunityCode != ''">
                AND o.opportunity_code LIKE CONCAT('%', #{query.opportunityCode}, '%')
            </if>
            <if test="query.opportunityName != null and query.opportunityName != ''">
                AND o.opportunity_name LIKE CONCAT('%', #{query.opportunityName}, '%')
            </if>
            <if test="query.opportunityType != null and query.opportunityType != ''">
                AND o.opportunity_type = #{query.opportunityType}
            </if>
            <if test="query.opportunitySource != null and query.opportunitySource != ''">
                AND o.opportunity_source = #{query.opportunitySource}
            </if>
            <if test="query.partnerId != null">
                AND o.partner_id = #{query.partnerId}
            </if>
            <if test="query.opportunityStage != null and query.opportunityStage != ''">
                AND o.opportunity_stage = #{query.opportunityStage}
            </if>
            <if test="query.opportunityStatus != null and query.opportunityStatus != ''">
                AND o.opportunity_status = #{query.opportunityStatus}
            </if>
            <if test="query.priority != null and query.priority != ''">
                AND o.priority = #{query.priority}
            </if>
            <if test="query.responsibleUserId != null">
                AND o.responsible_user_id = #{query.responsibleUserId}
            </if>
            <if test="query.deptId != null">
                AND o.dept_id = #{query.deptId}
            </if>
            <if test="query.minEstimatedAmount != null">
                AND o.estimated_amount >= #{query.minEstimatedAmount}
            </if>
            <if test="query.maxEstimatedAmount != null">
                AND o.estimated_amount &lt;= #{query.maxEstimatedAmount}
            </if>
            <if test="query.minWinProbability != null">
                AND o.win_probability >= #{query.minWinProbability}
            </if>
            <if test="query.maxWinProbability != null">
                AND o.win_probability &lt;= #{query.maxWinProbability}
            </if>
            <if test="query.estimatedCloseStartDate != null">
                AND o.estimated_close_date >= #{query.estimatedCloseStartDate}
            </if>
            <if test="query.estimatedCloseEndDate != null">
                AND o.estimated_close_date &lt;= #{query.estimatedCloseEndDate}
            </if>
            <if test="query.actualCloseStartDate != null">
                AND o.actual_close_date >= #{query.actualCloseStartDate}
            </if>
            <if test="query.actualCloseEndDate != null">
                AND o.actual_close_date &lt;= #{query.actualCloseEndDate}
            </if>
            <if test="query.createStartTime != null">
                AND DATE(o.create_time) >= #{query.createStartTime}
            </if>
            <if test="query.createEndTime != null">
                AND DATE(o.create_time) &lt;= #{query.createEndTime}
            </if>
            <if test="query.lostReason != null and query.lostReason != ''">
                AND o.lost_reason = #{query.lostReason}
            </if>
            <if test="query.tags != null and query.tags != ''">
                AND o.tags LIKE CONCAT('%', #{query.tags}, '%')
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND (
                    o.opportunity_name LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.opportunity_code LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.contact_person LIKE CONCAT('%', #{query.keywords}, '%')
                    OR p.partner_name LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.product_interest LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.requirements LIKE CONCAT('%', #{query.keywords}, '%')
                )
            </if>
        </where>
    </sql>

    <!-- 分页查询商机线索 -->
    <select id="selectOpportunityPage" resultMap="OpportunityVOMap">
        SELECT <include refid="joinColumns"/>
        FROM <include refid="baseTables"/>
        <include refid="whereConditions"/>
        ORDER BY o.create_time DESC
    </select>

    <!-- 根据ID查询商机线索详情 -->
    <select id="selectOpportunityById" resultMap="OpportunityVOMap">
        SELECT <include refid="joinColumns"/>
        FROM <include refid="baseTables"/>
        WHERE o.id = #{id} AND o.is_deleted = 0
    </select>

    <!-- 查询所有商机线索选项 -->
    <select id="selectOpportunityOptions" resultMap="OpportunityVOMap">
        SELECT 
            o.id,
            o.opportunity_code,
            o.opportunity_name,
            o.opportunity_status
        FROM opportunity o
        WHERE o.is_deleted = 0 AND o.opportunity_status = 'active'
        ORDER BY o.create_time DESC
    </select>

    <!-- 根据商机编码查询商机线索 -->
    <select id="selectByOpportunityCode" resultType="com.hntsz.boot.modules.opportunity.model.entity.Opportunity">
        SELECT * FROM opportunity 
        WHERE opportunity_code = #{opportunityCode} AND is_deleted = 0
    </select>

    <!-- 根据负责人ID查询商机线索数量 -->
    <select id="countByResponsibleUserId" resultType="int">
        SELECT COUNT(*) FROM opportunity 
        WHERE responsible_user_id = #{responsibleUserId} AND is_deleted = 0
    </select>

    <!-- 根据客户ID查询商机线索数量 -->
    <select id="countByPartnerId" resultType="int">
        SELECT COUNT(*) FROM opportunity 
        WHERE partner_id = #{partnerId} AND is_deleted = 0
    </select>

    <!-- 批量更新商机负责人 -->
    <update id="batchUpdateResponsibleUser">
        UPDATE opportunity 
        SET responsible_user_id = #{responsibleUserId}, 
            update_by = #{updateBy}, 
            update_time = NOW()
        WHERE id IN
        <foreach collection="opportunityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

</mapper>