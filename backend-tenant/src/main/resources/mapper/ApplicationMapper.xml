<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.application.mapper.ApplicationMapper">

    <!-- 应用分页查询结果映射 -->
    <resultMap id="ApplicationPageVOMap" type="com.hntsz.boot.modules.application.vo.ApplicationPageVO">
        <id column="id" property="id" />
        <result column="key" property="key" />
        <result column="display_name" property="name" />
        <result column="description" property="description" />
        <result column="icon" property="icon" />
        <result column="created_at" property="createdAt" />
        <result column="agent_count" property="agentCount" />
        <result column="session_count" property="sessionCount" />
        <result column="last_activity" property="lastActivity" />
        <result column="status" property="status" />
    </resultMap>



    <!-- 获取应用分页列表 -->
    <select id="selectApplicationPage" resultMap="ApplicationPageVOMap">
        SELECT
        a."Id" as id,
        a."Key" as key,
        a."DisplayName" as display_name,
        a."Description" as description,
        a."Icon" as icon,
        a."CreatedAt" as created_at,
        COALESCE(stats.agent_count, 0) as agent_count,
        COALESCE(stats.session_count, 0) as session_count,
        TO_CHAR(COALESCE(stats.last_activity, a."CreatedAt"), 'YYYY-MM-DD HH24:MI:SS') as last_activity,
        CASE
        WHEN COALESCE(stats.agent_count, 0) > 0 THEN 'active'
        ELSE 'inactive'
        END as status
        FROM "Applications" a
        LEFT JOIN (
        SELECT
        s."ApplicationId",
        COUNT(DISTINCT CASE WHEN ao."IsArchived" = false THEN s."AgentOptionId" END) as agent_count,
        COUNT(*) as session_count,
        MAX(s."UpdatedAt") as last_activity
        FROM "Sessions" s
        LEFT JOIN "AgentOptions" ao ON s."AgentOptionId" = ao."Id"
        GROUP BY s."ApplicationId"
        ) stats ON a."Id" = stats."ApplicationId"
        WHERE 1=1
        <if test="searchKeyword != null and searchKeyword != ''">
            AND (
            a."DisplayName" ILIKE CONCAT('%', #{searchKeyword}, '%')
            OR a."Description" ILIKE CONCAT('%', #{searchKeyword}, '%')
            OR a."Key" ILIKE CONCAT('%', #{searchKeyword}, '%')
            )
        </if>
        ORDER BY a."CreatedAt" DESC
    </select>



</mapper>