<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.ContractPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hntsz.boot.modules.contract.model.entity.ContractPayment">
        <id column="id" property="id" />
        <result column="contract_id" property="contractId" />
        <result column="payment_no" property="paymentNo" />
        <result column="payment_type" property="paymentType" />
        <result column="payment_method" property="paymentMethod" />
        <result column="payer_partner_id" property="payerPartnerId" />
        <result column="payee_partner_id" property="payeePartnerId" />
        <result column="planned_amount" property="plannedAmount" />
        <result column="actual_amount" property="actualAmount" />
        <result column="currency" property="currency" />
        <result column="planned_date" property="plannedDate" />
        <result column="actual_date" property="actualDate" />
        <result column="payment_status" property="paymentStatus" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account" property="bankAccount" />
        <result column="transaction_no" property="transactionNo" />
        <result column="voucher_attachment_id" property="voucherAttachmentId" />
        <result column="invoice_status" property="invoiceStatus" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="invoice_amount" property="invoiceAmount" />
        <result column="invoice_date" property="invoiceDate" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_id, payment_no, payment_type, payment_method, payer_partner_id,
        payee_partner_id, planned_amount, actual_amount, currency, planned_date,
        actual_date, payment_status, bank_name, bank_account, transaction_no, 
        voucher_attachment_id, invoice_status, invoice_no, invoice_amount, 
        invoice_date, remark, create_by, create_time, update_by, update_time, is_deleted
    </sql>

    <!-- 获取合同付款记录分页列表 -->
    <select id="getContractPaymentPage" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO">
        SELECT 
            cp.id,
            cp.contract_id,
            c.contract_no,
            c.contract_name,
            cp.payment_no,
            cp.payment_type,
            pt.label AS payment_type_label,
            cp.payment_method,
            pm.label AS payment_method_label,
            cp.payer_partner_id,
            payer.partner_name AS payer_partner_name,
            cp.payee_partner_id,
            payee.partner_name AS payee_partner_name,
            cp.planned_amount,
            cp.actual_amount,
            cp.currency,
            cp.planned_date,
            cp.actual_date,
            cp.payment_status,
            cp.bank_name,
            cp.bank_account,
            cp.transaction_no,
            cp.voucher_attachment_id,
            cp.invoice_status,
            cp.invoice_no,
            cp.invoice_amount,
            cp.invoice_date,
            cp.remark,
            cp.create_time,
            cp.update_time
        FROM contract_payment cp
        LEFT JOIN contract c ON cp.contract_id = c.id
        LEFT JOIN partner payer ON cp.payer_partner_id = payer.id
        LEFT JOIN partner payee ON cp.payee_partner_id = payee.id
        LEFT JOIN sys_dict_item pt ON cp.payment_type = pt.value AND pt.dict_code = 'payment_type'
        LEFT JOIN sys_dict_item pm ON cp.payment_method = pm.value AND pm.dict_code = 'payment_method'
        WHERE cp.is_deleted = 0
        <if test="query != null">
            <if test="query.keywords != null and query.keywords != ''">
                AND (cp.payment_no LIKE CONCAT('%', #{query.keywords}, '%')
                    OR cp.transaction_no LIKE CONCAT('%', #{query.keywords}, '%'))
            </if>
            <if test="query.contractId != null">
                AND cp.contract_id = #{query.contractId}
            </if>
            <if test="query.paymentType != null and query.paymentType != ''">
                AND cp.payment_type = #{query.paymentType}
            </if>
            <if test="query.paymentMethod != null and query.paymentMethod != ''">
                AND cp.payment_method = #{query.paymentMethod}
            </if>
            <if test="query.payerPartnerId != null">
                AND cp.payer_partner_id = #{query.payerPartnerId}
            </if>
            <if test="query.payeePartnerId != null">
                AND cp.payee_partner_id = #{query.payeePartnerId}
            </if>
            <if test="query.paymentStatus != null and query.paymentStatus != ''">
                AND cp.payment_status = #{query.paymentStatus}
            </if>
            <if test="query.invoiceStatus != null and query.invoiceStatus != ''">
                AND cp.invoice_status = #{query.invoiceStatus}
            </if>
            <if test="query.plannedDateStart != null">
                AND cp.planned_date &gt;= #{query.plannedDateStart}
            </if>
            <if test="query.plannedDateEnd != null">
                AND cp.planned_date &lt;= #{query.plannedDateEnd}
            </if>
            <if test="query.actualDateStart != null">
                AND cp.actual_date &gt;= #{query.actualDateStart}
            </if>
            <if test="query.actualDateEnd != null">
                AND cp.actual_date &lt;= #{query.actualDateEnd}
            </if>
            <if test="query.plannedAmountMin != null">
                AND cp.planned_amount &gt;= #{query.plannedAmountMin}
            </if>
            <if test="query.plannedAmountMax != null">
                AND cp.planned_amount &lt;= #{query.plannedAmountMax}
            </if>
            <if test="query.actualAmountMin != null">
                AND cp.actual_amount &gt;= #{query.actualAmountMin}
            </if>
            <if test="query.actualAmountMax != null">
                AND cp.actual_amount &lt;= #{query.actualAmountMax}
            </if>
            <if test="query.currency != null and query.currency != ''">
                AND cp.currency = #{query.currency}
            </if>
        </if>
        ORDER BY cp.create_time DESC
    </select>

    <!-- 获取合同付款记录详情 -->
    <select id="getContractPaymentDetail" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO">
        SELECT 
            cp.id,
            cp.contract_id,
            c.contract_no,
            c.contract_name,
            cp.payment_no,
            cp.payment_type,
            pt.label AS payment_type_label,
            cp.payment_method,
            pm.label AS payment_method_label,
            cp.payer_partner_id,
            payer.partner_name AS payer_partner_name,
            cp.payee_partner_id,
            payee.partner_name AS payee_partner_name,
            cp.planned_amount,
            cp.actual_amount,
            cp.currency,
            cp.planned_date,
            cp.actual_date,
            cp.payment_status,
            cp.bank_name,
            cp.bank_account,
            cp.transaction_no,
            cp.voucher_attachment_id,
            cp.invoice_status,
            cp.invoice_no,
            cp.invoice_amount,
            cp.invoice_date,
            cp.remark,
            cp.create_time,
            cp.update_time
        FROM contract_payment cp
        LEFT JOIN contract c ON cp.contract_id = c.id
        LEFT JOIN partner payer ON cp.payer_partner_id = payer.id
        LEFT JOIN partner payee ON cp.payee_partner_id = payee.id
        LEFT JOIN sys_dict_item pt ON cp.payment_type = pt.value AND pt.dict_code = 'payment_type'
        LEFT JOIN sys_dict_item pm ON cp.payment_method = pm.value AND pm.dict_code = 'payment_method'
        WHERE cp.id = #{id} AND cp.is_deleted = 0
    </select>

    <!-- 根据合同ID获取付款记录列表 -->
    <select id="getByContractId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO">
        SELECT 
            cp.id,
            cp.contract_id,
            cp.payment_no,
            cp.payment_type,
            pt.label AS payment_type_label,
            cp.planned_amount,
            cp.actual_amount,
            cp.currency,
            cp.planned_date,
            cp.actual_date,
            cp.payment_status,
            cp.invoice_status,
            payer.partner_name AS payer_partner_name,
            payee.partner_name AS payee_partner_name
        FROM contract_payment cp
        LEFT JOIN partner payer ON cp.payer_partner_id = payer.id
        LEFT JOIN partner payee ON cp.payee_partner_id = payee.id
        LEFT JOIN sys_dict_item pt ON cp.payment_type = pt.value AND pt.dict_code = 'payment_type'
        WHERE cp.contract_id = #{contractId} AND cp.is_deleted = 0
        ORDER BY cp.planned_date ASC, cp.create_time ASC
    </select>

    <!-- 根据付款单号查询付款记录 -->
    <select id="getByPaymentNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM contract_payment
        WHERE payment_no = #{paymentNo} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据合同ID统计付款总额 -->
    <select id="getTotalAmountByContractId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(actual_amount), 0)
        FROM contract_payment
        WHERE contract_id = #{contractId} AND is_deleted = 0
    </select>

    <!-- 根据合同ID和付款状态统计付款金额 -->
    <select id="getAmountByContractIdAndStatus" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(actual_amount), 0)
        FROM contract_payment
        WHERE contract_id = #{contractId} AND payment_status = #{paymentStatus} AND is_deleted = 0
    </select>

    <!-- 根据伙伴ID获取付款记录（作为付款方） -->
    <select id="getByPayerPartnerId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO">
        SELECT 
            cp.id,
            cp.contract_id,
            c.contract_no,
            c.contract_name,
            cp.payment_no,
            cp.payment_type,
            pt.label AS payment_type_label,
            cp.planned_amount,
            cp.actual_amount,
            cp.currency,
            cp.planned_date,
            cp.actual_date,
            cp.payment_status,
            payee.partner_name AS payee_partner_name
        FROM contract_payment cp
        LEFT JOIN contract c ON cp.contract_id = c.id
        LEFT JOIN partner payee ON cp.payee_partner_id = payee.id
        LEFT JOIN sys_dict_item pt ON cp.payment_type = pt.value AND pt.dict_code = 'payment_type'
        WHERE cp.payer_partner_id = #{partnerId} AND cp.is_deleted = 0
        ORDER BY cp.create_time DESC
    </select>

    <!-- 根据伙伴ID获取付款记录（作为收款方） -->
    <select id="getByPayeePartnerId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO">
        SELECT 
            cp.id,
            cp.contract_id,
            c.contract_no,
            c.contract_name,
            cp.payment_no,
            cp.payment_type,
            pt.label AS payment_type_label,
            cp.planned_amount,
            cp.actual_amount,
            cp.currency,
            cp.planned_date,
            cp.actual_date,
            cp.payment_status,
            payer.partner_name AS payer_partner_name
        FROM contract_payment cp
        LEFT JOIN contract c ON cp.contract_id = c.id
        LEFT JOIN partner payer ON cp.payer_partner_id = payer.id
        LEFT JOIN sys_dict_item pt ON cp.payment_type = pt.value AND pt.dict_code = 'payment_type'
        WHERE cp.payee_partner_id = #{partnerId} AND cp.is_deleted = 0
        ORDER BY cp.create_time DESC
    </select>

</mapper>
