package com.hntsz.boot.modules.contract.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.contract.mapper.ContractPaymentMapper;
import com.hntsz.boot.modules.contract.model.entity.ContractPayment;
import com.hntsz.boot.modules.contract.model.form.ContractPaymentForm;
import com.hntsz.boot.modules.contract.model.query.ContractPaymentQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO;
import com.hntsz.boot.modules.contract.service.ContractPaymentService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 合同付款记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class ContractPaymentServiceImpl extends ServiceImpl<ContractPaymentMapper, ContractPayment> implements ContractPaymentService {

    private final ContractPaymentMapper contractPaymentMapper;

    @Override
    public IPage<ContractPaymentVO> getContractPaymentPage(ContractPaymentQuery query) {
        Page<ContractPaymentVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        return contractPaymentMapper.getContractPaymentPage(page, query);
    }

    @Override
    public ContractPaymentVO getContractPaymentDetail(Long id) {
        return contractPaymentMapper.getContractPaymentDetail(id);
    }

    @Override
    public ContractPaymentForm getContractPaymentFormData(Long id) {
        ContractPayment entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        
        ContractPaymentForm form = new ContractPaymentForm();
        BeanUtils.copyProperties(entity, form);
        return form;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveContractPayment(ContractPaymentForm form) {
        // 检查付款单号是否重复
        if (StrUtil.isNotBlank(form.getPaymentNo()) && existsByPaymentNo(form.getPaymentNo(), null)) {
            throw new RuntimeException("付款单号已存在");
        }
        
        ContractPayment entity = new ContractPayment();
        BeanUtils.copyProperties(form, entity);
        
        // 设置默认状态
        if (StrUtil.isBlank(entity.getPaymentStatus())) {
            entity.setPaymentStatus("pending");
        }
        if (StrUtil.isBlank(entity.getInvoiceStatus())) {
            entity.setInvoiceStatus("not_issued");
        }
        if (StrUtil.isBlank(entity.getCurrency())) {
            entity.setCurrency("CNY");
        }
        
        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContractPayment(Long id, ContractPaymentForm form) {
        ContractPayment existingEntity = this.getById(id);
        if (existingEntity == null) {
            throw new RuntimeException("合同付款记录不存在");
        }
        
        // 检查付款单号是否重复
        if (StrUtil.isNotBlank(form.getPaymentNo()) && existsByPaymentNo(form.getPaymentNo(), id)) {
            throw new RuntimeException("付款单号已存在");
        }
        
        ContractPayment entity = new ContractPayment();
        BeanUtils.copyProperties(form, entity);
        entity.setId(id);
        
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContractPayments(Long[] ids) {
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public List<ContractPaymentVO> getByContractId(Long contractId) {
        return contractPaymentMapper.getByContractId(contractId);
    }

    @Override
    public ContractPayment getByPaymentNo(String paymentNo) {
        return contractPaymentMapper.getByPaymentNo(paymentNo);
    }

    @Override
    public boolean existsByPaymentNo(String paymentNo, Long excludeId) {
        if (StrUtil.isBlank(paymentNo)) {
            return false;
        }
        
        LambdaQueryWrapper<ContractPayment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractPayment::getPaymentNo, paymentNo);
        if (excludeId != null) {
            wrapper.ne(ContractPayment::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public BigDecimal getTotalAmountByContractId(Long contractId) {
        return contractPaymentMapper.getTotalAmountByContractId(contractId);
    }

    @Override
    public BigDecimal getAmountByContractIdAndStatus(Long contractId, String paymentStatus) {
        return contractPaymentMapper.getAmountByContractIdAndStatus(contractId, paymentStatus);
    }

    @Override
    public List<ContractPaymentVO> getByPayerPartnerId(Long partnerId) {
        return contractPaymentMapper.getByPayerPartnerId(partnerId);
    }

    @Override
    public List<ContractPaymentVO> getByPayeePartnerId(Long partnerId) {
        return contractPaymentMapper.getByPayeePartnerId(partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePaymentStatus(Long id, String status) {
        ContractPayment entity = new ContractPayment();
        entity.setId(id);
        entity.setPaymentStatus(status);
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInvoiceStatus(Long id, String invoiceStatus) {
        ContractPayment entity = new ContractPayment();
        entity.setId(id);
        entity.setInvoiceStatus(invoiceStatus);
        return this.updateById(entity);
    }
}
