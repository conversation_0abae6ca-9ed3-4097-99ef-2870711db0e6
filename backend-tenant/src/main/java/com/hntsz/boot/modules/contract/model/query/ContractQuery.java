package com.hntsz.boot.modules.contract.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同主表查询对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同主表查询对象")
public class ContractQuery extends BasePageQuery {

    /**
     * 搜索关键字(合同编号、合同名称)
     */
    @Schema(description = "搜索关键字")
    private String keywords;

    /**
     * 合同类型
     */
    @Schema(description = "合同类型")
    private String contractType;

    /**
     * 合同分类
     */
    @Schema(description = "合同分类")
    private String contractCategory;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private Long deptId;

    /**
     * 签署日期范围开始
     */
    @Schema(description = "签署日期范围开始")
    private LocalDate signingDateStart;

    /**
     * 签署日期范围结束
     */
    @Schema(description = "签署日期范围结束")
    private LocalDate signingDateEnd;

    /**
     * 生效日期范围开始
     */
    @Schema(description = "生效日期范围开始")
    private LocalDate effectiveDateStart;

    /**
     * 生效日期范围结束
     */
    @Schema(description = "生效日期范围结束")
    private LocalDate effectiveDateEnd;

    /**
     * 到期日期范围开始
     */
    @Schema(description = "到期日期范围开始")
    private LocalDate expiryDateStart;

    /**
     * 到期日期范围结束
     */
    @Schema(description = "到期日期范围结束")
    private LocalDate expiryDateEnd;

    /**
     * 合同金额最小值
     */
    @Schema(description = "合同金额最小值")
    private BigDecimal contractAmountMin;

    /**
     * 合同金额最大值
     */
    @Schema(description = "合同金额最大值")
    private BigDecimal contractAmountMax;

    /**
     * 伙伴ID
     */
    @Schema(description = "伙伴ID")
    private Long partnerId;

    /**
     * 商机ID
     */
    @Schema(description = "商机ID")
    private Long opportunityId;
}
