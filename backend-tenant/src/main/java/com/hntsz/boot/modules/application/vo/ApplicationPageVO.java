package com.hntsz.boot.modules.application.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;


/**
 * 应用分页列表视图对象
 */
@Data
@Schema(description = "应用分页列表视图对象")
public class ApplicationPageVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 应用唯一标识
     */
    @Schema(description = "应用唯一标识")
    private String key;

    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    private String name;

    /**
     * 应用描述
     */
    @Schema(description = "应用描述")
    private String description;

    /**
     * 应用状态
     */
    @Schema(description = "应用状态")
    private String status;

    /**
     * 应用图标URL
     */
    @Schema(description = "应用图标URL")
    private String icon;

    /**
     * 智能体数量
     */
    @Schema(description = "智能体数量")
    private Integer agentCount;

    /**
     * 会话数量
     */
    @Schema(description = "会话数量")
    private Integer sessionCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp createdAt;

    /**
     * 最后活动时间
     */
    @Schema(description = "最后活动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String lastActivity;
}