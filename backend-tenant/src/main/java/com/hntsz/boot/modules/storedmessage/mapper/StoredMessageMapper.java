package com.hntsz.boot.modules.storedmessage.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.storedmessage.model.entity.StoredMessage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

@Mapper
public interface StoredMessageMapper extends BaseMapper<StoredMessage> {

    default List<StoredMessage> getSessionMessageList(UUID sessionId) {
        return selectList(new LambdaQueryWrapper<StoredMessage>()
                .eq(StoredMessage::getSessionId, sessionId)
                .orderByAsc(StoredMessage::getCreatedAt)
        );
    };

    default Page<StoredMessage> getSessionMessagePage(Page<StoredMessage> page, UUID sessionId) {
        return selectPage(page, new LambdaQueryWrapper<StoredMessage>()
                .select(StoredMessage::getId, StoredMessage::getCategory, StoredMessage::getContent, StoredMessage::getCreatedAt)
                .eq(StoredMessage::getSessionId, sessionId)
                .orderByAsc(StoredMessage::getCreatedAt)
        );
    };

}
