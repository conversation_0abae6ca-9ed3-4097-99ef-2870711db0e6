package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.contract.model.entity.Contract;
import com.hntsz.boot.modules.contract.model.form.ContractForm;
import com.hntsz.boot.modules.contract.model.query.ContractQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;

import java.util.List;

/**
 * 合同主表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface ContractService extends IService<Contract> {

    /**
     * 获取合同分页列表
     *
     * @param query 查询参数
     * @return 合同分页列表
     */
    IPage<ContractVO> getContractPage(ContractQuery query);

    /**
     * 获取合同详情
     *
     * @param id 合同ID
     * @return 合同详情
     */
    ContractVO getContractDetail(Long id);

    /**
     * 获取合同表单数据
     *
     * @param id 合同ID
     * @return 合同表单数据
     */
    ContractForm getContractFormData(Long id);

    /**
     * 新增合同
     *
     * @param form 合同表单数据
     * @return 合同ID
     */
    Long saveContract(ContractForm form);

    /**
     * 更新合同
     *
     * @param id 合同ID
     * @param form 合同表单数据
     * @return 是否成功
     */
    boolean updateContract(Long id, ContractForm form);

    /**
     * 删除合同
     *
     * @param ids 合同ID数组
     * @return 是否成功
     */
    boolean deleteContracts(Long[] ids);

    /**
     * 获取合同选项列表
     *
     * @return 合同选项列表
     */
    List<ContractVO> getContractOptions();

    /**
     * 根据合同编号查询合同
     *
     * @param contractNo 合同编号
     * @return 合同
     */
    Contract getByContractNo(String contractNo);

    /**
     * 检查合同编号是否存在
     *
     * @param contractNo 合同编号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByContractNo(String contractNo, Long excludeId);

    /**
     * 根据伙伴ID获取相关合同列表
     *
     * @param partnerId 伙伴ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByPartnerId(Long partnerId);

    /**
     * 根据负责人ID获取合同列表
     *
     * @param responsibleUserId 负责人ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByResponsibleUserId(Long responsibleUserId);

    /**
     * 根据部门ID获取合同列表
     *
     * @param deptId 部门ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByDeptId(Long deptId);

    /**
     * 更新合同状态
     *
     * @param id 合同ID
     * @param status 合同状态
     * @return 是否成功
     */
    boolean updateContractStatus(Long id, String status);
}
