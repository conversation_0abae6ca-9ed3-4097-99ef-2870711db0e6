package com.hntsz.boot.modules.storedmessage.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.session.model.query.SessionMessageQuery;
import com.hntsz.boot.modules.storedmessage.converter.StoredMessageConverter;
import com.hntsz.boot.modules.storedmessage.mapper.StoredMessageMapper;
import com.hntsz.boot.modules.storedmessage.model.entity.StoredMessage;
import com.hntsz.boot.modules.storedmessage.model.vo.StoredMessageVO;
import com.hntsz.boot.modules.storedmessage.service.StoredMessageService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StoredMessageServiceImpl implements StoredMessageService {

    private final StoredMessageMapper storedMessageMapper;

    @Override
    public List<StoredMessageVO> getSessionMessageList(String sessionId) {
        List<StoredMessage> messageList = storedMessageMapper.getSessionMessageList(UUID.fromString(sessionId));
        List<StoredMessage> resultList = messageList.stream()
                .filter(message -> !message.getCategory().equals("System") && !StringUtils.isBlank(message.getContent()))
                .toList();
        return StoredMessageConverter.INSTANCE.toVoList(resultList);
    }

    @Override
    public Page<StoredMessageVO> getSessionMessagePage(SessionMessageQuery queryParam) {

        Page<StoredMessage> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        Page<StoredMessage> messagePage = storedMessageMapper.getSessionMessagePage(page, UUID.fromString(queryParam.getSessionId()));

        return StoredMessageConverter.INSTANCE.toVoPage(messagePage);
    }
}
