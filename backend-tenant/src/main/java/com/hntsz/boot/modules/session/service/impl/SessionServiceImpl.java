package com.hntsz.boot.modules.session.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.session.mapper.SessionMapper;
import com.hntsz.boot.modules.session.model.query.SessionPageQuery;
import com.hntsz.boot.modules.session.model.vo.SessionPageVO;
import com.hntsz.boot.modules.session.model.vo.SessionStatisticVO;
import com.hntsz.boot.modules.session.model.vo.SessionVO;
import com.hntsz.boot.modules.session.service.SessionService;
import com.hntsz.boot.modules.storedmessage.service.StoredMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@RequiredArgsConstructor
public class SessionServiceImpl implements SessionService {

    private final SessionMapper sessionMapper;

    private final StoredMessageService storedMessageService;

    @Override
    public IPage<SessionPageVO> getSessionPage(SessionPageQuery queryParam) {
        Page<SessionPageVO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        return sessionMapper.getSessionPage(page, queryParam);
    }

    @Override
    public SessionVO getSessionById(String sessionId) {
        return sessionMapper.getSessionById(sessionId);
    }

    @Override
    public SessionStatisticVO getSessionStatisticInfo() {
        SessionStatisticVO statisticInfo = sessionMapper.getSessionStatisticInfo();

        long totalSessionCount = statisticInfo.getTotalSessionCount();
        if (totalSessionCount > 0) {
            BigDecimal totalDuration = statisticInfo.getAvgDuration();
            BigDecimal avgDuration = totalDuration
                    .divide(BigDecimal.valueOf(totalSessionCount), 1, RoundingMode.HALF_UP);
            statisticInfo.setAvgDuration(avgDuration);
        } else {
            statisticInfo.setAvgDuration(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
        }
        return statisticInfo;
    }
}
