package com.hntsz.boot.modules.contract.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.contract.mapper.ContractMapper;
import com.hntsz.boot.modules.contract.model.entity.Contract;
import com.hntsz.boot.modules.contract.model.form.ContractForm;
import com.hntsz.boot.modules.contract.model.query.ContractQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import com.hntsz.boot.modules.contract.service.ContractAttachmentService;
import com.hntsz.boot.modules.contract.service.ContractPartnerRelationService;
import com.hntsz.boot.modules.contract.service.ContractService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 合同主表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements ContractService {

    private final ContractMapper contractMapper;
    private final ContractPartnerRelationService contractPartnerRelationService;
    private final ContractAttachmentService contractAttachmentService;

    @Override
    public IPage<ContractVO> getContractPage(ContractQuery query) {
        Page<ContractVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<ContractVO> result = contractMapper.getContractPage(page, query);
        
        // 批量加载参与方信息以避免N+1查询问题
        if (!result.getRecords().isEmpty()) {
            List<Long> contractIds = result.getRecords().stream()
                    .map(ContractVO::getId)
                    .toList();
            
            // 批量查询所有合同的伙伴信息
            Map<Long, List<ContractPartnerRelationVO>> partnerMap = contractPartnerRelationService
                    .getByContractIds(contractIds);
            
            // 设置每个合同的伙伴信息
            for (ContractVO contract : result.getRecords()) {
                contract.setParties(partnerMap.getOrDefault(contract.getId(), new ArrayList<>()));
            }
        }
        
        return result;
    }

    @Override
    public ContractVO getContractDetail(Long id) {
        ContractVO contractVO = contractMapper.getContractDetail(id);
        if (contractVO != null) {
            // 获取合同伙伴信息
            contractVO.setParties(contractPartnerRelationService.getByContractId(id));
            // 获取合同文件信息
            contractVO.setAttachments(contractAttachmentService.getByContractId(id));
        }
        return contractVO;
    }

    @Override
    public ContractForm getContractFormData(Long id) {
        Contract entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        
        ContractForm form = new ContractForm();
        BeanUtils.copyProperties(entity, form);
        
        // 获取合同伙伴关联信息
        form.setParties(contractPartnerRelationService.getByContractId(id)
                .stream()
                .map(vo -> {
                    var relationForm = new com.hntsz.boot.modules.contract.model.form.ContractPartnerRelationForm();
                    BeanUtils.copyProperties(vo, relationForm);
                    return relationForm;
                })
                .toList());
        
        return form;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveContract(ContractForm form) {
        // 检查合同编号是否重复
        if (existsByContractNo(form.getContractNo(), null)) {
            throw new RuntimeException("合同编号已存在");
        }
        
        Contract entity = new Contract();
        BeanUtils.copyProperties(form, entity);
        
        // 设置默认状态
        if (StrUtil.isBlank(entity.getContractStatus())) {
            entity.setContractStatus("draft");
        }
        
        this.save(entity);
        
        // 保存合同伙伴关联信息
        if (CollUtil.isNotEmpty(form.getParties())) {
            contractPartnerRelationService.batchSaveContractPartnerRelations(entity.getId(), form.getParties());
        }
        
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContract(Long id, ContractForm form) {
        Contract existingEntity = this.getById(id);
        if (existingEntity == null) {
            throw new RuntimeException("合同不存在");
        }
        
        // 检查合同编号是否重复
        if (existsByContractNo(form.getContractNo(), id)) {
            throw new RuntimeException("合同编号已存在");
        }
        
        Contract entity = new Contract();
        BeanUtils.copyProperties(form, entity);
        entity.setId(id);
        
        boolean result = this.updateById(entity);
        
        // 更新合同伙伴关联信息
        if (result) {
            // 先删除原有关联
            contractPartnerRelationService.deleteByContractId(id);

            // 重新保存关联信息
            if (CollUtil.isNotEmpty(form.getParties())) {
                contractPartnerRelationService.batchSaveContractPartnerRelations(id, form.getParties());
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContracts(Long[] ids) {
        // 删除合同伙伴关联信息
        for (Long id : ids) {
            contractPartnerRelationService.deleteByContractId(id);
        }
        
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public List<ContractVO> getContractOptions() {
        return contractMapper.getContractOptions();
    }

    @Override
    public Contract getByContractNo(String contractNo) {
        return contractMapper.getByContractNo(contractNo);
    }

    @Override
    public boolean existsByContractNo(String contractNo, Long excludeId) {
        LambdaQueryWrapper<Contract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Contract::getContractNo, contractNo);
        if (excludeId != null) {
            wrapper.ne(Contract::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public List<ContractVO> getContractsByPartnerId(Long partnerId) {
        return contractMapper.getContractsByPartnerId(partnerId);
    }

    @Override
    public List<ContractVO> getContractsByResponsibleUserId(Long responsibleUserId) {
        return contractMapper.getContractsByResponsibleUserId(responsibleUserId);
    }

    @Override
    public List<ContractVO> getContractsByDeptId(Long deptId) {
        return contractMapper.getContractsByDeptId(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContractStatus(Long id, String status) {
        Contract entity = new Contract();
        entity.setId(id);
        entity.setContractStatus(status);
        return this.updateById(entity);
    }
}
