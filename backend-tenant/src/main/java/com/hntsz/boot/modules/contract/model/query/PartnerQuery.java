package com.hntsz.boot.modules.contract.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务伙伴查询对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "业务伙伴查询对象")
public class PartnerQuery extends BasePageQuery {

    /**
     * 搜索关键字(伙伴名称、编码、联系人)
     */
    @Schema(description = "搜索关键字")
    private String keywords;

    /**
     * 伙伴类型
     */
    @Schema(description = "伙伴类型")
    private String partnerType;

    /**
     * 是否我司或旗下企业
     */
    @Schema(description = "是否我司或旗下企业")
    private Boolean isOurCompany;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型")
    private String certificateType;
}
