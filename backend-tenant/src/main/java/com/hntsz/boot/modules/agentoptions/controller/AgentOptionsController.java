package com.hntsz.boot.modules.agentoptions.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.agentoptions.form.AgentOptionsForm;
import com.hntsz.boot.modules.agentoptions.query.AgentOptionsPageQuery;
import com.hntsz.boot.modules.agentoptions.service.AgentOptionsService;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsDetailVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsPageVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsStatVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 智能体选项控制器
 *
 * <AUTHOR>
 * @since 2024-01-20
 */
@Tag(name = "智能体管理")
@RestController
@RequestMapping("/api/v1/agent-options")
@RequiredArgsConstructor
@Validated
public class AgentOptionsController {

    private final AgentOptionsService agentOptionsService;

    @Operation(summary = "分页查询智能体选项")
    @GetMapping("/page")
//    @PreAuthorize("@ss.hasPerm('agent:options:query')")
    public Result<IPage<AgentOptionsPageVO>> getAgentOptionsPage(AgentOptionsPageQuery pageQuery) {
        return Result.success(agentOptionsService.getAgentOptionsPage(pageQuery));
    }

    @Operation(summary = "获取所有智能体选项")
    @GetMapping("/all")
//    @PreAuthorize("@ss.hasPerm('agent:options:query')")
    public Result<List<AgentOptionsPageVO>> getAllAgentOptions() {
        return Result.success(agentOptionsService.getAllAgentOptions());
    }

    @Operation(summary = "获取智能体选项详情")
    @GetMapping("/{id}")
//    @PreAuthorize("@ss.hasPerm('agent:options:query')")
    public Result<AgentOptionsDetailVO> getAgentOptionsDetail(
            @Parameter(description = "智能体选项ID") @PathVariable UUID id) {
        return Result.success(agentOptionsService.getAgentOptionsDetail(id));
    }

    @Operation(summary = "新增智能体选项")
    @PostMapping
//    @PreAuthorize("@ss.hasPerm('agent:options:add')")
    public Result<Void> createAgentOptions(@Valid @RequestBody AgentOptionsForm form) {
        return Result.judge(agentOptionsService.saveAgentOptions(form));
    }

    @Operation(summary = "更新智能体选项")
    @PutMapping("/{id}")
//    @PreAuthorize("@ss.hasPerm('agent:options:edit')")
    public Result<Void> updateAgentOptions(
            @Parameter(description = "智能体选项ID") @PathVariable UUID id,
            @Valid @RequestBody AgentOptionsForm form) {
        form.setId(id);
        return Result.judge(agentOptionsService.updateAgentOptions(form));
    }

    @Operation(summary = "设置智能体选项归档状态")
    @PutMapping("/{id}/archive")
//    @PreAuthorize("hasAuthority('agentoptions:archive')")
    public Result<Void> setArchiveStatus(
            @Parameter(description = "智能体选项ID") @PathVariable UUID id,
            @Parameter(description = "是否归档") @RequestParam boolean isArchived) {
        return Result.judge(agentOptionsService.setArchiveStatus(id, isArchived));
    }

    @Operation(summary = "获取统计信息")
    @GetMapping("/statistics")
//    @PreAuthorize("@ss.hasPerm('agent:options:query')")
    public Result<AgentOptionsStatVO> getStatistics() {
        return Result.success(agentOptionsService.getStatistics());
    }
}