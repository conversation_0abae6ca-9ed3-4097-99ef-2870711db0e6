package com.hntsz.boot.modules.session.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.session.model.query.SessionMessageQuery;
import com.hntsz.boot.modules.session.model.query.SessionPageQuery;
import com.hntsz.boot.modules.session.model.vo.SessionPageVO;
import com.hntsz.boot.modules.session.model.vo.SessionStatisticVO;
import com.hntsz.boot.modules.session.model.vo.SessionVO;
import com.hntsz.boot.modules.session.service.SessionService;
import com.hntsz.boot.modules.storedmessage.model.vo.StoredMessageVO;
import com.hntsz.boot.modules.storedmessage.service.StoredMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "会话管理")
@RestController
@RequestMapping("/api/vi/session")
@RequiredArgsConstructor
public class SessionController {

    private final SessionService sessionService;

    private final StoredMessageService storedMessageService;


    @GetMapping("/page")
    @Operation(summary = "会话分页查询")
    public PageResult<SessionPageVO> getSessionPage(SessionPageQuery queryParam) {
        IPage<SessionPageVO> sessionPage = sessionService.getSessionPage(queryParam);
        return PageResult.success(sessionPage);
    }

    @GetMapping
    @Operation(summary = "查询会话详情")
    public Result<SessionVO> getSessionDetail(@RequestParam String sessionId) {
        return Result.success(sessionService.getSessionById(sessionId));
    }

    @GetMapping("/message")
    @Operation(summary = "获取会话对话记录")
    public PageResult<StoredMessageVO> getSessionMessage(SessionMessageQuery queryParam) {
        IPage<StoredMessageVO> sessionMessagePage = storedMessageService.getSessionMessagePage(queryParam);
        return PageResult.success(sessionMessagePage);
    }

    @GetMapping("/statistic")
    @Operation(summary = "会话统计")
    public Result<SessionStatisticVO> getSessionStatisticInfo() {
        return Result.success(sessionService.getSessionStatisticInfo());
    }
}
