package com.hntsz.boot.modules.example.model.entity;

import com.hntsz.boot.common.base.BaseEntityExtra;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.contract.ISortOrder;
import com.hntsz.boot.common.contract.ITopOrder;
@TableName("example")
@Data
@EqualsAndHashCode(callSuper = true)
public class Example extends BaseEntityExtra implements ISortOrder, ITopOrder {
    
    private String name;

    private Integer age;

    private Integer sortOrder;

    private Boolean isTop;
    
}
