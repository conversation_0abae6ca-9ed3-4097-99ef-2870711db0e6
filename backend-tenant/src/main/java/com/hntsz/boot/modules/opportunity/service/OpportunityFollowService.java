package com.hntsz.boot.modules.opportunity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.modules.opportunity.model.entity.OpportunityFollow;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityFollowForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityFollowQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO;

import java.util.List;

/**
 * 商机跟进记录服务接口
 *
 * <AUTHOR>
 */
public interface OpportunityFollowService extends IService<OpportunityFollow> {

    /**
     * 分页查询商机跟进记录
     *
     * @param queryParams 查询参数
     * @return 跟进记录分页列表
     */
    PageResult<OpportunityFollowVO> getFollowPage(OpportunityFollowQuery queryParams);

    /**
     * 根据ID查询跟进记录详情
     *
     * @param id 跟进记录ID
     * @return 跟进记录详情
     */
    OpportunityFollowVO getFollowById(Long id);

    /**
     * 根据ID获取跟进记录表单数据
     *
     * @param id 跟进记录ID
     * @return 跟进记录表单数据
     */
    OpportunityFollowForm getFollowFormData(Long id);

    /**
     * 新增跟进记录
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveFollow(OpportunityFollowForm formData);

    /**
     * 更新跟进记录
     *
     * @param id       跟进记录ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateFollow(Long id, OpportunityFollowForm formData);

    /**
     * 删除跟进记录
     *
     * @param ids 跟进记录ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteFollows(String ids);

    /**
     * 根据商机ID查询跟进记录列表
     *
     * @param opportunityId 商机ID
     * @return 跟进记录列表
     */
    List<OpportunityFollowVO> getFollowsByOpportunityId(Long opportunityId);

    /**
     * 根据商机ID删除所有跟进记录
     *
     * @param opportunityId 商机ID
     * @return 是否成功
     */
    boolean deleteByOpportunityId(Long opportunityId);
}