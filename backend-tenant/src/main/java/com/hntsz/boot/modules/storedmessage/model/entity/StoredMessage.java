package com.hntsz.boot.modules.storedmessage.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@TableName("\"StoredMessages\"")
public class StoredMessage {

    @TableId(value = "\"Id\"")
    private UUID id;

    @TableField(value = "\"SessionId\"")
    private UUID sessionId;

    @TableField(value = "\"Category\"")
    private String category;

    @TableField(value = "\"Content\"")
    private String content;

    @TableField(value = "\"Extra\"")
    private String extra;

    @TableField(value = "\"Model\"")
    private String model;

    @TableField(value = "\"CreatedAt\"")
    private OffsetDateTime createdAt;

    @TableField(value = "\"AgentInfoId\"")
    private UUID agentInfoId;

    @TableField(value = "\"InputTokenCount\"")
    private Long inputTokenCount;

    @TableField(value = "\"OutputTokenCount\"")
    private Long outputTokenCount;
}
