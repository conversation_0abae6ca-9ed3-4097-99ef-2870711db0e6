package com.hntsz.boot.modules.session.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@TableName("\"Sessions\"")
public class Session {

    @TableId(value = "\"Id\"")
    private UUID id;

    @TableField("\"Type\"")
    private String type;

    @TableField("\"AgentOptionId\"")
    private UUID agentOptionId;

    @TableField("\"ApplicationId\"")
    private UUID applicationId;

    @TableField("\"ClientIdentityId\"")
    private UUID clientIdentityId;

    @TableField("\"ParentMessageId\"")
    private UUID parentMessageId;

    @TableField("\"CreatedAt\"")
    private OffsetDateTime createdAt;

    @TableField("\"UpdatedAt\"")
    private OffsetDateTime updatedAt;

    @TableField("\"ParentSessionId\"")
    private UUID parentSessionId;
}
