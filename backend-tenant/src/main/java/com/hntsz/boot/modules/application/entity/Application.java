package com.hntsz.boot.modules.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hntsz.boot.core.handler.type.StringArrayTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;
import java.util.*;

/**
 * 应用实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("\"Applications\"") // 表名加双引号保持大小写
@Schema(description = "应用实体")
public class Application {

    /**
     * 主键ID
     */
    @TableId(value = "\"Id\"", type = IdType.ASSIGN_UUID)
    @Schema(description = "应用ID")
    private UUID id;

    /**
     * 应用程序标识符
     */
    @TableField("\"Key\"")
    @Schema(description = "应用程序标识符，用于标识不同的业务应用")
    private String key;

    /**
     * 应用显示名称
     */
    @TableField("\"DisplayName\"")
    @Schema(description = "应用显示名称")
    private String displayName;

    /**
     * 应用程序描述信息
     */
    @TableField("\"Description\"")
    @Schema(description = "应用程序描述信息，说明应用的用途和功能")
    private String description;

    /**
     * 应用图标URL
     */
    @TableField("\"Icon\"")
    @Schema(description = "应用图标URL")
    private String icon;

    /**
     * 登出URL
     */
    @TableField("\"SignOutUrl\"")
    @Schema(description = "登出URL")
    private String signOutUrl;

    /**
     * 主题
     */
    @TableField("\"Theme\"")
    @Schema(description = "主题")
    private String theme;

    /**
     * AI头像URL
     */
    @TableField("\"AiAvatar\"")
    @Schema(description = "AI头像URL")
    private String aiAvatar;

    /**
     * 用户头像URL
     */
    @TableField("\"UserAvatar\"")
    @Schema(description = "用户头像URL")
    private String userAvatar;

    /**
     * 推荐开始提示词
     */
    @TableField(value = "\"RecommendedStartPrompts\"", typeHandler = StringArrayTypeHandler.class)
    @Schema(description = "推荐开始提示词")
    private List<String> recommendedStartPrompts;

    /**
     * 智能体选项ID
     */
    @TableField(value = "\"AgentOptionId\"")
    @Schema(description = "智能体选项ID")
    private UUID agentOptionId;

    /**
     * 租户ID
     */
    @TableField(value = "\"TenantId\"")
    @Schema(description = "租户ID")
    private UUID tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "\"CreatedAt\"", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private OffsetDateTime createdAt;

    /**
     * 是否启用用户下一个提示预测
     */
    @TableField("\"EnableUserNextPromptPrediction\"")
    @Schema(description = "是否启用用户下一个提示预测")
    private Boolean enableUserNextPromptPrediction;
}
