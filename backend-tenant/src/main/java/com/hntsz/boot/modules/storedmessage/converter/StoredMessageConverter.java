package com.hntsz.boot.modules.storedmessage.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.storedmessage.model.entity.StoredMessage;
import com.hntsz.boot.modules.storedmessage.model.vo.StoredMessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface StoredMessageConverter {

    StoredMessageConverter INSTANCE = Mappers.getMapper(StoredMessageConverter.class);

    StoredMessageVO entityToVo(StoredMessage entity);

    List<StoredMessageVO> toVoList(List<StoredMessage> entityList);

    Page<StoredMessageVO> toVoPage(Page<StoredMessage> entityPage);

}
