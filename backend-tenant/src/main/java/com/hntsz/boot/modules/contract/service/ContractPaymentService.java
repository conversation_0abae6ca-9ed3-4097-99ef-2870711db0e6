package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.contract.model.entity.ContractPayment;
import com.hntsz.boot.modules.contract.model.form.ContractPaymentForm;
import com.hntsz.boot.modules.contract.model.query.ContractPaymentQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同付款记录表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface ContractPaymentService extends IService<ContractPayment> {

    /**
     * 获取合同付款记录分页列表
     *
     * @param query 查询参数
     * @return 合同付款记录分页列表
     */
    IPage<ContractPaymentVO> getContractPaymentPage(ContractPaymentQuery query);

    /**
     * 获取合同付款记录详情
     *
     * @param id 付款记录ID
     * @return 合同付款记录详情
     */
    ContractPaymentVO getContractPaymentDetail(Long id);

    /**
     * 获取合同付款记录表单数据
     *
     * @param id 付款记录ID
     * @return 合同付款记录表单数据
     */
    ContractPaymentForm getContractPaymentFormData(Long id);

    /**
     * 新增合同付款记录
     *
     * @param form 合同付款记录表单数据
     * @return 是否成功
     */
    boolean saveContractPayment(ContractPaymentForm form);

    /**
     * 更新合同付款记录
     *
     * @param id 付款记录ID
     * @param form 合同付款记录表单数据
     * @return 是否成功
     */
    boolean updateContractPayment(Long id, ContractPaymentForm form);

    /**
     * 删除合同付款记录
     *
     * @param ids 付款记录ID数组
     * @return 是否成功
     */
    boolean deleteContractPayments(Long[] ids);

    /**
     * 根据合同ID获取付款记录列表
     *
     * @param contractId 合同ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByContractId(Long contractId);

    /**
     * 根据付款单号查询付款记录
     *
     * @param paymentNo 付款单号
     * @return 付款记录
     */
    ContractPayment getByPaymentNo(String paymentNo);

    /**
     * 检查付款单号是否存在
     *
     * @param paymentNo 付款单号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByPaymentNo(String paymentNo, Long excludeId);

    /**
     * 根据合同ID统计付款总额
     *
     * @param contractId 合同ID
     * @return 付款总额
     */
    BigDecimal getTotalAmountByContractId(Long contractId);

    /**
     * 根据合同ID和付款状态统计付款金额
     *
     * @param contractId 合同ID
     * @param paymentStatus 付款状态
     * @return 付款金额
     */
    BigDecimal getAmountByContractIdAndStatus(Long contractId, String paymentStatus);

    /**
     * 根据伙伴ID获取付款记录（作为付款方）
     *
     * @param partnerId 伙伴ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByPayerPartnerId(Long partnerId);

    /**
     * 根据伙伴ID获取付款记录（作为收款方）
     *
     * @param partnerId 伙伴ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByPayeePartnerId(Long partnerId);

    /**
     * 更新付款状态
     *
     * @param id 付款记录ID
     * @param status 付款状态
     * @return 是否成功
     */
    boolean updatePaymentStatus(Long id, String status);

    /**
     * 更新发票状态
     *
     * @param id 付款记录ID
     * @param invoiceStatus 发票状态
     * @return 是否成功
     */
    boolean updateInvoiceStatus(Long id, String invoiceStatus);
}
