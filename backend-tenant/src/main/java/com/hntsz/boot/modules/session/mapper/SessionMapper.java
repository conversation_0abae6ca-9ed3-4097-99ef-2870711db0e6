package com.hntsz.boot.modules.session.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.session.model.entity.Session;
import com.hntsz.boot.modules.session.model.query.SessionPageQuery;
import com.hntsz.boot.modules.session.model.vo.SessionPageVO;
import com.hntsz.boot.modules.session.model.vo.SessionStatisticVO;
import com.hntsz.boot.modules.session.model.vo.SessionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SessionMapper extends BaseMapper<Session> {

    Page<SessionPageVO> getSessionPage(@Param("page") Page<SessionPageVO> page, @Param("queryParam") SessionPageQuery queryParam);

    SessionVO getSessionById(@Param("sessionId") String sessionId);

    SessionStatisticVO getSessionStatisticInfo();

}
