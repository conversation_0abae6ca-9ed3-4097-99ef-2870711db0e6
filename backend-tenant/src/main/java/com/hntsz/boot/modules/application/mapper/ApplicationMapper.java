package com.hntsz.boot.modules.application.mapper;

import com.hntsz.boot.modules.application.entity.Application;
import com.hntsz.boot.modules.application.vo.ApplicationPageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



/**
 * 应用Mapper接口
 */
@Mapper
public interface ApplicationMapper extends BaseMapper<Application> {

        /**
         * 获取应用分页列表
         *
         * @param page          分页参数
         * @param searchKeyword 搜索关键词
         * @return 应用分页列表
         */
        IPage<ApplicationPageVO> selectApplicationPage(
                        Page<ApplicationPageVO> page,
                        @Param("searchKeyword") String searchKeyword);


}