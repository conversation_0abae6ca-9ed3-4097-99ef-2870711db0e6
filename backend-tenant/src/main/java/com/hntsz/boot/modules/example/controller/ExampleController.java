package com.hntsz.boot.modules.example.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hntsz.boot.common.base.ConverterCrudController;
import com.hntsz.boot.modules.example.mapper.ExampleMapper;
import com.hntsz.boot.modules.example.model.entity.Example;
import com.hntsz.boot.modules.example.model.vo.ExampleVO;
import com.hntsz.boot.modules.example.model.form.ExampleForm;
import com.hntsz.boot.modules.example.converter.ExampleConverter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.hntsz.boot.modules.example.model.query.ExampleQuery;
@Tag(name = "示例接口")
@RestController
@RequestMapping("/api/v1/examples")
@Slf4j
public class ExampleController extends ConverterCrudController<Example, ExampleQuery, ExampleVO, ExampleVO, ExampleForm, ExampleForm, ExampleMapper, ExampleConverter> {
    
    public ExampleController(ExampleMapper exampleMapper, ExampleConverter exampleConverter) {
        super(exampleMapper, exampleConverter);
    }

    @Override
    protected void buildQueryWrapper(LambdaQueryWrapper<Example> queryWrapper, ExampleQuery query) {

        queryWrapper.like(query.getName() != null, Example::getName, query.getName())
                   .gt(query.getGreaterThanAge() != null, Example::getAge, query.getGreaterThanAge())
                   .lt(query.getLessThanAge() != null, Example::getAge, query.getLessThanAge());
    }
}
