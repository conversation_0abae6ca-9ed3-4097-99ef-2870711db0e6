package com.hntsz.boot.modules.opportunity.converter;

import com.hntsz.boot.modules.opportunity.model.entity.Opportunity;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityForm;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 商机线索对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpportunityConverter {

    /**
     * 实体转VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    OpportunityVO entityToVo(Opportunity entity);

    /**
     * 表单转实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    Opportunity formToEntity(OpportunityForm form);

    /**
     * 实体转表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    OpportunityForm entityToForm(Opportunity entity);
}