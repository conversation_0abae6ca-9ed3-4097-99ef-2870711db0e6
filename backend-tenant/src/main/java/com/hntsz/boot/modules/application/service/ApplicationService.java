package com.hntsz.boot.modules.application.service;

import com.hntsz.boot.modules.application.entity.Application;
import com.hntsz.boot.modules.application.vo.ApplicationDetailVO;
import com.hntsz.boot.modules.application.vo.ApplicationPageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.application.vo.ApplicationSelectedVO;

import java.util.*;

/**
 * 应用服务接口
 */
public interface ApplicationService extends IService<Application> {

    /**
     * 获取应用分页列表
     *
     * @param page          分页参数
     * @param searchKeyword 搜索关键词
     * @return 应用分页列表
     */
    IPage<ApplicationPageVO> getApplicationPage(
            Page<ApplicationPageVO> page,
            String searchKeyword);

    /**
     * 获取应用详情
     *
     * @param id 应用ID
     * @return 应用详情
     */
    ApplicationDetailVO getApplicationDetail(String id);

    /**
     * 创建应用
     *
     * @param application 应用信息
     * @return 是否成功
     */
    Boolean createApplication(Application application);

    /**
     * 更新应用
     *
     * @param application 应用信息
     * @return 是否成功
     */
    Boolean updateApplication(Application application);

    /**
     * 删除应用
     *
     * @param id 应用ID
     * @return 是否成功
     */
    Boolean deleteApplication(String id);

    /**
     * 获取应用下拉列表
     *
     * @return 应用下拉列表
     */
    List<ApplicationSelectedVO> getApplicationSelectedList();

}