package com.hntsz.boot.modules.session.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.modules.session.model.query.SessionPageQuery;
import com.hntsz.boot.modules.session.model.vo.SessionPageVO;
import com.hntsz.boot.modules.session.model.vo.SessionStatisticVO;
import com.hntsz.boot.modules.session.model.vo.SessionVO;

public interface SessionService {


    IPage<SessionPageVO> getSessionPage(SessionPageQuery queryParam);

    SessionVO getSessionById(String sessionId);

    SessionStatisticVO getSessionStatisticInfo();

}
