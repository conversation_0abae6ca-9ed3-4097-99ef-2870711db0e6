package com.hntsz.boot.modules.agentoptions.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能体选项分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "智能体选项分页查询参数")
public class AgentOptionsPageQuery extends BasePageQuery {

    @Schema(description = "关键字(名称/描述/标识)")
    private String keywords;

}