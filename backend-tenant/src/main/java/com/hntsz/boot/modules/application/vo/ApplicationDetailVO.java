package com.hntsz.boot.modules.application.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 应用详情视图对象
 */
@Data
@Schema(description = "应用详情视图对象")
public class ApplicationDetailVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private UUID id;

    /**
     * 应用唯一标识
     */
    @Schema(description = "应用唯一标识")
    private String key;

    /**
     * 应用显示名称
     */
    @Schema(description = "应用显示名称")
    private String displayName;

    /**
     * 应用描述
     */
    @Schema(description = "应用描述")
    private String description;

    /**
     * 应用图标URL
     */
    @Schema(description = "应用图标URL")
    private String icon;

    /**
     * 登出URL
     */
    @Schema(description = "登出URL")
    private String signOutUrl;

    /**
     * 主题
     */
    @Schema(description = "主题")
    private String theme;

    /**
     * AI头像URL
     */
    @Schema(description = "AI头像URL")
    private String aiAvatar;

    /**
     * 用户头像URL
     */
    @Schema(description = "用户头像URL")
    private String userAvatar;

    /**
     * 推荐开始提示
     */
    @Schema(description = "推荐开始提示")
    private List<String> recommendedStartPrompts;

    /**
     * 智能体配置ID
     */
    @Schema(description = "智能体配置ID")
    private UUID agentOptionId;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private UUID tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private OffsetDateTime createdAt;

    /**
     * 是否启用用户下一个提示预测
     */
    @Schema(description = "是否启用用户下一个提示预测")
    private Boolean enableUserNextPromptPrediction;

    /**
     * 智能体数量
     */
    @Schema(description = "智能体数量")
    private Integer agentCount;

    /**
     * 会话数量
     */
    @Schema(description = "会话数量")
    private Integer sessionCount;

    /**
     * 最后活动时间
     */
    @Schema(description = "最后活动时间")
    private String lastActivity;
}