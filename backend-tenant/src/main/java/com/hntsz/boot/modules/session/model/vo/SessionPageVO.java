package com.hntsz.boot.modules.session.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class SessionPageVO {

    @Schema(description = "会话id")
    private UUID id;

    @Schema(description = "用户名称")
    private String uniqueUserKey;

    @Schema(description = "用户ip")
    private String userIp;

    @Schema(description = "浏览器")
    private String userAgent;

    @Schema(description = "应用名称")
    private String applicationName;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "消息数量")
    private Long messageCount;

    @Schema(description = "会话时长")
    private Long duration;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime startTime;
}
