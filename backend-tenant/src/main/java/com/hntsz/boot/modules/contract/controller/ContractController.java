package com.hntsz.boot.modules.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.contract.model.form.ContractForm;
import com.hntsz.boot.modules.contract.model.query.ContractQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;
import com.hntsz.boot.modules.contract.service.ContractService;
import com.hntsz.boot.modules.contract.service.ContractAttachmentService;
import com.hntsz.boot.modules.contract.model.form.ContractAttachmentForm;
import com.hntsz.boot.modules.contract.model.vo.ContractAttachmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Tag(name = "合同管理接口")
@RestController
@RequestMapping("/api/v1/contracts")
@RequiredArgsConstructor
public class ContractController {

    private final ContractService contractService;
    private final ContractAttachmentService contractAttachmentService;

    @Operation(summary = "合同分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public PageResult<ContractVO> getContractPage(ContractQuery queryParams) {
        IPage<ContractVO> result = contractService.getContractPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "合同详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public Result<ContractVO> getContractDetail(
            @Parameter(description = "合同ID") @PathVariable Long id) {
        ContractVO contract = contractService.getContractDetail(id);
        return Result.success(contract);
    }

    @Operation(summary = "获取合同表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public Result<ContractForm> getContractFormData(
            @Parameter(description = "合同ID") @PathVariable Long id) {
        ContractForm formData = contractService.getContractFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "新增合同")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('contract:add')")
    public Result<Long> saveContract(@Validated @RequestBody ContractForm form) {
        Long contractId = contractService.saveContract(form);
        return Result.success(contractId);
    }

    @Operation(summary = "修改合同")
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('contract:edit')")
    public Result<Void> updateContract(
            @Parameter(description = "合同ID") @PathVariable Long id,
            @Validated @RequestBody ContractForm form) {
        boolean result = contractService.updateContract(id, form);
        return Result.judge(result);
    }

    @Operation(summary = "删除合同")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('contract:delete')")
    public Result<Void> deleteContracts(
            @Parameter(description = "合同ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        String[] idArray = ids.split(",");
        Long[] idLongArray = new Long[idArray.length];
        for (int i = 0; i < idArray.length; i++) {
            idLongArray[i] = Long.parseLong(idArray[i]);
        }
        boolean result = contractService.deleteContracts(idLongArray);
        return Result.judge(result);
    }

    @Operation(summary = "获取合同选项列表")
    @GetMapping("/options")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public Result<List<ContractVO>> getContractOptions() {
        List<ContractVO> options = contractService.getContractOptions();
        return Result.success(options);
    }

    @Operation(summary = "更新合同状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("@ss.hasPerm('contract:edit')")
    public Result<Void> updateContractStatus(
            @Parameter(description = "合同ID") @PathVariable Long id,
            @Parameter(description = "合同状态") @RequestParam String status) {
        boolean result = contractService.updateContractStatus(id, status);
        return Result.judge(result);
    }

    @Operation(summary = "根据伙伴ID获取相关合同列表")
    @GetMapping("/by-partner/{partnerId}")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public Result<List<ContractVO>> getContractsByPartnerId(
            @Parameter(description = "伙伴ID") @PathVariable Long partnerId) {
        List<ContractVO> contracts = contractService.getContractsByPartnerId(partnerId);
        return Result.success(contracts);
    }

    @Operation(summary = "获取合同文件列表")
    @GetMapping("/{contractId}/attachments")
    @PreAuthorize("@ss.hasPerm('contract:query')")
    public Result<List<ContractAttachmentVO>> getContractAttachments(
            @Parameter(description = "合同ID") @PathVariable Long contractId) {
        List<ContractAttachmentVO> attachments = contractAttachmentService.getByContractId(contractId);
        return Result.success(attachments);
    }

    @Operation(summary = "添加合同文件")
    @PostMapping("/{contractId}/attachments")
    @PreAuthorize("@ss.hasPerm('contract:edit')")
    public Result<Void> addContractAttachment(
            @Parameter(description = "合同ID") @PathVariable Long contractId,
            @Validated @RequestBody ContractAttachmentForm form) {
        form.setContractId(contractId);
        boolean result = contractAttachmentService.saveContractAttachment(form);
        return Result.judge(result);
    }

    @Operation(summary = "批量添加合同文件")
    @PostMapping("/{contractId}/attachments/batch")
    @PreAuthorize("@ss.hasPerm('contract:edit')")
    public Result<Void> batchAddContractAttachments(
            @Parameter(description = "合同ID") @PathVariable Long contractId,
            @Parameter(description = "附件ID列表") @RequestBody List<Long> attachmentIds) {
        boolean result = contractAttachmentService.batchSaveContractAttachments(contractId, attachmentIds);
        return Result.judge(result);
    }

    @Operation(summary = "删除合同文件")
    @DeleteMapping("/{contractId}/attachments/{attachmentId}")
    @PreAuthorize("@ss.hasPerm('contract:edit')")
    public Result<Void> deleteContractAttachment(
            @Parameter(description = "合同ID") @PathVariable Long contractId,
            @Parameter(description = "附件ID") @PathVariable Long attachmentId) {
        boolean result = contractAttachmentService.deleteContractAttachment(contractId, attachmentId);
        return Result.judge(result);
    }
}
