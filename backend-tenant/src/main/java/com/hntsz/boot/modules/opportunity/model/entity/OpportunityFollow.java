package com.hntsz.boot.modules.opportunity.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机跟进记录实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("opportunity_follow")
public class OpportunityFollow extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商机ID
     */
    private Long opportunityId;

    /**
     * 跟进方式(关联字典编码：follow_type)
     */
    private String followType;

    /**
     * 跟进时间
     */
    private LocalDateTime followDate;

    /**
     * 跟进时长(分钟)
     */
    private Integer followDuration;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进结果(关联字典编码：follow_result)
     */
    private String followResult;

    /**
     * 下一步行动计划
     */
    private String nextAction;

    /**
     * 下次跟进日期
     */
    private LocalDate nextFollowDate;

    /**
     * 附件ID
     */
    private String attachmentId;

    /**
     * 跟进人ID(关联sys_user表)
     */
    private String followUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 修改人ID
     */
    private Long updateBy;
}