package com.hntsz.boot.modules.attachment.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hntsz.boot.common.base.ConverterCrudController;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.shared.file.model.FileInfo;
import com.hntsz.boot.shared.file.service.FileService;
import com.hntsz.boot.modules.attachment.converter.AttachmentConverter;
import com.hntsz.boot.modules.attachment.mapper.AttachmentMapper;
import com.hntsz.boot.modules.attachment.model.entity.Attachment;
import com.hntsz.boot.modules.attachment.model.query.AttachmentQuery;
import com.hntsz.boot.modules.attachment.model.vo.AttachmentVO;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Tag(name = "附件管理")
@RestController
@RequestMapping("/api/v1/attachment")
@Slf4j
public class AttachmentController
        extends
        ConverterCrudController<Attachment, AttachmentQuery, AttachmentVO, AttachmentVO, AttachmentVO, AttachmentVO, AttachmentMapper, AttachmentConverter> {

    private final FileService fileService;

    public AttachmentController(AttachmentMapper attachmentMapper, AttachmentConverter attachmentConverter, FileService fileService) {
        super(attachmentMapper, attachmentConverter);
        this.fileService = fileService;
    }

    @Override
    protected void buildQueryWrapper(LambdaQueryWrapper<Attachment> queryWrapper, AttachmentQuery query) {
        queryWrapper.like(StringUtils.isNotBlank(query.getFileName()), Attachment::getFileName, query.getFileName())
                .eq(StringUtils.isNotBlank(query.getFileType()), Attachment::getFileType, query.getFileType())
                .orderByDesc(Attachment::getCreateTime);
    }

    @Override
    public Result<?> add(@Valid @RequestBody AttachmentVO form) {
        throw new RuntimeException("新增方法废弃");
    }

    @PostMapping("/upload")
    public Result<AttachmentVO> upload(
        @Parameter(
                name = "file",
                description = "表单文件对象",
                required = true,
                in = ParameterIn.DEFAULT,
                schema = @Schema(name = "file", format = "binary")
        )
        @RequestPart(value = "file") MultipartFile file
    ) {

        FileInfo fileInfo = fileService.uploadFile(file);

        Attachment attachment = new Attachment();
        attachment.setFileName(fileInfo.getName());
        attachment.setFilePath(fileInfo.getUrl());
        
        // 根据文件类型设置对应的类型
        String contentType = file.getContentType();
        if (contentType != null) {
            if (contentType.startsWith("image/")) {
                attachment.setFileType("IMAGE");
            } else if (contentType.startsWith("video/")) {
                attachment.setFileType("VIDEO");
            } else if (contentType.startsWith("audio/")) {
                attachment.setFileType("AUDIO");
            } else if (contentType.equals("application/pdf") || 
                      contentType.equals("application/msword") ||
                      contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                      contentType.equals("application/vnd.ms-excel") ||
                      contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                      contentType.equals("application/vnd.ms-powerpoint") ||
                      contentType.equals("application/vnd.openxmlformats-officedocument.presentationml.presentation") ||
                      contentType.equals("text/plain")) {
                attachment.setFileType("DOCUMENT");
            } else {
                attachment.setFileType("OTHER");
            }
        } else {
            attachment.setFileType("OTHER");
        }
        attachment.setFileSize(file.getSize());
        baseMapper.insert(attachment);

        AttachmentVO attachmentVO = converter.entityToVo(attachment);

        return Result.success(attachmentVO);
    }

}
