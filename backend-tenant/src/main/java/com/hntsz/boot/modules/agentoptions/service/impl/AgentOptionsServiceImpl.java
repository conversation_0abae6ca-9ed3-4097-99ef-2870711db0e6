package com.hntsz.boot.modules.agentoptions.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.agentoptions.entity.AgentOptions;
import com.hntsz.boot.modules.agentoptions.form.AgentOptionsForm;
import com.hntsz.boot.modules.agentoptions.mapper.AgentOptionsMapper;
import com.hntsz.boot.modules.agentoptions.query.AgentOptionsPageQuery;
import com.hntsz.boot.modules.agentoptions.service.AgentOptionsService;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsDetailVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsPageVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsStatVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 智能体选项服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentOptionsServiceImpl extends ServiceImpl<AgentOptionsMapper, AgentOptions>
        implements AgentOptionsService {

    private final AgentOptionsMapper agentOptionsMapper;

    @Override
    public IPage<AgentOptionsPageVO> getAgentOptionsPage(AgentOptionsPageQuery pageQuery) {
        LambdaQueryWrapper<AgentOptions> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(pageQuery.getKeywords())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AgentOptions::getName, pageQuery.getKeywords())
                    .or().like(AgentOptions::getDescription, pageQuery.getKeywords())
                    .or().like(AgentOptions::getKey, pageQuery.getKeywords()));
        }

        // 排序：按创建时间倒序
        queryWrapper.orderByDesc(AgentOptions::getCreatedAt);

        // 分页查询
        Page<AgentOptions> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        IPage<AgentOptions> entityPage = this.page(page, queryWrapper);

        // 转换为VO
        Page<AgentOptionsPageVO> voPage = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        BeanUtils.copyProperties(entityPage, voPage);

        List<AgentOptionsPageVO> voList = new ArrayList<>();
        for (AgentOptions entity : entityPage.getRecords()) {
            AgentOptionsPageVO vo = new AgentOptionsPageVO();
            BeanUtils.copyProperties(entity, vo);

            // 设置父级名称
            if (entity.getParentId() != null) {
                vo.setType("conversational");
                AgentOptions parent = this.getById(entity.getParentId());
                if (parent != null) {
                    vo.setParentName(parent.getName());
                }
            } else {
                vo.setType("other");
            }

            voList.add(vo);
        }

        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public AgentOptionsDetailVO getAgentOptionsDetail(UUID id) {
        AgentOptions entity = this.getById(id);
        if (entity == null) {
            return null;
        }

        AgentOptionsDetailVO vo = new AgentOptionsDetailVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置类型
        if (entity.getParentId() != null) {
            vo.setType("conversational");
        } else {
            vo.setType("other");
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAgentOptions(AgentOptionsForm form) {
        AgentOptions entity = new AgentOptions();

        // 默认配置
        if (StrUtil.isBlank(form.getKey())) {
            form.setKey("default");
        }

        form.setId(UUID.randomUUID());
        BeanUtils.copyProperties(form, entity);

        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAgentOptions(AgentOptionsForm form) {
        if (form.getId() == null) {
            throw new IllegalArgumentException("更新时ID不能为空");
        }

        AgentOptions entity = this.getById(form.getId());
        if (entity == null) {
            throw new IllegalArgumentException("智能体选项不存在");
        }
        BeanUtils.copyProperties(form, entity);

        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setArchiveStatus(UUID id, boolean isArchived) {
        if (id == null) {
            return false;
        }

        AgentOptions entity = new AgentOptions();
        entity.setId(id);
        entity.setIsArchived(isArchived);

        return this.updateById(entity);
    }

    @Override
    public AgentOptionsStatVO getStatistics() {
        // 统计总数
        long totalCount = this.count();

        // 统计活跃数量（未归档）
        LambdaQueryWrapper<AgentOptions> activeWrapper = new LambdaQueryWrapper<>();
        activeWrapper.eq(AgentOptions::getIsArchived, false);
        long activeCount = this.count(activeWrapper);

        AgentOptionsStatVO statVO = new AgentOptionsStatVO();
        statVO.setTotalCount(totalCount);
        statVO.setActiveCount(activeCount);

        return statVO;
    }

    @Override
    public List<AgentOptionsPageVO> getAllAgentOptions() {
        // 查询所有未归档的智能体选项
        LambdaQueryWrapper<AgentOptions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentOptions::getIsArchived, false)
                .orderByDesc(AgentOptions::getCreatedAt);

        List<AgentOptions> entityList = this.list(queryWrapper);

        List<AgentOptionsPageVO> voList = new ArrayList<>();
        for (AgentOptions entity : entityList) {
            AgentOptionsPageVO vo = new AgentOptionsPageVO();
            BeanUtils.copyProperties(entity, vo);
            voList.add(vo);
        }

        return voList;
    }
}