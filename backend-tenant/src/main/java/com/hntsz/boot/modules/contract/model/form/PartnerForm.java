package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 业务伙伴表单对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "业务伙伴表单对象")
public class PartnerForm {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 伙伴名称
     */
    @Schema(description = "伙伴名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "伙伴名称不能为空")
    @Size(max = 200, message = "伙伴名称长度不能超过200个字符")
    private String partnerName;

    /**
     * 伙伴编码
     */
    @Schema(description = "伙伴编码")
    @Size(max = 50, message = "伙伴编码长度不能超过50个字符")
    private String partnerCode;

    /**
     * 是否我司或旗下企业(1-是 0-否)
     */
    @Schema(description = "是否我司或旗下企业")
    private Boolean isOurCompany;

    /**
     * 伙伴类型(关联字典编码：partner_type)
     */
    @Schema(description = "伙伴类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "伙伴类型不能为空")
    private String partnerType;

    /**
     * 法定代表人
     */
    @Schema(description = "法定代表人")
    @Size(max = 100, message = "法定代表人长度不能超过100个字符")
    private String legalRepresentative;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;

    /**
     * 地址
     */
    @Schema(description = "地址")
    @Size(max = 500, message = "地址长度不能超过500个字符")
    private String address;

    /**
     * 证件类型(关联字典编码：certificate_type)
     */
    @Schema(description = "证件类型")
    private String certificateType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码")
    @Size(max = 50, message = "证件号码长度不能超过50个字符")
    private String certificateNumber;

    /**
     * 税号
     */
    @Schema(description = "税号")
    @Size(max = 50, message = "税号长度不能超过50个字符")
    private String taxNumber;

    /**
     * 开户银行
     */
    @Schema(description = "开户银行")
    @Size(max = 200, message = "开户银行长度不能超过200个字符")
    private String bankName;

    /**
     * 银行账号
     */
    @Schema(description = "银行账号")
    @Size(max = 50, message = "银行账号长度不能超过50个字符")
    private String bankAccount;

    /**
     * 状态(active-正常 inactive-禁用)
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
