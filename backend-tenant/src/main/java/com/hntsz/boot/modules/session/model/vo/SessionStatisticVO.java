package com.hntsz.boot.modules.session.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SessionStatisticVO {

    @Schema(description = "会话总数")
    private Long totalSessionCount = 0L;

    @Schema(description = "进行中会话数")
    private Long processingCount = 0L;

    @Schema(description = "平均时长")
    private BigDecimal avgDuration = BigDecimal.ZERO;

    @Schema(description = "总消息数")
    private Long totalMessageCount = 0L;
}
