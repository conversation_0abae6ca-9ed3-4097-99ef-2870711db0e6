package com.hntsz.boot.modules.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.contract.mapper.ContractAttachmentMapper;
import com.hntsz.boot.modules.contract.model.entity.ContractAttachment;
import com.hntsz.boot.modules.contract.model.form.ContractAttachmentForm;
import com.hntsz.boot.modules.contract.model.vo.ContractAttachmentVO;
import com.hntsz.boot.modules.contract.service.ContractAttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同文件关联Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
@RequiredArgsConstructor
public class ContractAttachmentServiceImpl extends ServiceImpl<ContractAttachmentMapper, ContractAttachment> implements ContractAttachmentService {

    private final ContractAttachmentMapper contractAttachmentMapper;

    @Override
    public List<ContractAttachmentVO> getByContractId(Long contractId) {
        return contractAttachmentMapper.getByContractId(contractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveContractAttachment(ContractAttachmentForm form) {
        ContractAttachment entity = new ContractAttachment();
        BeanUtils.copyProperties(form, entity);
        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveContractAttachments(Long contractId, List<Long> attachmentIds) {
        if (attachmentIds == null || attachmentIds.isEmpty()) {
            return true;
        }

        List<ContractAttachment> entities = new ArrayList<>();
        for (int i = 0; i < attachmentIds.size(); i++) {
            ContractAttachment entity = new ContractAttachment();
            entity.setContractId(contractId);
            entity.setAttachmentId(attachmentIds.get(i));
            entity.setSort(i);
            entities.add(entity);
        }

        return this.saveBatch(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByContractId(Long contractId) {
        return contractAttachmentMapper.deleteByContractId(contractId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAttachmentId(Long attachmentId) {
        return contractAttachmentMapper.deleteByAttachmentId(attachmentId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContractAttachment(Long contractId, Long attachmentId) {
        LambdaQueryWrapper<ContractAttachment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractAttachment::getContractId, contractId)
               .eq(ContractAttachment::getAttachmentId, attachmentId);
        return this.remove(wrapper);
    }
}
