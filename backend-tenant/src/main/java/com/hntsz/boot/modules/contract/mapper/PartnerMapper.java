package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.modules.contract.model.entity.Partner;
import com.hntsz.boot.modules.contract.model.query.PartnerQuery;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务伙伴 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface PartnerMapper extends BaseMapper<Partner> {

    /**
     * 获取业务伙伴分页列表
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 业务伙伴分页列表
     */
    IPage<PartnerVO> getPartnerPage(IPage<PartnerVO> page, @Param("query") PartnerQuery query);

    /**
     * 获取业务伙伴详情
     *
     * @param id 业务伙伴ID
     * @return 业务伙伴详情
     */
    PartnerVO getPartnerDetail(@Param("id") Long id);

    /**
     * 获取业务伙伴选项列表
     *
     * @return 业务伙伴选项列表
     */
    List<Option> getPartnerOptions();

    /**
     * 根据伙伴名称查询伙伴
     *
     * @param partnerName 伙伴名称
     * @return 业务伙伴
     */
    Partner getByPartnerName(@Param("partnerName") String partnerName);

    /**
     * 根据伙伴编码查询伙伴
     *
     * @param partnerCode 伙伴编码
     * @return 业务伙伴
     */
    Partner getByPartnerCode(@Param("partnerCode") String partnerCode);
}
