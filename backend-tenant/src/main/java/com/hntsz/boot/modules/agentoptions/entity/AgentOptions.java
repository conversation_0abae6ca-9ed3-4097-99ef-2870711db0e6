package com.hntsz.boot.modules.agentoptions.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 智能体选项实体类
 *
 */
@Data
@TableName("\"AgentOptions\"")
@Schema(description = "智能体选项")
public class AgentOptions {

    @Schema(description = "主键ID")
    @TableId(value = "\"Id\"", type = IdType.ASSIGN_UUID)
    private UUID id;

    @Schema(description = "父级ID")
    @TableField(value = "\"ParentId\"")
    private UUID parentId;

    @Schema(description = "智能体名称")
    @TableField(value = "\"Name\"")
    private String name;

    @Schema(description = "智能体描述")
    @TableField(value = "\"Description\"")
    private String description;

    @Schema(description = "智能体标识")
    @TableField(value = "\"Key\"")
    private String key;

    @Schema(description = "扩展信息")
    @TableField(value = "\"Extra\"")
    private String extra;

    @Schema(description = "创建时间")
    @TableField(value = "\"CreatedAt\"", fill = FieldFill.INSERT)
    private OffsetDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "\"UpdatedAt\"", fill = FieldFill.INSERT)
    private OffsetDateTime updatedAt;

    @Schema(description = "是否归档")
    @TableField(value = "\"IsArchived\"")
    private Boolean isArchived = false;
}