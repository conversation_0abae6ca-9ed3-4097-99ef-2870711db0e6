package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同付款记录表表单对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同付款记录表表单对象")
public class ContractPaymentForm {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "合同ID不能为空")
    private Long contractId;

    /**
     * 付款单号
     */
    @Schema(description = "付款单号")
    @Size(max = 100, message = "付款单号长度不能超过100个字符")
    private String paymentNo;

    /**
     * 付款类型(关联字典编码：payment_type)
     */
    @Schema(description = "付款类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "付款类型不能为空")
    private String paymentType;

    /**
     * 付款方式(关联字典编码：payment_method)
     */
    @Schema(description = "付款方式")
    private String paymentMethod;

    /**
     * 付款方ID(关联partner表)
     */
    @Schema(description = "付款方ID")
    private Long payerPartnerId;

    /**
     * 收款方ID(关联partner表)
     */
    @Schema(description = "收款方ID")
    private Long payeePartnerId;

    /**
     * 计划金额
     */
    @Schema(description = "计划金额")
    private BigDecimal plannedAmount;

    /**
     * 实际金额
     */
    @Schema(description = "实际金额")
    private BigDecimal actualAmount;

    /**
     * 币种
     */
    @Schema(description = "币种")
    @Size(max = 10, message = "币种长度不能超过10个字符")
    private String currency;

    /**
     * 计划付款日期
     */
    @Schema(description = "计划付款日期")
    private LocalDate plannedDate;

    /**
     * 实际付款日期
     */
    @Schema(description = "实际付款日期")
    private LocalDate actualDate;

    /**
     * 付款状态(pending-待付款 paid-已付款 partial-部分付款 overdue-已逾期 cancelled-已取消)
     */
    @Schema(description = "付款状态")
    private String paymentStatus;

    /**
     * 付款银行
     */
    @Schema(description = "付款银行")
    @Size(max = 200, message = "付款银行长度不能超过200个字符")
    private String bankName;

    /**
     * 付款账号
     */
    @Schema(description = "付款账号")
    @Size(max = 50, message = "付款账号长度不能超过50个字符")
    private String bankAccount;

    /**
     * 交易流水号
     */
    @Schema(description = "交易流水号")
    @Size(max = 100, message = "交易流水号长度不能超过100个字符")
    private String transactionNo;

    /**
     * 付款凭证文件Id
     */
    @Schema(description = "付款凭证文件Id")
    @Size(max = 500, message = "付款凭证文件Id长度不能超过500个字符")
    private String voucherAttachmentId;

    /**
     * 发票状态(not_issued-未开票 issued-已开票 received-已收票)
     */
    @Schema(description = "发票状态")
    private String invoiceStatus;

    /**
     * 发票号码
     */
    @Schema(description = "发票号码")
    @Size(max = 100, message = "发票号码长度不能超过100个字符")
    private String invoiceNo;

    /**
     * 发票金额
     */
    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    @Schema(description = "开票日期")
    private LocalDate invoiceDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
