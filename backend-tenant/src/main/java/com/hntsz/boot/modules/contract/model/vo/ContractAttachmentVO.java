package com.hntsz.boot.modules.contract.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合同文件关联VO对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Schema(description = "合同文件关联VO对象")
public class ContractAttachmentVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 合同ID(关联contract表)
     */
    @Schema(description = "合同ID")
    private Long contractId;

    /**
     * 附件ID(关联tsz_attachment表)
     */
    @Schema(description = "附件ID")
    private Long attachmentId;

    /**
     * 文件名称(来自tsz_attachment表)
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小")
    private Long fileSize;
}
