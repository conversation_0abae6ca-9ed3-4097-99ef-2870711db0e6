package com.hntsz.boot.modules.attachment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件实体对象
 */
@TableName("tsz_attachment")
@Data
@EqualsAndHashCode(callSuper = true)
public class Attachment extends BaseEntityExtra {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)
     */
    private String fileType;

    /**
     * 文件大小
     */
    private Long fileSize;
} 