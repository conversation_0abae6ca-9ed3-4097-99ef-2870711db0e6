package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 合同文件关联实体对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@Setter
@TableName("contract_attachment")
public class ContractAttachment extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同ID(关联contract表)
     */
    private Long contractId;

    /**
     * 附件ID(关联tsz_attachment表)
     */
    private Long attachmentId;

    /**
     * 排序
     */
    private Integer sort;
}
