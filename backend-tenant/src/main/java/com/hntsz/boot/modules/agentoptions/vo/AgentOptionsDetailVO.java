package com.hntsz.boot.modules.agentoptions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 智能体选项详情视图对象
 */
@Data
@Schema(description = "智能体详情")
public class AgentOptionsDetailVO {

    @Schema(description = "智能体ID")
    private UUID id;

    @Schema(description = "父级智能体ID")
    private UUID parentId;

    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体描述")
    private String description;

    @Schema(description = "智能体标识")
    private String key;

    @Schema(description = "智能体类型")
    private String type; // conversational | task

    @Schema(description = "扩展信息")
    private String extra;

    @Schema(description = "是否归档")
    private Boolean isArchived;

    @Schema(description = "创建时间")
    private OffsetDateTime createdAt;

    @Schema(description = "更新时间")
    private OffsetDateTime updatedAt;
}