package com.hntsz.boot.modules.application.converter;

import com.hntsz.boot.modules.application.entity.Application;
import com.hntsz.boot.modules.application.vo.ApplicationSelectedVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ApplicationConverter {

    ApplicationConverter INSTANCE = Mappers.getMapper(ApplicationConverter.class);

    @Mapping(source = "displayName", target = "name")
    ApplicationSelectedVO convert(Application application);

    List<ApplicationSelectedVO> convertToList(List<Application> list);
}
