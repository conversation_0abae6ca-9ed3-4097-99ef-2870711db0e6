package com.hntsz.boot.modules.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hntsz.boot.modules.application.converter.ApplicationConverter;
import com.hntsz.boot.modules.application.entity.Application;
import com.hntsz.boot.modules.application.mapper.ApplicationMapper;
import com.hntsz.boot.modules.application.service.ApplicationService;
import com.hntsz.boot.modules.application.vo.ApplicationDetailVO;
import com.hntsz.boot.modules.application.vo.ApplicationPageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.core.security.util.SecurityUtils;
import com.hntsz.boot.modules.application.vo.ApplicationSelectedVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.*;

/**
 * 应用服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, Application> implements ApplicationService {

    private final ApplicationMapper applicationMapper;

    @Override
    public IPage<ApplicationPageVO> getApplicationPage(Page<ApplicationPageVO> page, String searchKeyword) {
        return applicationMapper.selectApplicationPage(page, searchKeyword);
    }

    @Override
    public ApplicationDetailVO getApplicationDetail(String id) {
        Application application = applicationMapper.selectOne(
                new QueryWrapper<Application>().eq("\"Id\"", UUID.fromString(id)));
        if (application == null) {
            throw new RuntimeException("应用不存在");
        }

        ApplicationDetailVO detailVO = new ApplicationDetailVO();
        BeanUtils.copyProperties(application, detailVO);

        // TODO: 通过统计查询获取真实的代理数量、会话数量和最后活动时间
        detailVO.setAgentCount(0);
        detailVO.setSessionCount(0);
        detailVO.setLastActivity("无活动");

        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createApplication(Application application) {
        try {
            if (application == null) {
                throw new IllegalArgumentException("应用信息不能为空");
            }
            application.setCreatedAt(OffsetDateTime.now());
            if (application.getId() == null) {
                application.setId(UUID.randomUUID());
            }

            // todo:设置默认值
            if (!StringUtils.hasText(application.getKey())) {
                application.setKey("default-app");
            }
            if (!StringUtils.hasText(application.getTheme())) {
                application.setTheme("light");
            }

            if (application.getRecommendedStartPrompts() == null) {
                application.setRecommendedStartPrompts(new ArrayList<>());
            }
            if (application.getTenantId() == null) {
                UUID currentTenantId = SecurityUtils.getTenantId();
                if (currentTenantId != null) {
                    application.setTenantId(currentTenantId);
                } else {
                    throw new RuntimeException("请登录后创建应用");
                }
            }
            if (application.getEnableUserNextPromptPrediction() == null) {
                application.setEnableUserNextPromptPrediction(false);
            }

            return this.save(application);
        } catch (Exception e) {
            log.error("创建应用失败", e);
            throw new RuntimeException("创建应用失败: " + (e.getMessage() != null ? e.getMessage() : e.toString()), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateApplication(Application application) {
        try {
            if (application.getId() == null) {
                throw new RuntimeException("应用ID不能为空");
            }

            // 设置默认值
            if (application.getRecommendedStartPrompts() == null) {
                application.setRecommendedStartPrompts(new ArrayList<>());
            }
            if (application.getEnableUserNextPromptPrediction() == null) {
                application.setEnableUserNextPromptPrediction(false);
            }

            log.info("更新应用: ID={}, 租户ID={}", application.getId(), application.getTenantId());
            return this.updateById(application);
        } catch (Exception e) {
            log.error("更新应用失败: ID={}", application.getId(), e);
            throw new RuntimeException("更新应用失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteApplication(String id) {
        try {
            // 检查应用是否存在
            Application application = this.getById(id);
            if (application == null) {
                throw new RuntimeException("应用不存在");
            }

            // TODO: 检查是否有关联的智能体或会话，如果有则不允许删除

            return this.removeById(id);
        } catch (Exception e) {
            log.error("删除应用失败", e);
            throw new RuntimeException("删除应用失败: " + e.getMessage());
        }
    }

    @Override
    public List<ApplicationSelectedVO> getApplicationSelectedList() {
        List<Application> applicationList = applicationMapper.selectList(new LambdaQueryWrapper<Application>()
                .select(Application::getId, Application::getKey, Application::getDisplayName)
        );
        return ApplicationConverter.INSTANCE.convertToList(applicationList);
    }
}