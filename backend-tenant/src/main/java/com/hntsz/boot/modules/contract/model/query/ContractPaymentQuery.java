package com.hntsz.boot.modules.contract.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同付款记录表查询对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同付款记录表查询对象")
public class ContractPaymentQuery extends BasePageQuery {

    /**
     * 搜索关键字(付款单号、交易流水号)
     */
    @Schema(description = "搜索关键字")
    private String keywords;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private Long contractId;

    /**
     * 付款类型
     */
    @Schema(description = "付款类型")
    private String paymentType;

    /**
     * 付款方式
     */
    @Schema(description = "付款方式")
    private String paymentMethod;

    /**
     * 付款方ID
     */
    @Schema(description = "付款方ID")
    private Long payerPartnerId;

    /**
     * 收款方ID
     */
    @Schema(description = "收款方ID")
    private Long payeePartnerId;

    /**
     * 付款状态
     */
    @Schema(description = "付款状态")
    private String paymentStatus;

    /**
     * 发票状态
     */
    @Schema(description = "发票状态")
    private String invoiceStatus;

    /**
     * 计划付款日期范围开始
     */
    @Schema(description = "计划付款日期范围开始")
    private LocalDate plannedDateStart;

    /**
     * 计划付款日期范围结束
     */
    @Schema(description = "计划付款日期范围结束")
    private LocalDate plannedDateEnd;

    /**
     * 实际付款日期范围开始
     */
    @Schema(description = "实际付款日期范围开始")
    private LocalDate actualDateStart;

    /**
     * 实际付款日期范围结束
     */
    @Schema(description = "实际付款日期范围结束")
    private LocalDate actualDateEnd;

    /**
     * 计划金额最小值
     */
    @Schema(description = "计划金额最小值")
    private BigDecimal plannedAmountMin;

    /**
     * 计划金额最大值
     */
    @Schema(description = "计划金额最大值")
    private BigDecimal plannedAmountMax;

    /**
     * 实际金额最小值
     */
    @Schema(description = "实际金额最小值")
    private BigDecimal actualAmountMin;

    /**
     * 实际金额最大值
     */
    @Schema(description = "实际金额最大值")
    private BigDecimal actualAmountMax;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;
}
