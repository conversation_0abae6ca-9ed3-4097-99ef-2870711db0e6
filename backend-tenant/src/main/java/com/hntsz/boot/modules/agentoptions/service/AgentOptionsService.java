package com.hntsz.boot.modules.agentoptions.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.agentoptions.entity.AgentOptions;
import com.hntsz.boot.modules.agentoptions.form.AgentOptionsForm;
import com.hntsz.boot.modules.agentoptions.query.AgentOptionsPageQuery;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsDetailVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsPageVO;
import com.hntsz.boot.modules.agentoptions.vo.AgentOptionsStatVO;

import java.util.List;
import java.util.UUID;

/**
 * 智能体选项服务接口
 */
public interface AgentOptionsService extends IService<AgentOptions> {

    /**
     * 分页查询智能体选项
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    IPage<AgentOptionsPageVO> getAgentOptionsPage(AgentOptionsPageQuery pageQuery);

    /**
     * 根据ID查询智能体选项详情
     *
     * @param id 智能体选项ID
     * @return 智能体选项详情
     */
    AgentOptionsDetailVO getAgentOptionsDetail(UUID id);

    /**
     * 新增智能体选项
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean saveAgentOptions(AgentOptionsForm form);

    /**
     * 更新智能体选项
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean updateAgentOptions(AgentOptionsForm form);

    /**
     * 设置智能体选项归档状态
     *
     * @param id         智能体选项ID
     * @param isArchived 是否归档（true：归档，false：取消归档）
     * @return 是否成功
     */
    boolean setArchiveStatus(UUID id, boolean isArchived);

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    AgentOptionsStatVO getStatistics();

    /**
     * 获取所有智能体
     *
     * @return 所有智能体选项列表
     */
    List<AgentOptionsPageVO> getAllAgentOptions();
}