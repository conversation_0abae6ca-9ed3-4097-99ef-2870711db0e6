package com.hntsz.boot.modules.opportunity.converter;

import com.hntsz.boot.modules.opportunity.model.entity.OpportunityFollow;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityFollowForm;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 商机跟进记录对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpportunityFollowConverter {

    /**
     * 实体转VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    OpportunityFollowVO entityToVo(OpportunityFollow entity);

    /**
     * 表单转实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    OpportunityFollow formToEntity(OpportunityFollowForm form);

    /**
     * 实体转表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    OpportunityFollowForm entityToForm(OpportunityFollow entity);
}