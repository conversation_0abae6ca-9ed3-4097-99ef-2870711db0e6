package com.hntsz.boot.modules.application.controller;

import com.hntsz.boot.common.annotation.RepeatSubmit;
import com.hntsz.boot.modules.application.entity.Application;
import com.hntsz.boot.modules.application.service.ApplicationService;
import com.hntsz.boot.modules.application.vo.ApplicationDetailVO;
import com.hntsz.boot.modules.application.vo.ApplicationPageVO;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.common.base.BasePageQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.application.vo.ApplicationSelectedVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 应用管理控制器
 */
@Tag(name = "应用管理", description = "应用管理相关接口")
@RestController
@RequestMapping("/api/v1/applications")
@RequiredArgsConstructor
public class ApplicationController {

    private final ApplicationService applicationService;

    /**
     * 获取应用分页列表
     */
    @Operation(summary = "获取应用分页列表")
    @GetMapping("/page")
    // @PreAuthorize("@ss.hasPerm('sys:application:query')")
    public PageResult<ApplicationPageVO> getApplicationPage(
            @Parameter(description = "分页查询参数") BasePageQuery queryParams,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String searchKeyword) {
        Page<ApplicationPageVO> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
        IPage<ApplicationPageVO> result = applicationService.getApplicationPage(page, searchKeyword);

        return PageResult.success(result);
    }

    /**
     * 获取应用下拉列表
     */
    @Operation(summary = "获取应用下拉列表")
    @GetMapping("/list")
    public Result<List<ApplicationSelectedVO>> getApplicationList() {
        return Result.success(applicationService.getApplicationSelectedList());
    }

    /**
     * 获取应用详情
     */
    @Operation(summary = "获取应用详情")
    @GetMapping("/{id}")
    // @PreAuthorize("@ss.hasPerm('sys:application:query')")
    public Result<ApplicationDetailVO> getApplicationDetail(
            @Parameter(description = "应用ID") @PathVariable String id) {
        return Result.success(applicationService.getApplicationDetail(id));
    }

    /**
     * 创建应用
     */
    @Operation(summary = "创建应用")
    @PostMapping
    // @PreAuthorize("@ss.hasPerm('sys:application:create')")
    @RepeatSubmit
    public Result<String> createApplication(
            @Parameter(description = "应用信息") @Validated @RequestBody Application application) {
        return Result.judge(applicationService.createApplication(application));

    }

    /**
     * 更新应用
     */
    @Operation(summary = "更新应用")
    @PutMapping
    // @PreAuthorize("@ss.hasPerm('sys:application:edit')")
    @RepeatSubmit
    public Result<String> updateApplication(
            @Parameter(description = "应用信息") @Validated @RequestBody Application application) {
        return Result.judge(applicationService.updateApplication(application));
    }

    /**
     * 删除应用
     */
    @Operation(summary = "删除应用")
    @DeleteMapping("/{id}")
    // @PreAuthorize("@ss.hasPerm('sys:application:delete')")
    public Result<String> deleteApplication(
            @Parameter(description = "应用ID") @PathVariable String id) {
        return Result.judge(applicationService.deleteApplication(id));
    }

}