package com.hntsz.boot.modules.sessionuser.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.sessionuser.model.query.SessionUserPageQuery;
import com.hntsz.boot.modules.sessionuser.model.vo.SessionUserPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SessionUserMapper {

    Page<SessionUserPageVO> getSessionUserPage(@Param("page") Page<SessionUserPageVO> page,
                                               @Param("queryParam") SessionUserPageQuery queryParam);
}
