package com.hntsz.boot.modules.sessionuser.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
public class SessionUserPageVO {

    @Schema(description = "用户唯一标识")
    private String uniqueUserKey;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "会话数量")
    private Long sessionCount;

    @Schema(description = "消息数量")
    private Long messageCount;

    @Schema(description = "会话时长")
    private String preferenceAgent;

    @Schema(description = "智能体名称")
    private String agentDescription;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "位置")
    private String address;

    @Schema(description = "最后活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime lastActiveTime;



}
