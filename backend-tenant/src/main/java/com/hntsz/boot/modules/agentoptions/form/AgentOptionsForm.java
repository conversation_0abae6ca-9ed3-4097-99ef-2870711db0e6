package com.hntsz.boot.modules.agentoptions.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.UUID;

/**
 * 智能体选项表单对象
 *
 * <AUTHOR>
 * @since 2024-01-20
 */
@Data
@Schema(description = "智能体表单")
public class AgentOptionsForm {

    @Schema(description = "智能体ID（编辑时必填）")
    private UUID id;

    @Schema(description = "父级智能体ID")
    private UUID parentId;

    @Schema(description = "智能体名称")
    @NotBlank(message = "智能体名称不能为空")
    @Size(max = 100, message = "智能体名称长度不能超过100个字符")
    private String name;

    @Schema(description = "智能体描述")
    @Size(max = 500, message = "智能体描述长度不能超过500个字符")
    private String description;

    @Schema(description = "智能体标识")
    @Size(max = 50, message = "智能体标识长度不能超过50个字符")
    private String key;

    @Schema(description = "扩展信息")
    private String extra;
}