package com.hntsz.boot.modules.contract.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 合同伙伴关联表查询对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同伙伴关联表查询对象")
public class ContractPartnerRelationQuery extends BasePageQuery {

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private Long contractId;

    /**
     * 伙伴ID
     */
    @Schema(description = "伙伴ID")
    private Long partnerId;

    /**
     * 伙伴角色
     */
    @Schema(description = "伙伴角色")
    private String partnerRole;

    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String partnerRoleDesc;

    /**
     * 签署人
     */
    @Schema(description = "签署人")
    private String signingPerson;

    /**
     * 签署日期范围开始
     */
    @Schema(description = "签署日期范围开始")
    private LocalDate signingDateStart;

    /**
     * 签署日期范围结束
     */
    @Schema(description = "签署日期范围结束")
    private LocalDate signingDateEnd;
}
