package com.hntsz.boot.modules.storedmessage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.session.model.query.SessionMessageQuery;
import com.hntsz.boot.modules.storedmessage.model.vo.StoredMessageVO;

import java.util.List;

public interface StoredMessageService {

    List<StoredMessageVO> getSessionMessageList(String sessionId);

    Page<StoredMessageVO> getSessionMessagePage(SessionMessageQuery queryParam);
}
