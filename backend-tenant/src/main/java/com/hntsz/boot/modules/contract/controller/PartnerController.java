package com.hntsz.boot.modules.contract.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.contract.model.form.PartnerForm;
import com.hntsz.boot.modules.contract.model.query.PartnerQuery;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;
import com.hntsz.boot.modules.contract.service.PartnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务伙伴控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Tag(name = "业务伙伴接口")
@RestController
@RequestMapping("/api/v1/partners")
@RequiredArgsConstructor
public class PartnerController {

    private final PartnerService partnerService;

    @Operation(summary = "业务伙伴分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('partner:query')")
    public PageResult<PartnerVO> getPartnerPage(PartnerQuery queryParams) {
        IPage<PartnerVO> result = partnerService.getPartnerPage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "业务伙伴详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('partner:query')")
    public Result<PartnerVO> getPartnerDetail(
            @Parameter(description = "业务伙伴ID") @PathVariable Long id) {
        PartnerVO partner = partnerService.getPartnerDetail(id);
        return Result.success(partner);
    }

    @Operation(summary = "获取业务伙伴表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('partner:query')")
    public Result<PartnerForm> getPartnerFormData(
            @Parameter(description = "业务伙伴ID") @PathVariable Long id) {
        PartnerForm formData = partnerService.getPartnerFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "新增业务伙伴")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('partner:add')")
    public Result<Void> savePartner(@Validated @RequestBody PartnerForm form) {
        boolean result = partnerService.savePartner(form);
        return Result.judge(result);
    }

    @Operation(summary = "修改业务伙伴")
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('partner:edit')")
    public Result<Void> updatePartner(
            @Parameter(description = "业务伙伴ID") @PathVariable Long id,
            @Validated @RequestBody PartnerForm form) {
        boolean result = partnerService.updatePartner(id, form);
        return Result.judge(result);
    }

    @Operation(summary = "删除业务伙伴")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('partner:delete')")
    public Result<Void> deletePartners(
            @Parameter(description = "业务伙伴ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        String[] idArray = ids.split(",");
        Long[] idLongArray = new Long[idArray.length];
        for (int i = 0; i < idArray.length; i++) {
            idLongArray[i] = Long.parseLong(idArray[i]);
        }
        boolean result = partnerService.deletePartners(idLongArray);
        return Result.judge(result);
    }

    @Operation(summary = "获取业务伙伴选项列表")
    @GetMapping("/options")
    @PreAuthorize("@ss.hasPerm('partner:query')")
    public Result<List<Option>> getPartnerOptions() {
        List<Option> options = partnerService.getPartnerOptions();
        return Result.success(options);
    }
}
