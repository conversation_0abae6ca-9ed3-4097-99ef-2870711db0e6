package com.hntsz.boot.core.handler;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.github.f4b6a3.uuid.UuidCreator;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * mybatis-plus 字段自动填充
 *
 * <AUTHOR>
 * @since 2022/10/14
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * 新增填充创建时间和主键
     *
     * @param metaObject 元数据
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        fillPrimaryKey(metaObject);
        // 填充时间字段
        this.strictInsertFill(metaObject, "createdAt", OffsetDateTime::now, OffsetDateTime.class);
        this.strictInsertFill(metaObject, "updatedAt", OffsetDateTime::now, OffsetDateTime.class);
    }

    /**
     * 更新填充更新时间
     *
     * @param metaObject 元数据
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updatedAt", OffsetDateTime::now, OffsetDateTime.class);
    }

    private void fillPrimaryKey(MetaObject metaObject) {
        // 1. 获取实体类对应的 TableInfo（MyBatis-Plus 的元数据）
        Class<?> clazz = metaObject.getOriginalObject().getClass();
        TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);

        if (tableInfo == null) {
            return;
        }

        // 2. 获取主键字段名（支持自定义字段名）
        String keyProperty = tableInfo.getKeyProperty();
        if (keyProperty == null || keyProperty.isEmpty()) {
            return; // 无主键
        }

        // 3. 检查主键是否已赋值
        Object idVal = getFieldValByName(keyProperty, metaObject);
        if (idVal == null) {
            // 4. 生成 UUIDv7 并填充
            Object uuidV7 = UuidCreator.getTimeOrderedEpoch();
            setFieldValByName(keyProperty, uuidV7, metaObject);
        }
    }
}
