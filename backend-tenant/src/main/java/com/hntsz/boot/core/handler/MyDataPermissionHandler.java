package com.hntsz.boot.core.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.hntsz.boot.core.security.util.SecurityUtils;
import net.sf.jsqlparser.expression.Expression;

/**
 * 数据权限控制器
 *
 * <AUTHOR>
 * @since 2021-12-10 13:28
 */
public class MyDataPermissionHandler implements DataPermissionHandler {

    /**
     * 获取数据权限的sql片段
     *
     * @param where             查询条件
     * @param mappedStatementId mapper接口方法的全路径
     * @return sql片段
     */
    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        // 如果是未登录，或者是定时任务执行的SQL，或者是超级管理员，直接返回
        if (SecurityUtils.getUserId() == null || SecurityUtils.isRoot()) {
            return where;
        }
        // 简化数据权限处理，暂时返回原始where条件
        // TODO: 根据新的User实体结构重新实现数据权限逻辑
        return where;
    }

    // 移除dataScopeFilter方法，因为不再使用数据权限功能

}
