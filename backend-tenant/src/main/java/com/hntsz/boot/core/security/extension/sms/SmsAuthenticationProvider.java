package com.hntsz.boot.core.security.extension.sms;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hntsz.boot.common.constant.RedisConstants;
import com.hntsz.boot.core.security.exception.CaptchaValidationException;
import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;


/**
 * 短信验证码认证 Provider
 *
 * <AUTHOR>
 * @since 2.17.0
 */
@Slf4j
public class SmsAuthenticationProvider implements AuthenticationProvider {

    private final UserService userService;

    private final RedisTemplate<String, Object> redisTemplate;

    public SmsAuthenticationProvider(UserService userService, RedisTemplate<String, Object> redisTemplate) {
        this.userService = userService;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 短信验证码认证
     *
     * @param authentication 短信认证请求
     * @return 认证结果
     * @throws AuthenticationException 认证异常
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String mobile = (String) authentication.getPrincipal();
        String inputVerifyCode = (String) authentication.getCredentials();

        // TODO: 暂时注释掉，等待添加相关方法
        // 根据手机号获取用户信息
        // UserAuthCredentials userAuthCredentials = userService.getAuthCredentialsByMobile(mobile);

        // if (userAuthCredentials == null) {
        //     throw new UsernameNotFoundException("用户不存在");
        // }

        // 验证短信验证码
        String cacheKey = StrUtil.format(RedisConstants.Captcha.SMS_LOGIN_CODE, mobile);
        String cachedVerifyCode = (String) redisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isBlank(cachedVerifyCode)) {
            throw new CaptchaValidationException("验证码已过期，请重新获取");
        }

        if (!ObjectUtil.equal(inputVerifyCode, cachedVerifyCode)) {
            throw new CaptchaValidationException("验证码错误");
        }

        // 验证成功后删除验证码
        redisTemplate.delete(cacheKey);

        // 临时返回，等待实现完整逻辑
        throw new UsernameNotFoundException("短信登录功能暂未完全实现");

        // TODO: 恢复以下代码当相关方法实现后
        // // 检查用户状态是否有效
        // if (!"Active".equals(userAuthCredentials.getStatus())) {
        //     throw new DisabledException("用户已被禁用，请联系管理员");
        // }
        //
        // // 创建已认证的短信认证令牌
        // SmsAuthenticationToken authenticationToken = new SmsAuthenticationToken(
        //         new SysUserDetails(userAuthCredentials), null);
        // authenticationToken.setDetails(authentication.getDetails());
        //
        // return authenticationToken;
    }

    /**
     * 是否支持指定的认证类型
     *
     * @param authentication 认证类型
     * @return 是否支持
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return SmsAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
