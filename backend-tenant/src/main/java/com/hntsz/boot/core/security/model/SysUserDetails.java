package com.hntsz.boot.core.security.model;

import com.hntsz.boot.common.constant.SecurityConstants;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.UUID;

/**
 * Spring Security 用户认证对象
 * <p>
 * 封装了用户的基本信息和权限信息，供 Spring Security 进行用户认证与授权。
 * 实现了 {@link UserDetails} 接口，提供用户的核心信息。
 *
 * <AUTHOR>
 * @version 3.0.0
 */
@Data
@NoArgsConstructor
public class SysUserDetails implements UserDetails {

    /**
     * 用户ID
     */
    private UUID userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 账号是否启用(true:启用 false:禁用)
     */
    private Boolean enabled;

    /**
     * 租户ID
     */
    private UUID tenantId;

    /**
     * 用户角色权限集合
     */
    private Collection<SimpleGrantedAuthority> authorities;

    /**
     * 构造函数：根据用户认证信息初始化用户详情对象
     *
     * @param user 用户认证信息对象 {@link UserAuthCredentials}
     */
    public SysUserDetails(UserAuthCredentials user) {
        this.userId = user.getUserId();
        this.username = user.getUniqueUserKey(); // 使用uniqueUserKey作为username
        this.password = user.getPassword();
        this.enabled = "Active".equals(user.getStatus()); // 使用新的状态值
        this.tenantId = user.getTenantId();

        // 简化权限处理，使用roleKey作为单一角色
        if (user.getRoleKey() != null && !user.getRoleKey().trim().isEmpty()) {
            this.authorities = Collections.singleton(
                    new SimpleGrantedAuthority(SecurityConstants.ROLE_PREFIX + user.getRoleKey()));
        } else {
            this.authorities = Collections.emptySet();
        }
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.username;
    }

    @Override
    public boolean isEnabled() {
        return this.enabled;
    }
}
