package com.hntsz.boot.core.security.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.UUID;

/**
 * 在线用户信息对象
 *
 * <AUTHOR>
 * @since 2025/2/27 10:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OnlineUser {

    /**
     * 用户ID
     */
    private UUID userId;

    /**
     * 用户名（使用uniqueUserKey）
     */
    private String username;

    /**
     * 角色权限集合
     */
    private Set<String> roles;

}
