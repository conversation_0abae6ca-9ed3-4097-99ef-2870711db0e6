package com.hntsz.boot.core.security.extension.wechat;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 微信认证 Provider
 *
 * <AUTHOR>
 * @since 2.17.0
 */
@Slf4j
public class WechatAuthenticationProvider implements AuthenticationProvider {

    private final UserService userService;

    private final WxMaService wxMaService;

    public WechatAuthenticationProvider(UserService userService, WxMaService wxMaService) {
        this.userService = userService;
        this.wxMaService = wxMaService;
    }

    /**
     * 微信认证
     *
     * @param authentication 微信认证请求
     * @return 认证结果
     * @throws AuthenticationException 认证异常
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String code = (String) authentication.getPrincipal();

        WxMaJscode2SessionResult sessionInfo;
        try {
            sessionInfo = wxMaService.getUserService().getSessionInfo(code);
        } catch (WxErrorException e) {
            log.error("微信认证失败: {}", e.getMessage());
            throw new UsernameNotFoundException("微信认证失败，请稍后重试");
        }

        String openId = sessionInfo.getOpenid();
        if (StrUtil.isBlank(openId)) {
            throw new UsernameNotFoundException("未能获取到微信 OpenID，请稍后重试");
        }

        // TODO: 暂时注释掉，等待添加相关方法
        // 根据微信 OpenID 查询用户信息
        // UserAuthCredentials userAuthCredentials = userService.getAuthCredentialsByOpenId(openId);

        // if (userAuthCredentials == null) {
        //     // TODO: 用户不存在则注册，这里需要获取用户手机号并与现有用户绑定
        //     userService.registerOrBindWechatUser(openId);
        //
        //     // 再次查询用户信息，确保用户注册成功
        //     userAuthCredentials = userService.getAuthCredentialsByOpenId(openId);
        //     if (userAuthCredentials == null) {
        //         throw new UsernameNotFoundException("用户注册失败，请稍后重试");
        //     }
        // }

        // 临时返回，等待实现完整逻辑
        throw new UsernameNotFoundException("微信登录功能暂未完全实现");

        // TODO: 恢复以下代码当相关方法实现后
        // // 检查用户状态是否有效
        // if (!"Active".equals(userAuthCredentials.getStatus())) {
        //     throw new DisabledException("用户已被禁用，请联系管理员");
        // }
        //
        // // 创建已认证的微信认证令牌
        // WechatAuthenticationToken authenticationToken = new WechatAuthenticationToken(
        //         new SysUserDetails(userAuthCredentials), null);
        // authenticationToken.setDetails(authentication.getDetails());
        //
        // return authenticationToken;
    }

    /**
     * 是否支持指定的认证类型
     *
     * @param authentication 认证类型
     * @return 是否支持
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return WechatAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
