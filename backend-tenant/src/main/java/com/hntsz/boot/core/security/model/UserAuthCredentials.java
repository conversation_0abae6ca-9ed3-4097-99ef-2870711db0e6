package com.hntsz.boot.core.security.model;

import lombok.Getter;
import lombok.Setter;
import java.util.UUID;

/**
 * 用户认证凭证信息
 *
 * <AUTHOR>
 * @since 2022/10/22
 */
@Getter
@Setter
public class UserAuthCredentials {

    /**
     * 用户ID
     */
    private UUID userId;

    /**
     * 用户唯一标识
     */
    private String uniqueUserKey;

    /**
     * 用户显示名称
     */
    private String displayName;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 状态(Active: 正常, Inactive: 禁用)
     */
    private String status;

    /**
     * 用户角色标识
     */
    private String roleKey;

    /**
     * 租户ID
     */
    private UUID tenantId;

}
