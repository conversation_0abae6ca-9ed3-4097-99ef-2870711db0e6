package com.hntsz.boot.core.handler.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * PostgreSQL 数组类型处理器
 * 处理 PostgreSQL text[] 类型与 Java List<String> 之间的转换
 *
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.ARRAY)
public class StringArrayTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType)
            throws SQLException {
        if (parameter == null || parameter.isEmpty()) {
            ps.setArray(i, ps.getConnection().createArrayOf("text", new String[0]));
        } else {
            ps.setArray(i, ps.getConnection().createArrayOf("text", parameter.toArray(new String[0])));
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return arrayToList(array);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return arrayToList(array);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return arrayToList(array);
    }

    /**
     * 将 SQL Array 转换为 List<String>
     *
     * @param array SQL Array 对象
     * @return List<String> 列表
     * @throws SQLException SQL 异常
     */
    private List<String> arrayToList(Array array) throws SQLException {
        if (array == null) {
            return new ArrayList<>();
        }
        
        Object[] objects = (Object[]) array.getArray();
        if (objects == null || objects.length == 0) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        for (Object obj : objects) {
            if (obj != null) {
                result.add(obj.toString());
            }
        }
        return result;
    }
}