package com.hntsz.boot.core.security.service;

import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 系统用户认证 DetailsService
 *
 * <AUTHOR>
 * @since 2021/10/19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysUserDetailsService implements UserDetailsService {

    private final UserService userService;

    /**
     * 根据用户唯一标识获取用户信息
     *
     * @param uniqueUserKey 用户唯一标识
     * @return 用户信息
     * @throws UsernameNotFoundException 用户名未找到异常
     */
    @Override
    public UserDetails loadUserByUsername(String uniqueUserKey) throws UsernameNotFoundException {
        try {
            UserAuthCredentials userAuthCredentials = userService.getAuthCredentialsByUniqueUserKey(uniqueUserKey);
            if (userAuthCredentials == null) {
                throw new UsernameNotFoundException(uniqueUserKey);
            }
            return new SysUserDetails(userAuthCredentials);
        } catch (Exception e) {
            // 记录异常日志
            log.error("认证异常:{}", e.getMessage());
            // 抛出异常
            throw e;
        }
    }
}
