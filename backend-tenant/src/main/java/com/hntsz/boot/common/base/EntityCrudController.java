package com.hntsz.boot.common.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public abstract class EntityCrudController<T, M extends BaseMapper<T>> extends BaseCrudController<T, BasePageQuery, T, T, T, T, M> {
    
    public EntityCrudController(M baseMapper) {
        super(baseMapper);
    }

    @Override
    protected T convertToVO(T entity) {
        return entity;
    }

    @Override
    protected T convertCfToEntity(T form) {
        return form;
    }

    @Override
    protected T convertUfToEntity(T form) {
        return form;
    }
    
}
