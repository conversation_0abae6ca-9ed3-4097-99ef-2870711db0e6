package com.hntsz.boot.common.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import com.hntsz.boot.common.result.PageResult;
/**
 * 通用 CRUD 控制器约定
 *
 * @param <T> 实体类型
 * @param <Q> 查询条件类型
 * @param <V> VO类型
 * @param <D> 详情VO类型
 * @param <CF> Create Form类型
 * @param <UF> Update Form类型
 */
public abstract class ContractCrudController<T, Q extends BasePageQuery, V, D, CF, UF> {

    protected abstract PageResult<V> listImplement(Q query);

    @Operation(summary = "分页列表")
    @GetMapping
    public PageResult<V> list(@Valid @Parameter(description = "查询条件") Q query){
        return listImplement(query);
    }

    protected abstract Result<D> getImplement(Long id);

    @Operation(summary = "获取详情")
    @GetMapping("/{id}")
    public Result<D> get(@Valid @Parameter(description = "ID") @PathVariable Long id){
        return getImplement(id);
    }

    protected abstract Result<?> addImplement(CF form);

    @Operation(summary = "新增")
    @PostMapping
    public Result<?> add(@Valid @RequestBody CF form){
        return addImplement(form);
    }

    protected abstract Result<?> updateImplement(Long id, UF form);

    @Operation(summary = "修改")
    @PutMapping("/{id}")
    public Result<?> update(
            @Parameter(description = "ID") @PathVariable Long id,
            @Valid @RequestBody UF form
    ){
        return updateImplement(id, form);
    }

    protected abstract Result<?> deleteImplement(Long[] ids);

    @Operation(summary = "删除")
    @DeleteMapping("/{ids}")
    public Result<?> deleteByIds(@Valid @Parameter(description = "ID") @PathVariable Long[] ids){
        return deleteImplement(ids);
    }

} 