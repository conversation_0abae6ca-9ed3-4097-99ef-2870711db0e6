package com.hntsz.boot.system.controller;

import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.system.model.dto.TenantInfoDTO;
import com.hntsz.boot.system.model.dto.UpdateTenantInfoRequest;
import com.hntsz.boot.system.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 租户信息管理控制器
 */
@Tag(name = "租户信息管理")
@RestController
@RequestMapping("/api/v1/tenant")
@RequiredArgsConstructor
@Slf4j
public class TenantController {

    private final TenantService tenantService;

    /**
     * 获取当前租户信息
     *
     * @return 租户信息
     */
    @Operation(summary = "获取当前租户信息")
    @GetMapping("/info")
    public Result<TenantInfoDTO> getTenantInfo() {
        log.info("获取当前租户信息");
        TenantInfoDTO tenantInfo = tenantService.getCurrentTenantInfo();
        return Result.success(tenantInfo);
    }

    /**
     * 更新租户信息
     *
     * @param request 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新租户信息")
    @PutMapping("/info")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> updateTenantInfo(@Validated @RequestBody UpdateTenantInfoRequest request) {
        log.info("更新租户信息: {}", request);
        tenantService.updateTenantInfo(request);
        return Result.success();
    }
}