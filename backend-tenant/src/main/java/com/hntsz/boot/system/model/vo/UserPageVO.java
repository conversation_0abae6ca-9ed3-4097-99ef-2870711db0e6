package com.hntsz.boot.system.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 用户分页视图对象
 *
 * <AUTHOR>
 * @since 2022/1/15 9:41
 */
@Schema(description ="用户分页对象")
@Data
public class UserPageVO {

    @Schema(description="id")
    private UUID id;

    @Schema(description="用户ID")
    private String uniqueUserKey;

    @Schema(description="用户名")
    private String displayName;

    @Schema(description="性别")
    private Integer gender;

    @Schema(description="用户状态")
    private String status;

    @Schema(description="角色名称")
    private String roleKey;

    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private OffsetDateTime createdAt;

    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private OffsetDateTime updatedAt;

    @Schema(description="租户ID")
    private String tenantId;
}
