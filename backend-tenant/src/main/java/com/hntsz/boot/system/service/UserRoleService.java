package com.hntsz.boot.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.system.model.entity.UserRole;

import java.util.List;
import java.util.UUID;

public interface UserRoleService extends IService<UserRole> {

    /**
     * 保存用户角色
     *
     * @param userId
     * @param roleIds
     * @return
     */
    boolean saveUserRoles(UUID userId, List<UUID> roleIds);

    /**
     * 判断角色是否存在绑定的用户
     *
     * @param roleId 角色ID
     * @return true：已分配 false：未分配
     */
    boolean hasAssignedUsers(UUID roleId);
}
