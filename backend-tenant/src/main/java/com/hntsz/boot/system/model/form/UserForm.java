package com.hntsz.boot.system.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.UUID;

/**
 * 用户表单对象
 *
 * <AUTHOR>
 * @since 2022/4/12 11:04
 */
@Schema(description = "用户表单对象")
@Getter
@Setter
public class UserForm {

    @Schema(description = "用户ID")
    private UUID id;

    @Schema(description = "用户唯一标识")
    @NotBlank(message = "用户唯一标识不能为空")
    private String uniqueUserKey;

    @Schema(description = "用户显示名称")
    @NotBlank(message = "用户显示名称不能为空")
    private String displayName;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "手机号码")
    @Pattern(regexp = "^$|^1(3\\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$", message = "手机号码格式不正确")
    private String phone;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "用户头像")
    private String avatarUrl;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户状态(Active:正常;Inactive:禁用)")
    private String status;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "用户角色标识")
    private List<String> roleKey;

    @Schema(description = "租户ID")
    private String tenantId;

}
