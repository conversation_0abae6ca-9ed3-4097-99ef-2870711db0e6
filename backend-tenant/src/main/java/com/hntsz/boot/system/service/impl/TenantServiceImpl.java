package com.hntsz.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hntsz.boot.common.exception.BusinessException;
import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.util.SecurityUtils;
import com.hntsz.boot.system.mapper.TenantMapper;
import com.hntsz.boot.system.model.dto.TenantInfoDTO;
import com.hntsz.boot.system.model.dto.UpdateTenantInfoRequest;
import com.hntsz.boot.system.model.entity.Tenant;
import com.hntsz.boot.system.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * 租户服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TenantServiceImpl implements TenantService {

    private final TenantMapper tenantMapper;

    /**
     * 获取当前租户信息
     *
     * @return 租户信息
     */
    @Override
    public TenantInfoDTO getCurrentTenantInfo() {
        Optional<SysUserDetails> userOptional = SecurityUtils.getUser();
        if (userOptional.isEmpty()) {
            throw new BusinessException("用户未登录");
        }

        SysUserDetails currentUser = userOptional.get();
        UUID tenantId = currentUser.getTenantId();
        if (tenantId == null) {
            throw new BusinessException("当前用户未关联租户信息");
        }

        Tenant tenant = tenantMapper.selectOne(
                new LambdaQueryWrapper<Tenant>()
                        .eq(Tenant::getId, tenantId));
        if (tenant == null) {
            throw new BusinessException("租户信息不存在");
        }

        TenantInfoDTO dto = BeanUtil.copyProperties(tenant, TenantInfoDTO.class);
        dto.setTenantId(tenant.getId());
        return dto;
    }

    /**
     * 更新租户信息
     *
     * @param request 更新请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantInfo(UpdateTenantInfoRequest request) {
        Optional<SysUserDetails> userOptional = SecurityUtils.getUser();
        if (userOptional.isEmpty()) {
            throw new BusinessException("用户未登录");
        }

        SysUserDetails currentUser = userOptional.get();
        UUID tenantId = currentUser.getTenantId();
        if (tenantId == null) {
            throw new BusinessException("当前用户未关联租户信息");
        }
        Tenant tenant = tenantMapper.selectOne(
                new LambdaQueryWrapper<Tenant>()
                        .eq(Tenant::getId, tenantId));
        if (tenant == null) {
            throw new BusinessException("租户信息不存在");
        }

        int updateCount = tenantMapper.update(null,
                new LambdaUpdateWrapper<Tenant>()
                        .eq(Tenant::getId, tenantId)
                        .set(Tenant::getName, request.getName())
                        .set(Tenant::getDescription, request.getDescription()));
        if (updateCount == 0) {
            throw new BusinessException("更新租户信息失败");
        }

        log.info("租户信息更新成功，租户ID: {}, 名称: {}, 描述: {}", tenantId, request.getName(), request.getDescription());
    }
}