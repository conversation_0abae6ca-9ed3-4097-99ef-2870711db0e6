package com.hntsz.boot.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

/**
 * 角色实体
 *
 * <AUTHOR>
 * @since 2024/6/23
 */
@TableName("\"Roles\"")
@Getter
@Setter
public class Role {

    @TableId(value = "\"Id\"", type = IdType.ASSIGN_UUID)
    private UUID id;

    /**
     * 角色名称
     */
    @TableField("\"Name\"")
    private String name;

    /**
     * 角色编码
     */
    @TableField("\"Code\"")
    private String code;

    /**
     * 显示顺序
     */
    @TableField("\"Sort\"")
    private Integer sort;

    /**
     * 角色状态(1-正常 0-停用)
     */
    @TableField("\"Status\"")
    private String status;

    /**
     * 数据权限(1-所有数据 2-部门及子部门数据 3-本部门数据 4-本人数据)
     */
    @TableField("\"DataScope\"")
    private String dataScope;

    /**
     * 创建人 ID
     */
    @TableField("\"CreateBy\"")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("\"CreateTime\"")
    private Instant createTime;

    /**
     * 更新人 ID
     */
    @TableField("\"UpdateBy\"")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("\"UpdateTime\"")
    private Instant updateTime;

    /**
     * 是否删除(0-否 1-是)
     */
    @TableLogic(value = "false", delval = "true")
    @TableField("\"IsDeleted\"")
    private Boolean isDeleted;
}