package com.hntsz.boot.system.service;

import com.hntsz.boot.system.model.dto.TenantInfoDTO;
import com.hntsz.boot.system.model.dto.UpdateTenantInfoRequest;

/**
 * 租户服务接口
 */
public interface TenantService {

    /**
     * 获取当前租户信息
     *
     * @return 租户信息
     */
    TenantInfoDTO getCurrentTenantInfo();

    /**
     * 更新租户信息
     *
     * @param request 更新请求
     */
    void updateTenantInfo(UpdateTenantInfoRequest request);
}