package com.hntsz.boot.system.model.bo;

import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 用户持久化对象
 *
 * <AUTHOR>
 * @since 2022/6/10
 */
@Data
public class UserBO {

    /**
     * 用户ID
     */
    private UUID id;

    /**
     * 账户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别(1->男；2->女)
     */
    private Integer gender;

    /**
     * 状态: 1->启用;0->禁用
     */
    private String status;

    /**
     * 角色名称，多个使用英文逗号(,)分割
     */
    private String roleNames;

    /**
     * 创建时间
     */
    private OffsetDateTime createdTime;
}
