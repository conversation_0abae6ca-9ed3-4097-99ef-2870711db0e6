package com.hntsz.boot.system.model.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 个人中心用户信息
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Schema(description = "个人中心用户信息")
@Data
public class UserProfileVO {

    @Schema(description = "用户ID")
    private String id;

    @Schema(description = "用户名")
    private String uniqueUserKey;

    @Schema(description = "用户昵称")
    private String displayName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "所属部门")
    private String department;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "登录次数")
    private Integer loginCount;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime lastLoginAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime updatedAt;

    @Schema(description = "角色名称")
    private List<String> roleKeyList;

    @Schema(description = "租户ID")
    private UUID tenantId;
}
