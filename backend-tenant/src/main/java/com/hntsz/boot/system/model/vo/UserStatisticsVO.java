package com.hntsz.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description ="用户统计数据对象")
public class UserStatisticsVO {
    
    @Schema(description="总成员数")
    private Long totalCount = 0L;
    
    @Schema(description="活跃成员数量")
    private Long activeCount = 0L;
    
    @Schema(description="禁用成员数量")
    private Long inactiveCount = 0L;
    
    @Schema(description="管理员数量")
    private Long adminCount = 0L;
}
