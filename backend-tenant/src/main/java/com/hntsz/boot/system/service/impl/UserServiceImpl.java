package com.hntsz.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.f4b6a3.uuid.UuidCreator;
import com.hntsz.boot.common.constant.RedisConstants;
import com.hntsz.boot.common.constant.SystemConstants;
import com.hntsz.boot.common.exception.BusinessException;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.core.security.token.TokenManager;
import com.hntsz.boot.core.security.service.PermissionService;
import com.hntsz.boot.core.security.util.SecurityUtils;
import com.hntsz.boot.shared.mail.service.MailService;
import com.hntsz.boot.shared.sms.enums.SmsTypeEnum;
import com.hntsz.boot.shared.sms.service.SmsService;
import com.hntsz.boot.system.converter.UserConverter;
import com.hntsz.boot.system.enums.DictCodeEnum;
import com.hntsz.boot.system.mapper.UserMapper;
import com.hntsz.boot.system.model.bo.UserBO;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.model.dto.CurrentUserDTO;
import com.hntsz.boot.system.model.dto.UserExportDTO;
import com.hntsz.boot.system.model.entity.DictItem;
import com.hntsz.boot.system.model.entity.User;
import com.hntsz.boot.system.model.entity.UserRole;
import com.hntsz.boot.system.model.form.*;
import com.hntsz.boot.system.model.query.UserPageQuery;
import com.hntsz.boot.system.model.vo.UserPageVO;
import com.hntsz.boot.system.model.vo.UserProfileVO;
import com.hntsz.boot.system.model.vo.UserStatisticsVO;
import com.hntsz.boot.system.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户业务实现类
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final PasswordEncoder passwordEncoder;

    private final UserRoleService userRoleService;

    private final RoleService roleService;

    private final PermissionService permissionService;

    private final SmsService smsService;

    private final MailService mailService;

    private final StringRedisTemplate redisTemplate;

    private final TokenManager tokenManager;

    private final DictItemService dictItemService;

    private final UserConverter userConverter;

    /**
     * 获取用户分页列表
     *
     * @param queryParams 查询参数
     * @return {@link IPage<UserPageVO>} 用户分页列表
     */
    @Override
    public IPage<UserPageVO> getUserPage(UserPageQuery queryParams) {

        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<User> page = new Page<>(pageNum, pageSize);

        boolean isRoot = SecurityUtils.isRoot();
        queryParams.setIsRoot(isRoot);

        // 查询数据
        Page<User> userPage = this.baseMapper.getUserPage(page, queryParams);

        // 实体转换
        return userConverter.toPageVo(userPage);
    }

    /**
     * 获取用户表单数据
     *
     * @param userId 用户ID
     * @return {@link UserForm} 用户表单数据
     */
    @Override
    public UserForm getUserFormData(UUID userId) {
        return this.baseMapper.getUserFormData(userId);
    }

    /**
     * 新增用户
     *
     * @param userForm 用户表单对象
     * @return true|false
     */
    @Override
    public boolean saveUser(UserForm userForm) {

        String uniqueUserKey = userForm.getUniqueUserKey();

        long count = this.count(new LambdaQueryWrapper<User>().eq(User::getUniqueUserKey, uniqueUserKey));
        Assert.isTrue(count == 0, "用户标识已存在");

        // 实体转换 form->entity
        User entity = userConverter.toEntity(userForm);

        // 设置默认加密密码
        if (StrUtil.isBlank(entity.getPassword())) {
            String defaultEncryptPwd = passwordEncoder.encode(SystemConstants.DEFAULT_PASSWORD);
            entity.setPassword(defaultEncryptPwd);
        } else {
            entity.setPassword(passwordEncoder.encode(entity.getPassword()));
        }

        if (ObjectUtil.isNotEmpty(userForm.getRoleKey())) {
            String roleKey = String.join(",", userForm.getRoleKey());
            entity.setRoleKey(roleKey);
        }
        UUID tenantId = SecurityUtils.getTenantId();
        entity.setTenantId(tenantId);

        // 新增用户
        return this.save(entity);
    }

    /**
     * 更新用户
     *
     * @param profileForm 用户表单对象
     * @return true|false
     */
    @Override
    @Transactional
    public boolean updateUser(UserProfileForm profileForm) {

        // 校验用户唯一标识是否存在
        String uniqueUserKey = profileForm.getUniqueUserKey();

        long count = this.count(new LambdaQueryWrapper<User>()
                .eq(User::getUniqueUserKey, uniqueUserKey)
                .ne(User::getId, profileForm.getId()));
        Assert.isTrue(count == 0, "用户标识已存在");

        // form -> entity
        User entity = userConverter.toEntity(profileForm);

        if (ObjectUtil.isNotEmpty(profileForm.getRoleKeyList())) {
            String roleKey = String.join(",", profileForm.getRoleKeyList());
            entity.setRoleKey(roleKey);
        }
        // 修改用户
        return this.updateById(entity);
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return true|false
     */
    @Override
    public boolean deleteUserById(String id) {
        Assert.isTrue(StrUtil.isNotBlank(id), "删除的用户数据为空");
        return this.removeById(UUID.fromString(id));

    }

    /**
     * 根据用户唯一标识获取认证信息
     *
     * @param uniqueUserKey 用户唯一标识
     * @return {@link UserAuthCredentials}
     */
    @Override
    public UserAuthCredentials getAuthCredentialsByUniqueUserKey(String uniqueUserKey) {
        return this.baseMapper.getAuthCredentialsByUniqueUserKey(uniqueUserKey);
    }

    /**
     * 根据微信 OpenID 获取用户认证信息
     *
     * @param openId 微信 OpenID
     * @return {@link UserAuthCredentials}
     */
    @Override
    public UserAuthCredentials getAuthCredentialsByOpenId(String openId) {
        // TODO: 暂时返回null，等待完整实现
        return this.baseMapper.getAuthCredentialsByOpenId(openId);
    }

    /**
     * 根据微信 OpenID 注册或绑定用户
     *
     * @param openId 微信 OpenID
     */
    @Override
    public void registerOrBindWechatUser(String openId) {
        // TODO: 暂时注释，等待完整实现
        // User user = this.getOne(
        // new LambdaQueryWrapper<User>().eq(User::getWechatOpenId, openId)
        // );
        // if (user == null) {
        // // 用户不存在，创建新用户
        // user = new User();
        // user.setWechatOpenId(openId);
        // user.setUniqueUserKey("wx_" + openId.substring(openId.length() - 8));
        // user.setDisplayName("微信用户");
        // user.setStatus("Active");
        // this.save(user);
        // }
    }

    /**
     * 根据手机号获取用户认证信息
     *
     * @param mobile 手机号
     * @return {@link UserAuthCredentials}
     */
    @Override
    public UserAuthCredentials getAuthCredentialsByMobile(String mobile) {
        // TODO: 暂时返回null，等待完整实现
        return this.baseMapper.getAuthCredentialsByMobile(mobile);
    }

    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     * @return {@link List<UserExportDTO>} 导出用户列表
     */
    @Override
    public List<UserExportDTO> listExportUsers(UserPageQuery queryParams) {

        boolean isRoot = SecurityUtils.isRoot();
        queryParams.setIsRoot(isRoot);

        List<UserExportDTO> exportUsers = this.baseMapper.listExportUsers(queryParams);
        if (CollectionUtil.isNotEmpty(exportUsers)) {
            // 获取性别的字典项
            Map<String, String> genderMap = dictItemService.list(
                    new LambdaQueryWrapper<DictItem>().eq(DictItem::getDictCode,
                            DictCodeEnum.GENDER.getValue()))
                    .stream()
                    .collect(Collectors.toMap(DictItem::getValue, DictItem::getLabel));

            exportUsers.forEach(item -> {
                String gender = item.getGender();
                if (StrUtil.isBlank(gender)) {
                    return;
                }

                // 判断map是否为空
                if (genderMap.isEmpty()) {
                    return;
                }

                item.setGender(genderMap.get(gender));
            });
        }
        return exportUsers;
    }

    /**
     * 获取登录用户信息
     *
     * @return {@link CurrentUserDTO} 用户信息
     */
    @Override
    public CurrentUserDTO getCurrentUserInfo() {

        String username = SecurityUtils.getUsername();

        // 获取登录用户基础信息
        User user = this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUniqueUserKey, username)
                .select(
                        User::getId,
                        User::getUniqueUserKey,
                        User::getDisplayName,
                        User::getAvatarUrl));
        // entity->VO
        CurrentUserDTO userInfoVO = userConverter.toCurrentUserDto(user);

        // 简化权限处理，使用roleKey
        if (user.getRoleKey() != null) {
            userInfoVO.setRoles(Set.of(user.getRoleKey()));
        } else {
            userInfoVO.setRoles(Collections.emptySet());
        }

        // 暂时不处理权限集合
        userInfoVO.setPerms(Collections.emptySet());
        return userInfoVO;
    }

    /**
     * 获取个人中心用户信息
     *
     * @param userId 用户ID
     * @return {@link UserProfileVO} 个人中心用户信息
     */
    @Override
    public UserProfileVO getUserProfile(UUID userId) {
        User entity = this.baseMapper.selectById(userId);
        UserProfileVO profileVo = userConverter.toProfileVo(entity);
        List<String> roleKeyList = Optional.ofNullable(entity.getRoleKey()).filter(key -> !key.trim().isEmpty())
                .map(key -> Arrays.stream(key.split(",")).map(String::trim).collect(Collectors.toList())).orElse(Collections.emptyList());
        profileVo.setRoleKeyList(roleKeyList);
        return profileVo;
    }

    /**
     * 修改个人中心用户信息
     *
     * @param formData 表单数据
     * @return true|false
     */
    @Override
    public boolean updateUserProfile(UserProfileForm formData) {
        UUID userId = SecurityUtils.getUserId();
        User entity = userConverter.toEntity(formData);
        entity.setId(userId);
        return this.updateById(entity);
    }

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param data   密码修改表单数据
     * @return true|false
     */
    @Override
    public boolean changePassword(UUID userId, PasswordUpdateForm data) {

        User user = this.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        String oldPassword = data.getOldPassword();

        // 校验原密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码错误");
        }
        // 新旧密码不能相同
        if (passwordEncoder.matches(data.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与原密码相同");
        }

        String newPassword = data.getNewPassword();
        boolean result = this.update(new LambdaUpdateWrapper<User>()
                .eq(User::getId, userId)
                .set(User::getPassword, passwordEncoder.encode(newPassword)));

        if (result) {
            // 加入黑名单，重新登录
            String accessToken = SecurityUtils.getTokenFromRequest();
            tokenManager.invalidateToken(accessToken);
        }
        return result;
    }

    /**
     * 重置密码
     *
     * @param userId   用户ID
     * @param password 密码重置表单数据
     * @return true|false
     */
    @Override
    public boolean resetPassword(String userId, String password) {
        return this.update(new LambdaUpdateWrapper<User>()
                .eq(User::getId, UUID.fromString(userId))
                .set(User::getPassword, passwordEncoder.encode(password)));
    }

    /**
     * 发送短信验证码(绑定或更换手机号)
     *
     * @param mobile 手机号
     * @return true|false
     */
    @Override
    public boolean sendMobileCode(String mobile) {

        // String code = String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
        // TODO 为了方便测试，验证码固定为 1234，实际开发中在配置了厂商短信服务后，可以使用上面的随机验证码
        String code = "1234";

        Map<String, String> templateParams = new HashMap<>();
        templateParams.put("code", code);
        boolean result = smsService.sendSms(mobile, SmsTypeEnum.CHANGE_MOBILE, templateParams);
        if (result) {
            // 缓存验证码，5分钟有效，用于更换手机号校验
            String redisCacheKey = StrUtil.format(RedisConstants.Captcha.MOBILE_CODE, mobile);
            redisTemplate.opsForValue().set(redisCacheKey, code, 5, TimeUnit.MINUTES);
        }
        return result;
    }

    /**
     * 绑定或更换手机号
     *
     * @param form 表单数据
     * @return true|false
     */
    @Override
    public boolean bindOrChangeMobile(MobileUpdateForm form) {

        UUID currentUserId = SecurityUtils.getUserId();
        User currentUser = this.getById(currentUserId);

        if (currentUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 校验验证码
        String inputVerifyCode = form.getCode();
        String mobile = form.getMobile();

        String cacheKey = StrUtil.format(RedisConstants.Captcha.MOBILE_CODE, mobile);

        String cachedVerifyCode = redisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isBlank(cachedVerifyCode)) {
            throw new BusinessException("验证码已过期");
        }
        if (!inputVerifyCode.equals(cachedVerifyCode)) {
            throw new BusinessException("验证码错误");
        }
        // 验证完成删除验证码
        redisTemplate.delete(cacheKey);

        // 更新手机号码
        // TODO: 暂时注释
        // return this.update(
        // new LambdaUpdateWrapper<User>()
        // .eq(User::getId, currentUserId)
        // .set(User::getMobile, mobile)
        // );
        return true;
    }

    /**
     * 发送邮箱验证码（绑定或更换邮箱）
     *
     * @param email 邮箱
     */
    @Override
    public void sendEmailCode(String email) {

        // String code = String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
        // TODO 为了方便测试，验证码固定为 1234，实际开发中在配置了邮箱服务后，可以使用上面的随机验证码
        String code = "1234";

        mailService.sendMail(email, "邮箱验证码", "您的验证码为：" + code + "，请在5分钟内使用");
        // 缓存验证码，5分钟有效，用于更换邮箱校验
        String redisCacheKey = StrUtil.format(RedisConstants.Captcha.EMAIL_CODE, email);
        redisTemplate.opsForValue().set(redisCacheKey, code, 5, TimeUnit.MINUTES);
    }

    /**
     * 修改当前用户邮箱
     *
     * @param form 表单数据
     * @return true|false
     */
    @Override
    public boolean bindOrChangeEmail(EmailUpdateForm form) {

        UUID currentUserId = SecurityUtils.getUserId();

        User currentUser = this.getById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 获取前端输入的验证码
        String inputVerifyCode = form.getCode();

        // 获取缓存的验证码
        String email = form.getEmail();
        String redisCacheKey = RedisConstants.Captcha.EMAIL_CODE + email;
        String cachedVerifyCode = redisTemplate.opsForValue().get(redisCacheKey);

        if (StrUtil.isBlank(cachedVerifyCode)) {
            throw new BusinessException("验证码已过期");
        }

        if (!inputVerifyCode.equals(cachedVerifyCode)) {
            throw new BusinessException("验证码错误");
        }
        // 验证完成删除验证码
        redisTemplate.delete(redisCacheKey);

        // 更新邮箱地址
        // return this.update(
        // new LambdaUpdateWrapper<User>()
        // .eq(User::getId, currentUserId)
        // .set(User::getEmail, email)
        // );
        return true;
    }

    /**
     * 获取用户选项列表
     *
     * @return {@link List<Option<String>>} 用户选项列表
     */
    @Override
    public List<Option<String>> listUserOptions() {
        List<User> list = this.list(new LambdaQueryWrapper<User>()
                .eq(User::getStatus, 1));
        return userConverter.toOptions(list);
    }

    /**
     * 更新用户登录信息（登录次数和最后登录时间）
     *
     * @param userId 用户ID
     * @return {@link Boolean} 是否更新成功
     */
    @Override
    public boolean updateLoginInfo(UUID userId) {
        // 使用原生SQL来处理UUID类型转换
        return this.baseMapper.updateLoginInfo(userId.toString());
    }

    /**
     * 获取用户统计信息
     *
     * @return {@link UserStatisticsVO} 用户统计信息
     */
    @Override
    public UserStatisticsVO getStatisticsInfo() {
        return this.baseMapper.getStatisticsInfo();
    }

}
