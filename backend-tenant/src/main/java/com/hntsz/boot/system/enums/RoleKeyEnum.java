package com.hntsz.boot.system.enums;

import lombok.Getter;

@Getter
public enum RoleKeyEnum {
    /**
     * 管理员 - 拥有最高权限
     */
    ADMIN("ADMIN", "管理员"),

    /**
     * 操作员 - 负责日常业务操作
     */
    OPERATOR("OPERATOR", "操作员"),

    /**
     * 技术支持 - 提供技术维护与支持
     */
    SUPPORT("SUPPORT", "技术支持"),

    /**
     * 查看者 - 仅具有查看权限
     */
    VIEWER("VIEWER", "查看者"),

    /**
     * 测试员 - 负责系统测试工作
     */
    TESTER("TESTER", "测试员");

    /**
     * 角色键（通常用于权限判断、数据库存储等）
     */
    private final String key;

    /**
     * 角色中文名称（用于展示）
     */
    private final String label;

    RoleKeyEnum(String key, String label) {
        this.key = key;
        this.label = label;
    }

    /**
     * 根据 key 获取对应的枚举值
     */
    public static RoleKeyEnum fromKey(String key) {
        for (RoleKeyEnum role : RoleKeyEnum.values()) {
            if (role.key.equals(key)) {
                return role;
            }
        }
        return null;
    }

    /**
     * 根据 label 获取对应的枚举值
     */
    public static RoleKeyEnum fromLabel(String label) {
        for (RoleKeyEnum role : RoleKeyEnum.values()) {
            if (role.label.equals(label)) {
                return role;
            }
        }
        return null;
    }
}
