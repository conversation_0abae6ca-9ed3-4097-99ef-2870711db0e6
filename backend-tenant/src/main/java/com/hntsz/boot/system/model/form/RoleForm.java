package com.hntsz.boot.system.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

import java.util.UUID;

@Schema(description = "角色表单对象")
@Data
public class RoleForm {

    @Schema(description = "角色ID")
    private UUID id;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String name;

    @Schema(description = "角色编码")
    @NotBlank(message = "角色编码不能为空")
    private String code;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "角色状态(1-正常；0-停用)")
    private String status;

    @Schema(description = "数据权限")
    private String dataScope;

}
