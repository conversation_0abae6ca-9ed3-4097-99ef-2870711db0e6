package com.hntsz.boot.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 租户实体
 */
@Getter
@Setter
@TableName("\"Tenants\"")
public class Tenant {

    /**
     * 租户ID
     */
    @TableId(value = "\"Id\"")
    private UUID id;

    /**
     * 租户标识
     */
    @TableField("\"Key\"")
    private String key;

    /**
     * 租户名称
     */
    @TableField("\"Name\"")
    private String name;

    /**
     * 租户描述
     */
    @TableField("\"Description\"")
    private String description;

    /**
     * 租户密钥
     */
    @TableField("\"SecretKey\"")
    private String secretKey;

    /**
     * 是否禁用
     */
    @TableField("\"Disabled\"")
    private Boolean disabled;

    /**
     * 创建时间
     */
    @TableField(value = "\"CreatedAt\"", fill = FieldFill.INSERT)
    private OffsetDateTime createdAt;
}