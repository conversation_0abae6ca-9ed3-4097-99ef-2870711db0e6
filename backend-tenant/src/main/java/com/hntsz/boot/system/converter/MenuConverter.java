package com.hntsz.boot.system.converter;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.hntsz.boot.system.model.entity.Menu;
import com.hntsz.boot.system.model.form.MenuForm;
import com.hntsz.boot.system.model.vo.MenuVO;

/**
 * 菜单对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/26
 */
@Mapper(componentModel = "spring")
public interface MenuConverter {

    MenuVO toVo(Menu entity);

    @Mapping(target = "params", ignore = true)
    MenuForm toForm(Menu entity);

    @Mapping(target = "params", ignore = true)
    Menu toEntity(MenuForm menuForm);

}