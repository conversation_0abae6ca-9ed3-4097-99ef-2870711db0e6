package com.hntsz.boot.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新租户信息请求DTO
 */
@Schema(description = "更新租户信息请求DTO")
@Data
public class UpdateTenantInfoRequest {

    @Schema(description = "租户名称", required = true)
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 1, max = 100, message = "租户名称长度必须在1-100字符之间")
    private String name;

    @Schema(description = "租户描述")
    @Size(max = 500, message = "租户描述长度不能超过500字符")
    private String description;
}