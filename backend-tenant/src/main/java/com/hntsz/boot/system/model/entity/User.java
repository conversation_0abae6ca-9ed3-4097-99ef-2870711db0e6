package com.hntsz.boot.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 用户实体
 */
@Getter
@Setter
@TableName("\"TenantUsers\"")
public class User {

    @TableId(value = "\"Id\"")
    private UUID id;

    /**
     * 用户唯一key
     */
    @TableField("\"UniqueUserKey\"")
    private String uniqueUserKey;

    /**
     * 密码
     */
    @TableField("\"PasswordHash\"")
    private String password;

    /**
     * 用户显示名称
     */
    @TableField("\"DisplayName\"")
    private String displayName;

    /**
     * 用户角色标识
     */
    @TableField("\"RoleKey\"")
    private String roleKey;

    /**
     * 性别((1-男 2-女 0-保密)
     */
    @TableField("\"Gender\"")
    private Integer gender;

    /**
     * 状态(Active: 正常, Inactive: 禁用)
     */
    @TableField("\"Status\"")
    private String status;

    /**
     * 邮箱
     */
    @TableField("\"Email\"")
    private String email;

    /**
     * 手机号
     */
    @TableField("\"PhoneNumber\"")
    private String phone;

    /**
     * 职位
     */
    @TableField("\"Position\"")
    private String position;

    /**
     * 部门
     */
    @TableField("\"Department\"")
    private String department;

    /**
     * 描述
     */
    @TableField("\"Description\"")
    private String description;

    /**
     * 头像
     */
    @TableField("\"AvatarUrl\"")
    private String avatarUrl;


    /**
     * 登录次数
     */
    @TableField("\"LoginCount\"")
    private Integer loginCount;

    /**
     * 最后登录时间
     */
    @TableField("\"LastLoginAt\"")
    private OffsetDateTime lastLoginAt;


    /**
     * 创建时间
     */
    @TableField(value = "\"CreatedAt\"", fill = FieldFill.INSERT)
    private OffsetDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "\"UpdatedAt\"", fill = FieldFill.INSERT_UPDATE)
    private OffsetDateTime updatedAt;

    /**
     * 租户ID
     */
    @TableField(value = "\"TenantId\"")
    private UUID tenantId;

    /**
     * 微信 OpenID
     */
    // private String openid;
}