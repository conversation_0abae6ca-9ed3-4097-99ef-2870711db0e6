package com.hntsz.boot.system.enums;

import com.hntsz.boot.common.base.IBaseEnum;

import lombok.Getter;

/**
 * 字典编码枚举
 *
 * <AUTHOR>
 * @since 2024/10/30
 */
@Getter
public enum DictCodeEnum implements IBaseEnum<String> {

    GENDER("gender", "性别"),
    NOTICE_TYPE("notice_type", "通知类型"),
    NOTICE_LEVEL("notice_level", "通知级别"),
    
    // 商机管理相关字典
    OPPORTUNITY_TYPE("opportunity_type", "商机类型"),
    OPPORTUNITY_SOURCE("opportunity_source", "商机来源"),
    LOST_REASON("lost_reason", "失败原因"),
    FOLLOW_TYPE("follow_type", "跟进方式"),
    FOLLOW_RESULT("follow_result", "跟进结果");

    private final String value;

    private final String label;

    DictCodeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
