package com.hntsz.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.entity.Role;
import com.hntsz.boot.system.model.vo.RolePageVO;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.system.model.form.RoleForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.UUID;

/**
 * 角色对象转换器
 *
 * <AUTHOR>
 * @since 2022/5/29
 */
@Mapper(componentModel = "spring")
public interface RoleConverter {

    Page<RolePageVO> toPageVo(Page<Role> page);

    @Mappings({
            @Mapping(target = "value", source = "id"),
            @Mapping(target = "label", source = "name"),
            @Mapping(target = "children", ignore = true),
            @Mapping(target = "tag", ignore = true)
    })
    Option<UUID> toOption(Role role);

    List<Option<UUID>> toOptions(List<Role> roles);

    @Mappings({
            @Mapping(target = "createBy", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateBy", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "isDeleted", ignore = true)
    })
    Role toEntity(RoleForm roleForm);

    RoleForm toForm(Role entity);

    // 时间类型转换方法
    default LocalDateTime map(Instant instant) {
        return instant == null ? null : LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    default Instant map(LocalDateTime localDateTime) {
        return localDateTime == null ? null : localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }
}