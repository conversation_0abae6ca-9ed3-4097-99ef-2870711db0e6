package com.hntsz.boot.system.enums;

import lombok.Getter;

@Getter
public enum UserStatusEnum {

    Active("Active", "正常"),
    Inactive("Inactive", "禁用");

    private final String code;
    private final String label;

    UserStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }


    /**
     * 根据 key 获取对应的枚举值
     */
    public static UserStatusEnum fromKey(String code) {
        for (UserStatusEnum status : UserStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据 label 获取对应的枚举值
     */
    public static UserStatusEnum fromLabel(String label) {
        for (UserStatusEnum status : UserStatusEnum.values()) {
            if (status.label.equals(label)) {
                return status;
            }
        }
        return null;
    }

}
