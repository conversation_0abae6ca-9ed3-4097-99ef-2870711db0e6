package com.hntsz.boot.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.bo.UserBO;
import com.hntsz.boot.system.model.entity.User;
import com.hntsz.boot.system.model.query.UserPageQuery;
import com.hntsz.boot.system.model.form.UserForm;
import com.hntsz.boot.common.annotation.DataPermission;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.model.dto.UserExportDTO;
import com.hntsz.boot.system.model.vo.UserStatisticsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

/**
 * 用户持久层接口
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 获取用户分页列表
     *
     * @param page        分页参数
     * @param queryParams 查询参数
     * @return 用户分页列表
     */
    default Page<User> getUserPage(Page<User> page, UserPageQuery queryParams) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(User::getCreatedAt);
        if (ObjectUtil.isNotEmpty(queryParams.getKeywords())) {
            queryWrapper.like(User::getUniqueUserKey, queryParams.getKeywords())
                    .or()
                    .like(User::getDisplayName, queryParams.getKeywords());
        }
        return selectPage(page, queryWrapper);
    };

    /**
     * 获取用户表单详情
     *
     * @param userId 用户ID
     * @return 用户表单详情
     */
    UserForm getUserFormData(UUID userId);

    /**
     * 根据用户唯一标识获取认证信息
     *
     * @param uniqueUserKey 用户唯一标识
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByUniqueUserKey(String uniqueUserKey);

    /**
     * 根据微信openid获取用户认证信息
     *
     * @param openid 微信openid
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByOpenId(String openid);

    /**
     * 根据手机号获取用户认证信息
     *
     * @param mobile 手机号
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByMobile(String mobile);

    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     * @return 导出用户列表
     */
    List<UserExportDTO> listExportUsers(UserPageQuery queryParams);

    /**
     * 更新用户登录信息（登录次数和最后登录时间）
     *
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updateLoginInfo(String userId);

    /**
     * 获取用户统计信息
     *
     * @return 用户统计信息
     */
    UserStatisticsVO getStatisticsInfo();
}
