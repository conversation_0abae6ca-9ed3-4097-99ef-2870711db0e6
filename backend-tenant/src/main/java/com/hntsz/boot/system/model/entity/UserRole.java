package com.hntsz.boot.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * 用户和角色关联表
 *
 * <AUTHOR>
 * @since 2022/12/17
 */
@TableName("\"UserRoles\"")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRole {

    /**
     * 用户角色关联ID
     */
    @TableId(value = "\"Id\"", type = IdType.ASSIGN_UUID)
    private UUID id;

    /**
     * 用户ID
     */
    @TableField("\"UserId\"")
    private UUID userId;

    /**
     * 角色ID
     */
    @TableField("\"RoleId\"")
    private UUID roleId;
}