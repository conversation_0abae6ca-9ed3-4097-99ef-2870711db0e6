package com.hntsz.boot.system.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 租户信息DTO
 */
@Schema(description = "租户信息DTO")
@Data
public class TenantInfoDTO {

    @Schema(description = "租户ID")
    private UUID tenantId;

    @Schema(description = "租户标识（只读）")
    private String key;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "租户描述")
    private String description;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private OffsetDateTime createdAt;

    @Schema(description = "是否禁用")
    private Boolean disabled;

    @Schema(description = "LOGO URL")
    private String logoUrl;
}