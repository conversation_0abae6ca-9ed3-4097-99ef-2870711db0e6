# 认证授权流程改造指南

## 📋 概述

本文档详细说明了如何改造当前系统的认证授权流程，包括需要修改的文件、步骤说明以及最佳实践。

## 🏗️ 当前架构概览

### 核心组件
- **Spring Security**: 安全框架
- **JWT Token**: 无状态认证
- **Redis**: Token黑名单和缓存
- **MyBatis**: 数据访问层
- **多租户支持**: 基于TenantId的数据隔离

### 认证流程
1. 用户提交登录凭据
2. AuthenticationManager验证凭据
3. 生成JWT Token
4. 后续请求携带Token进行认证

## 🔧 改造步骤

### 第一步：分析现有认证流程

#### 1.1 核心文件清单
```
认证相关文件：
├── SecurityConfig.java                    # Spring Security配置
├── AuthController.java                    # 认证接口
├── AuthService.java                       # 认证服务接口
├── AuthServiceImpl.java                   # 认证服务实现
├── JwtTokenManager.java                   # JWT Token管理
├── SecurityUtils.java                     # 安全工具类
├── SysUserDetails.java                    # 用户详情对象
├── UserAuthCredentials.java               # 用户认证凭据
├── SysUserDetailsService.java             # 用户详情服务
└── TokenAuthenticationFilter.java         # Token认证过滤器

认证提供者：
├── WechatAuthenticationProvider.java      # 微信认证
├── WechatAuthenticationToken.java         # 微信认证Token
├── SmsAuthenticationProvider.java         # 短信认证
└── SmsAuthenticationToken.java            # 短信认证Token

数据访问层：
├── UserMapper.java                        # 用户数据访问接口
├── UserMapper.xml                         # 用户SQL映射
└── UserServiceImpl.java                   # 用户服务实现
```

### 第二步：确定改造目标

#### 2.1 常见改造场景
- [ ] 添加新的认证方式（如OAuth2、LDAP等）
- [ ] 修改Token生成策略
- [ ] 增强权限控制粒度
- [ ] 集成第三方认证服务
- [ ] 实现单点登录（SSO）
- [ ] 添加多因素认证（MFA）
- [ ] 优化会话管理

#### 2.2 改造原则
1. **向后兼容**: 保持现有API不变
2. **渐进式**: 分阶段实施改造
3. **可扩展**: 支持未来功能扩展
4. **安全性**: 不降低系统安全性

### 第三步：具体改造步骤

#### 3.1 添加新认证方式

**步骤1: 创建认证Token**
```java
// 文件: src/main/java/com/hntsz/boot/core/security/extension/oauth2/OAuth2AuthenticationToken.java
public class OAuth2AuthenticationToken extends AbstractAuthenticationToken {
    // 实现OAuth2认证Token
}
```

**步骤2: 创建认证Provider**
```java
// 文件: src/main/java/com/hntsz/boot/core/security/extension/oauth2/OAuth2AuthenticationProvider.java
@Component
public class OAuth2AuthenticationProvider implements AuthenticationProvider {
    // 实现OAuth2认证逻辑
}
```

**步骤3: 更新SecurityConfig**
```java
// 文件: src/main/java/com/hntsz/boot/config/SecurityConfig.java
@Bean
public AuthenticationManager authenticationManager(
    DaoAuthenticationProvider daoAuthenticationProvider,
    OAuth2AuthenticationProvider oauth2AuthenticationProvider) {
    return new ProviderManager(
        daoAuthenticationProvider,
        oauth2AuthenticationProvider
    );
}
```

#### 3.2 修改Token生成策略

**需要修改的文件:**
- `JwtTokenManager.java` - Token生成和解析逻辑
- `JwtClaimConstants.java` - 添加新的Claims常量
- `AuthServiceImpl.java` - 更新Token生成调用

**示例修改:**
```java
// 在JwtTokenManager.java中添加新的Claims
payload.put(JwtClaimConstants.CUSTOM_CLAIM, customValue);
```

#### 3.3 增强权限控制

**步骤1: 扩展用户权限模型**
```java
// 修改文件: SysUserDetails.java
private Set<String> permissions; // 添加细粒度权限
private Map<String, Object> attributes; // 添加扩展属性
```

**步骤2: 更新权限查询**
```sql
-- 修改文件: UserMapper.xml
-- 添加权限查询SQL
SELECT p.permission_code FROM user_permissions p WHERE p.user_id = #{userId}
```

**步骤3: 实现权限注解**
```java
// 创建文件: src/main/java/com/hntsz/boot/core/security/annotation/RequirePermission.java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    String[] value();
}
```

### 第四步：数据库改造

#### 4.1 用户表结构优化
```sql
-- 可能需要的表结构修改
ALTER TABLE "TenantUsers" ADD COLUMN "LastPasswordChange" TIMESTAMP;
ALTER TABLE "TenantUsers" ADD COLUMN "AccountLocked" BOOLEAN DEFAULT FALSE;
ALTER TABLE "TenantUsers" ADD COLUMN "LoginAttempts" INTEGER DEFAULT 0;
```

#### 4.2 权限表设计
```sql
-- 创建权限相关表
CREATE TABLE "Permissions" (
    "Id" UUID PRIMARY KEY,
    "Code" VARCHAR(100) NOT NULL,
    "Name" VARCHAR(200) NOT NULL,
    "Description" TEXT
);

CREATE TABLE "UserPermissions" (
    "UserId" UUID REFERENCES "TenantUsers"("Id"),
    "PermissionId" UUID REFERENCES "Permissions"("Id"),
    PRIMARY KEY ("UserId", "PermissionId")
);
```

### 第五步：配置文件调整

#### 5.1 Security配置
```yaml
# 文件: application-dev.yml
security:
  session:
    type: jwt
    access-token-time-to-live: 7200  # 2小时
    refresh-token-time-to-live: 604800  # 7天
  oauth2:
    enabled: true
    providers:
      google:
        client-id: ${GOOGLE_CLIENT_ID}
        client-secret: ${GOOGLE_CLIENT_SECRET}
```

#### 5.2 多租户配置
```yaml
# 租户相关配置
tenant:
  isolation:
    type: schema  # schema | database | discriminator
  default-tenant: system
```

## 🚀 实施建议

### 阶段一：准备工作（1-2天）
1. 备份现有代码和数据库
2. 创建功能分支
3. 搭建测试环境
4. 编写测试用例

### 阶段二：核心改造（3-5天）
1. 实现新的认证方式
2. 修改Token生成逻辑
3. 更新权限控制机制
4. 调整数据库结构

### 阶段三：集成测试（2-3天）
1. 单元测试
2. 集成测试
3. 性能测试
4. 安全测试

### 阶段四：部署上线（1天）
1. 生产环境部署
2. 数据迁移
3. 监控告警
4. 回滚预案

## ⚠️ 注意事项

### 安全考虑
1. **密码策略**: 实施强密码策略
2. **会话管理**: 合理设置Token过期时间
3. **输入验证**: 严格验证所有输入
4. **日志记录**: 记录所有认证相关操作

### 性能优化
1. **缓存策略**: 合理使用Redis缓存
2. **数据库优化**: 添加必要的索引
3. **连接池**: 优化数据库连接池配置
4. **异步处理**: 对于耗时操作使用异步处理

### 兼容性
1. **API版本**: 保持API向后兼容
2. **数据迁移**: 提供平滑的数据迁移方案
3. **客户端适配**: 确保前端客户端正常工作

## 📚 参考资源

- [Spring Security官方文档](https://spring.io/projects/spring-security)
- [JWT最佳实践](https://tools.ietf.org/html/rfc7519)
- [OAuth2.0规范](https://tools.ietf.org/html/rfc6749)
- [多租户架构设计](https://docs.microsoft.com/en-us/azure/architecture/guide/multitenant/overview)

## 🔍 故障排查

### 常见问题
1. **Token解析失败**: 检查JWT密钥配置
2. **权限验证异常**: 确认权限数据正确性
3. **数据库连接问题**: 检查连接池配置
4. **Redis连接异常**: 验证Redis服务状态

### 调试技巧
1. 启用Spring Security调试日志
2. 使用JWT调试工具验证Token
3. 监控数据库查询性能
4. 检查网络连接状态

## 💡 高级改造场景

### 场景1: 集成OAuth2.0

#### 需要修改的文件
```
新增文件：
├── OAuth2AuthenticationProvider.java      # OAuth2认证提供者
├── OAuth2AuthenticationToken.java         # OAuth2认证Token
├── OAuth2UserService.java                 # OAuth2用户服务
└── OAuth2LoginSuccessHandler.java         # OAuth2登录成功处理器

修改文件：
├── SecurityConfig.java                     # 添加OAuth2配置
├── AuthController.java                     # 添加OAuth2登录接口
└── application.yml                         # 添加OAuth2配置
```

#### 实现示例
```java
// OAuth2AuthenticationProvider.java
@Component
public class OAuth2AuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private OAuth2UserService oauth2UserService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        OAuth2AuthenticationToken token = (OAuth2AuthenticationToken) authentication;

        // 1. 验证OAuth2授权码
        OAuth2User oauth2User = oauth2UserService.loadUser(token.getAuthorizationCode());

        // 2. 查找或创建本地用户
        UserAuthCredentials userCredentials = findOrCreateUser(oauth2User);

        // 3. 创建认证结果
        SysUserDetails userDetails = new SysUserDetails(userCredentials);
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return OAuth2AuthenticationToken.class.isAssignableFrom(authentication);
    }
}
```

### 场景2: 实现多因素认证(MFA)

#### 核心组件
```java
// MfaAuthenticationProvider.java
@Component
public class MfaAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private TotpService totpService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        MfaAuthenticationToken mfaToken = (MfaAuthenticationToken) authentication;

        // 1. 验证第一因素（用户名密码）
        Authentication firstFactor = authenticateFirstFactor(mfaToken);

        // 2. 验证第二因素（TOTP码）
        boolean totpValid = totpService.validateTotp(
            mfaToken.getUserId(),
            mfaToken.getTotpCode()
        );

        if (!totpValid) {
            throw new BadCredentialsException("Invalid TOTP code");
        }

        return firstFactor;
    }
}
```

### 场景3: 实现基于角色的访问控制(RBAC)

#### 权限模型设计
```sql
-- 角色表
CREATE TABLE "Roles" (
    "Id" UUID PRIMARY KEY,
    "Code" VARCHAR(50) NOT NULL,
    "Name" VARCHAR(100) NOT NULL,
    "TenantId" UUID NOT NULL
);

-- 权限表
CREATE TABLE "Permissions" (
    "Id" UUID PRIMARY KEY,
    "Code" VARCHAR(100) NOT NULL,
    "Name" VARCHAR(200) NOT NULL,
    "Resource" VARCHAR(100),
    "Action" VARCHAR(50)
);

-- 角色权限关联表
CREATE TABLE "RolePermissions" (
    "RoleId" UUID REFERENCES "Roles"("Id"),
    "PermissionId" UUID REFERENCES "Permissions"("Id"),
    PRIMARY KEY ("RoleId", "PermissionId")
);

-- 用户角色关联表
CREATE TABLE "UserRoles" (
    "UserId" UUID REFERENCES "TenantUsers"("Id"),
    "RoleId" UUID REFERENCES "Roles"("Id"),
    PRIMARY KEY ("UserId", "RoleId")
);
```

#### 权限检查实现
```java
// PermissionEvaluator.java
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Autowired
    private PermissionService permissionService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        SysUserDetails userDetails = (SysUserDetails) authentication.getPrincipal();
        String permissionCode = permission.toString();

        return permissionService.hasPermission(userDetails.getUserId(), permissionCode);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        // 实现基于资源ID的权限检查
        return hasPermission(authentication, null, permission);
    }
}
```

### 场景4: 实现单点登录(SSO)

#### SAML2.0集成
```java
// Saml2AuthenticationProvider.java
@Component
public class Saml2AuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private Saml2Service saml2Service;

    @Override
    public Authentication authenticate(Authentication authentication) {
        Saml2AuthenticationToken samlToken = (Saml2AuthenticationToken) authentication;

        // 1. 验证SAML响应
        Saml2Response samlResponse = saml2Service.validateResponse(samlToken.getSamlResponse());

        // 2. 提取用户信息
        String username = samlResponse.getNameId();
        Map<String, String> attributes = samlResponse.getAttributes();

        // 3. 查找或创建本地用户
        UserAuthCredentials userCredentials = findOrCreateUserFromSaml(username, attributes);

        // 4. 创建认证结果
        SysUserDetails userDetails = new SysUserDetails(userCredentials);
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }
}
```

## 🔧 工具和脚本

### 数据迁移脚本
```sql
-- 迁移现有用户数据到新权限模型
-- migration_v1_to_v2.sql

-- 1. 创建默认角色
INSERT INTO "Roles" ("Id", "Code", "Name", "TenantId")
SELECT gen_random_uuid(), 'ADMIN', '管理员', "TenantId"
FROM "TenantUsers"
WHERE "RoleKey" = 'admin'
GROUP BY "TenantId";

-- 2. 迁移用户角色关系
INSERT INTO "UserRoles" ("UserId", "RoleId")
SELECT u."Id", r."Id"
FROM "TenantUsers" u
JOIN "Roles" r ON r."TenantId" = u."TenantId" AND r."Code" = UPPER(u."RoleKey");

-- 3. 清理旧字段（可选）
-- ALTER TABLE "TenantUsers" DROP COLUMN "RoleKey";
```

### 测试脚本
```java
// AuthenticationIntegrationTest.java
@SpringBootTest
@AutoConfigureTestDatabase
class AuthenticationIntegrationTest {

    @Autowired
    private AuthService authService;

    @Test
    void testUsernamePasswordLogin() {
        // 测试用户名密码登录
        AuthenticationToken token = authService.login("testuser", "password");
        assertThat(token).isNotNull();
        assertThat(token.getAccessToken()).isNotBlank();
    }

    @Test
    void testOAuth2Login() {
        // 测试OAuth2登录
        AuthenticationToken token = authService.loginByOAuth2("google", "auth_code");
        assertThat(token).isNotNull();
    }

    @Test
    void testMfaLogin() {
        // 测试多因素认证
        AuthenticationToken token = authService.loginWithMfa("testuser", "password", "123456");
        assertThat(token).isNotNull();
    }
}
```

### 性能监控
```java
// AuthenticationMetrics.java
@Component
public class AuthenticationMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter loginAttempts;
    private final Counter loginSuccesses;
    private final Counter loginFailures;

    public AuthenticationMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.loginAttempts = Counter.builder("auth.login.attempts").register(meterRegistry);
        this.loginSuccesses = Counter.builder("auth.login.successes").register(meterRegistry);
        this.loginFailures = Counter.builder("auth.login.failures").register(meterRegistry);
    }

    public void recordLoginAttempt() {
        loginAttempts.increment();
    }

    public void recordLoginSuccess() {
        loginSuccesses.increment();
    }

    public void recordLoginFailure() {
        loginFailures.increment();
    }
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-08-21
**维护者**: 开发团队
