# 认证方式扩展示例

## 📝 示例：添加邮箱验证码认证

本示例演示如何在现有系统中添加邮箱验证码认证功能。

### 第一步：创建认证Token

```java
// 文件：src/main/java/com/hntsz/boot/core/security/extension/email/EmailAuthenticationToken.java
package com.hntsz.boot.core.security.extension.email;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * 邮箱验证码认证令牌
 */
public class EmailAuthenticationToken extends AbstractAuthenticationToken {

    private final Object principal;  // 邮箱地址
    private Object credentials;      // 验证码

    /**
     * 创建未认证的邮箱认证令牌
     */
    public EmailAuthenticationToken(String email, String verificationCode) {
        super(null);
        this.principal = email;
        this.credentials = verificationCode;
        setAuthenticated(false);
    }

    /**
     * 创建已认证的邮箱认证令牌
     */
    public EmailAuthenticationToken(Object principal, Object credentials,
                                   Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.credentials = credentials;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return this.credentials;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
        this.credentials = null;
    }
}
```

### 第二步：创建认证Provider

```java
// 文件：src/main/java/com/hntsz/boot/core/security/extension/email/EmailAuthenticationProvider.java
package com.hntsz.boot.core.security.extension.email;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hntsz.boot.common.constant.RedisConstants;
import com.hntsz.boot.core.security.exception.CaptchaValidationException;
import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

/**
 * 邮箱验证码认证提供者
 */
@Slf4j
@Component
public class EmailAuthenticationProvider implements AuthenticationProvider {

    private final UserService userService;
    private final RedisTemplate<String, Object> redisTemplate;

    public EmailAuthenticationProvider(UserService userService, RedisTemplate<String, Object> redisTemplate) {
        this.userService = userService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String email = (String) authentication.getPrincipal();
        String inputVerifyCode = (String) authentication.getCredentials();

        // 1. 验证邮箱验证码
        String cacheKey = StrUtil.format(RedisConstants.Captcha.EMAIL_LOGIN_CODE, email);
        String cachedVerifyCode = (String) redisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isBlank(cachedVerifyCode)) {
            throw new CaptchaValidationException("验证码已过期，请重新获取");
        }

        if (!ObjectUtil.equal(inputVerifyCode, cachedVerifyCode)) {
            throw new CaptchaValidationException("验证码错误");
        }

        // 2. 根据邮箱获取用户信息
        UserAuthCredentials userAuthCredentials = userService.getAuthCredentialsByEmail(email);
        if (userAuthCredentials == null) {
            throw new UsernameNotFoundException("用户不存在");
        }

        // 3. 检查用户状态
        if (!"Active".equals(userAuthCredentials.getStatus())) {
            throw new DisabledException("用户已被禁用，请联系管理员");
        }

        // 4. 验证成功后删除验证码
        redisTemplate.delete(cacheKey);

        // 5. 创建已认证的邮箱认证令牌
        SysUserDetails userDetails = new SysUserDetails(userAuthCredentials);
        EmailAuthenticationToken authenticationToken = new EmailAuthenticationToken(
                userDetails, null, userDetails.getAuthorities());
        authenticationToken.setDetails(authentication.getDetails());

        return authenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return EmailAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
```

### 第三步：扩展UserService

```java
// 在UserService.java中添加方法
/**
 * 根据邮箱获取用户认证信息
 *
 * @param email 邮箱地址
 * @return {@link UserAuthCredentials}
 */
UserAuthCredentials getAuthCredentialsByEmail(String email);
```

```java
// 在UserServiceImpl.java中实现方法
@Override
public UserAuthCredentials getAuthCredentialsByEmail(String email) {
    return this.baseMapper.getAuthCredentialsByEmail(email);
}
```

```java
// 在UserMapper.java中添加方法
/**
 * 根据邮箱获取用户认证信息
 *
 * @param email 邮箱地址
 * @return 认证信息
 */
UserAuthCredentials getAuthCredentialsByEmail(String email);
```

```xml
<!-- 在UserMapper.xml中添加SQL查询 -->
<select id="getAuthCredentialsByEmail" resultType="com.hntsz.boot.core.security.model.UserAuthCredentials">
    SELECT
        "Id" as userId,
        "UniqueUserKey" as uniqueUserKey,
        "DisplayName" as displayName,
        "PasswordHash" as password,
        "Status" as status,
        "RoleKey" as roleKey,
        "TenantId" as tenantId
    FROM
        "TenantUsers"
    WHERE
        "Email" = #{email} AND "IsArchived" = false
</select>
```

### 第四步：更新SecurityConfig

```java
// 在SecurityConfig.java中添加Bean
/**
 * 邮箱验证码认证 Provider
 */
@Bean
public EmailAuthenticationProvider emailAuthenticationProvider() {
    return new EmailAuthenticationProvider(userService, redisTemplate);
}

/**
 * 认证管理器 - 添加邮箱认证支持
 */
@Bean
public AuthenticationManager authenticationManager(
        DaoAuthenticationProvider daoAuthenticationProvider,
        WechatAuthenticationProvider weChatAuthenticationProvider,
        SmsAuthenticationProvider smsAuthenticationProvider,
        EmailAuthenticationProvider emailAuthenticationProvider) {
    return new ProviderManager(
            daoAuthenticationProvider,
            weChatAuthenticationProvider,
            smsAuthenticationProvider,
            emailAuthenticationProvider);
}
```

### 第五步：添加Redis常量

```java
// 在RedisConstants.java的Captcha类中添加
/**
 * 邮箱登录验证码 KEY
 */
String EMAIL_LOGIN_CODE = "captcha:email:login:{}";
```

### 第六步：扩展AuthService

```java
// 在AuthService.java中添加方法
/**
 * 发送邮箱登录验证码
 *
 * @param email 邮箱地址
 */
void sendEmailLoginCode(String email);

/**
 * 邮箱验证码登录
 *
 * @param email 邮箱地址
 * @param code  验证码
 * @return 登录结果
 */
AuthenticationToken loginByEmail(String email, String code);
```

```java
// 在AuthServiceImpl.java中实现方法
@Override
public void sendEmailLoginCode(String email) {
    // 生成6位数字验证码
    String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    
    // 发送邮件验证码（这里需要集成邮件服务）
    // emailService.sendVerificationCode(email, code);
    
    // 缓存验证码至Redis，有效期5分钟
    redisTemplate.opsForValue().set(
        StrUtil.format(RedisConstants.Captcha.EMAIL_LOGIN_CODE, email), 
        code, 
        5, 
        TimeUnit.MINUTES
    );
    
    log.info("邮箱验证码已发送到: {}, 验证码: {}", email, code);
}

@Override
public AuthenticationToken loginByEmail(String email, String code) {
    // 1. 创建邮箱验证码认证令牌（未认证）
    EmailAuthenticationToken emailAuthenticationToken = new EmailAuthenticationToken(email, code);

    // 2. 执行认证（认证中）
    Authentication authentication = authenticationManager.authenticate(emailAuthenticationToken);

    // 3. 认证成功后生成 JWT 令牌，并存入 Security 上下文
    AuthenticationToken authenticationToken = tokenManager.generateToken(authentication);
    SecurityContextHolder.getContext().setAuthentication(authentication);

    // 4. 更新用户登录信息
    Optional<SysUserDetails> user = SecurityUtils.getUser();
    if (user.isPresent()) {
        UUID userId = user.get().getUserId();
        userService.updateLoginInfo(userId);
    }

    return authenticationToken;
}
```

### 第七步：添加REST接口

```java
// 在AuthController.java中添加接口
@Operation(summary = "发送邮箱登录验证码")
@PostMapping("/login/email/code")
public Result<Void> sendEmailLoginCode(
        @Parameter(description = "邮箱地址", example = "<EMAIL>") 
        @RequestParam String email) {
    authService.sendEmailLoginCode(email);
    return Result.success();
}

@Operation(summary = "邮箱验证码登录")
@PostMapping("/login/email")
@Log(value = "邮箱验证码登录", module = LogModuleEnum.LOGIN)
public Result<AuthenticationToken> loginByEmail(
        @Parameter(description = "邮箱地址", example = "<EMAIL>") 
        @RequestParam String email,
        @Parameter(description = "验证码", example = "123456") 
        @RequestParam String code) {
    AuthenticationToken loginResult = authService.loginByEmail(email, code);
    return Result.success(loginResult);
}
```

### 第八步：编写测试

```java
// 文件：src/test/java/com/hntsz/boot/auth/EmailAuthenticationTest.java
@SpringBootTest
@AutoConfigureTestDatabase
class EmailAuthenticationTest {

    @Autowired
    private AuthService authService;

    @Test
    void testSendEmailCode() {
        // 测试发送邮箱验证码
        assertDoesNotThrow(() -> {
            authService.sendEmailLoginCode("<EMAIL>");
        });
    }

    @Test
    void testEmailLogin() {
        // 先发送验证码
        authService.sendEmailLoginCode("<EMAIL>");
        
        // 使用固定验证码测试登录（实际环境中需要从Redis获取）
        AuthenticationToken token = authService.loginByEmail("<EMAIL>", "123456");
        
        assertThat(token).isNotNull();
        assertThat(token.getAccessToken()).isNotBlank();
    }
}
```

## 🎯 总结

通过以上8个步骤，我们成功添加了邮箱验证码认证功能：

1. ✅ 创建了EmailAuthenticationToken
2. ✅ 实现了EmailAuthenticationProvider
3. ✅ 扩展了UserService和数据访问层
4. ✅ 更新了SecurityConfig配置
5. ✅ 添加了Redis缓存支持
6. ✅ 扩展了AuthService业务逻辑
7. ✅ 提供了REST API接口
8. ✅ 编写了单元测试

这个示例展示了如何在不破坏现有功能的前提下，优雅地扩展认证方式。
