# 认证授权改造快速参考

## 🎯 核心文件清单

### 认证核心文件
| 文件路径 | 作用 | 改造频率 |
|---------|------|---------|
| `SecurityConfig.java` | Spring Security配置 | ⭐⭐⭐ |
| `AuthServiceImpl.java` | 认证服务实现 | ⭐⭐⭐ |
| `JwtTokenManager.java` | JWT Token管理 | ⭐⭐ |
| `SysUserDetailsService.java` | 用户详情服务 | ⭐⭐ |
| `UserMapper.xml` | 用户数据查询 | ⭐⭐ |

### 认证提供者文件
| 文件路径 | 作用 | 状态 |
|---------|------|------|
| `WechatAuthenticationProvider.java` | 微信认证 | 已实现 |
| `SmsAuthenticationProvider.java` | 短信认证 | 已实现 |
| `OAuth2AuthenticationProvider.java` | OAuth2认证 | 待实现 |

## 🔧 常见改造场景

### 1. 添加新认证方式

**步骤：**
1. 创建 `XxxAuthenticationToken.java`
2. 创建 `XxxAuthenticationProvider.java`
3. 在 `SecurityConfig.java` 中注册Provider
4. 在 `AuthService.java` 中添加接口方法
5. 在 `AuthController.java` 中添加REST接口

**模板代码：**
```java
// 1. 认证Token
public class CustomAuthenticationToken extends AbstractAuthenticationToken {
    private final Object principal;
    private Object credentials;
    
    public CustomAuthenticationToken(Object principal, Object credentials) {
        super(null);
        this.principal = principal;
        this.credentials = credentials;
        setAuthenticated(false);
    }
}

// 2. 认证Provider
@Component
public class CustomAuthenticationProvider implements AuthenticationProvider {
    @Override
    public Authentication authenticate(Authentication authentication) {
        // 实现认证逻辑
        return new UsernamePasswordAuthenticationToken(userDetails, null, authorities);
    }
    
    @Override
    public boolean supports(Class<?> authentication) {
        return CustomAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
```

### 2. 修改Token生成策略

**需要修改的文件：**
- `JwtTokenManager.java` - 核心逻辑
- `JwtClaimConstants.java` - 添加新Claims
- `SecurityUtils.java` - 更新工具方法

**关键代码位置：**
```java
// JwtTokenManager.java - generateToken方法
Map<String, Object> payload = new HashMap<>();
payload.put(JwtClaimConstants.USER_ID, userDetails.getUserId().toString());
payload.put(JwtClaimConstants.TENANT_ID, userDetails.getTenantId().toString());
// 添加新的Claims
payload.put("customClaim", customValue);
```

### 3. 增强权限控制

**数据库表设计：**
```sql
-- 权限表
CREATE TABLE "Permissions" (
    "Id" UUID PRIMARY KEY,
    "Code" VARCHAR(100) NOT NULL,
    "Name" VARCHAR(200) NOT NULL
);

-- 用户权限关联表
CREATE TABLE "UserPermissions" (
    "UserId" UUID,
    "PermissionId" UUID,
    PRIMARY KEY ("UserId", "PermissionId")
);
```

**代码修改：**
```java
// SysUserDetails.java - 添加权限字段
private Set<String> permissions;

// UserMapper.xml - 添加权限查询
<select id="getUserPermissions" resultType="String">
    SELECT p."Code" FROM "Permissions" p
    JOIN "UserPermissions" up ON p."Id" = up."PermissionId"
    WHERE up."UserId" = #{userId}
</select>
```

## 📋 改造检查清单

### 准备阶段
- [ ] 备份现有代码和数据库
- [ ] 创建功能分支
- [ ] 准备测试环境
- [ ] 编写测试用例

### 开发阶段
- [ ] 实现新的认证Provider
- [ ] 更新SecurityConfig配置
- [ ] 修改Token生成逻辑
- [ ] 更新数据库查询
- [ ] 添加新的REST接口

### 测试阶段
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 安全测试通过
- [ ] 性能测试通过

### 部署阶段
- [ ] 数据库迁移脚本
- [ ] 配置文件更新
- [ ] 监控告警配置
- [ ] 回滚方案准备

## ⚡ 快速命令

### 创建新认证方式
```bash
# 1. 创建文件结构
mkdir -p src/main/java/com/hntsz/boot/core/security/extension/custom
touch src/main/java/com/hntsz/boot/core/security/extension/custom/CustomAuthenticationToken.java
touch src/main/java/com/hntsz/boot/core/security/extension/custom/CustomAuthenticationProvider.java

# 2. 运行测试
mvn test -Dtest=AuthenticationTest

# 3. 编译检查
mvn compile
```

### 数据库操作
```sql
-- 查看当前用户权限
SELECT u."UniqueUserKey", u."RoleKey", u."TenantId" 
FROM "TenantUsers" u 
WHERE u."Status" = 'Active';

-- 添加新权限
INSERT INTO "Permissions" ("Id", "Code", "Name") 
VALUES (gen_random_uuid(), 'USER_MANAGE', '用户管理');
```

## 🚨 常见问题

### 编译错误
**问题：** `Cannot resolve symbol 'CustomAuthenticationProvider'`
**解决：** 检查包导入和@Component注解

### 认证失败
**问题：** 新认证方式不生效
**解决：** 确认Provider已在SecurityConfig中注册

### Token解析错误
**问题：** JWT Token包含新Claims后解析失败
**解决：** 同步更新parseToken方法

### 权限检查异常
**问题：** 权限注解不生效
**解决：** 确认@EnableGlobalMethodSecurity已启用

## 📞 技术支持

- **文档位置：** `/docs/认证授权流程改造指南.md`
- **示例代码：** `/src/test/java/com/hntsz/boot/auth/`
- **配置文件：** `/src/main/resources/application-*.yml`

---
**版本：** v1.0 | **更新：** 2025-08-21
