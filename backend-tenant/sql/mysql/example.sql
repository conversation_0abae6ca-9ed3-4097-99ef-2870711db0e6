DROP TABLE IF EXISTS `example`;
CREATE TABLE `example` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `age` int(11) NOT NULL,
    `sort_order` int(11) NOT NULL DEFAULT '0',
    `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶（0: 否, 1: 是）',
    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '示例表';
    
INSERT INTO `example` (`name`, `age`, `sort_order`, `is_top`, `create_by`, `create_time`) VALUES
('张三', 18, 0, 0, 1, '2021-01-01 00:00:00'),
('李四', 20, 1, 1, 1, '2021-01-01 00:00:00'),
('王五', 22, 2, 0, 1, '2021-01-01 00:00:00'),
('赵六', 24, 3, 1, 1, '2021-01-01 00:00:00');


