-- 1. 性别
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Gender" SMALLINT;

COMMENT ON COLUMN "public"."TenantUsers"."Gender" IS '用户性别：0-女，1-男，2-其他';

-- 2. 状态：默认值为 'Active'
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Status" TEXT COLLATE "pg_catalog"."default" DEFAULT 'Active' NOT NULL;

COMMENT ON COLUMN "public"."TenantUsers"."Status" IS '用户状态，默认Active，可为Inactive等';

-- 3. 邮箱地址
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Email" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."Email" IS '用户邮箱地址';

-- 4. 联系电话
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "PhoneNumber" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."PhoneNumber" IS '用户联系电话';

-- 5. 添加“职位”字段
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Position" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."Position" IS '用户在组织中的职位，如：高级工程师、项目经理等';

-- 6. 所属部门
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Department" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."Department" IS '用户所属部门';

-- 7. 描述信息
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "Description" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."Description" IS '用户描述或备注信息';

-- 8. 用户头像 URL
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "AvatarUrl" TEXT COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."TenantUsers"."AvatarUrl" IS '用户头像的URL地址';

-- 9. 登录次数：默认值为 0
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "LoginCount" INTEGER DEFAULT 0 NOT NULL;

COMMENT ON COLUMN "public"."TenantUsers"."LoginCount" IS '用户累计登录次数';

-- 10. 更新时间：字段名为 "UpdateAt"
ALTER TABLE "public"."TenantUsers"
ADD COLUMN "UpdatedAt" TIMESTAMPTZ(6) DEFAULT NOW();

COMMENT ON COLUMN "public"."TenantUsers"."UpdateAt" IS '用户信息最后更新时间';

-- 新增用户
INSERT INTO "public"."TenantUsers" ("Id", "UniqueUserKey", "PasswordHash", "DisplayName", "RoleKey", "LastLoginAt", "CreatedAt", "TenantId", "Gender", "Status", "Email", "PhoneNumber", "Position", "Department", "Description", "AvatarUrl", "LoginCount", "UpdatedAt") VALUES ('0198c5ae-03e7-7586-b44a-b5e9b75a2fca', 'admin', '$2a$10$46JZxS83OwsmuCpJJn6eseA30LY4SpzOUoFNUhBcyKDYju7aa9mma', 'admin', 'ADMIN', '2025-08-21 10:21:06.821306+08', '2025-08-20 12:12:52.585104+08', '019887c5-cf14-7d67-9f02-4b0dd30c76fa', 0, 'Active', NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-08-20 12:12:52.585104+08');
