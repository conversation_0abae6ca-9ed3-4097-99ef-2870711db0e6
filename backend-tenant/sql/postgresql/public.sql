/*
 Navicat Premium Dump SQL

 Source Server         : WSL_postgres
 Source Server Type    : PostgreSQL
 Source Server Version : 170006 (170006)
 Source Host           : **************:5432
 Source Catalog        : aihub
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170006 (170006)
 File Encoding         : 65001

 Date: 23/09/2025 09:54:55
*/


-- ----------------------------
-- Table structure for AgentOptions
-- ----------------------------
DROP TABLE IF EXISTS "public"."AgentOptions";
CREATE TABLE "public"."AgentOptions" (
  "Id" uuid NOT NULL,
  "ParentId" uuid,
  "Name" text COLLATE "pg_catalog"."default" NOT NULL,
  "Description" text COLLATE "pg_catalog"."default" NOT NULL,
  "Key" text COLLATE "pg_catalog"."default" NOT NULL,
  "Extra" text COLLATE "pg_catalog"."default",
  "CreatedAt" timestamptz(6) NOT NULL,
  "UpdatedAt" timestamptz(6) NOT NULL,
  "IsArchived" bool NOT NULL
)
;
COMMENT ON COLUMN "public"."AgentOptions"."Id" IS '智能体ID';
COMMENT ON COLUMN "public"."AgentOptions"."ParentId" IS '父级智能体ID，用于标识该智能体属于哪个智能体';
COMMENT ON COLUMN "public"."AgentOptions"."Name" IS '智能体名称';
COMMENT ON COLUMN "public"."AgentOptions"."Description" IS '智能体描述';
COMMENT ON COLUMN "public"."AgentOptions"."Key" IS '智能体Key';
COMMENT ON COLUMN "public"."AgentOptions"."Extra" IS '智能体额外信息';
COMMENT ON COLUMN "public"."AgentOptions"."CreatedAt" IS '智能体创建时间，记录智能体注册到系统的时间';
COMMENT ON COLUMN "public"."AgentOptions"."UpdatedAt" IS '智能体更新时间，记录智能体更新到系统的时间';
COMMENT ON COLUMN "public"."AgentOptions"."IsArchived" IS '智能体是否归档，记录智能体是否被归档';

-- ----------------------------
-- Table structure for Applications
-- ----------------------------
DROP TABLE IF EXISTS "public"."Applications";
CREATE TABLE "public"."Applications" (
  "Id" uuid NOT NULL,
  "Key" text COLLATE "pg_catalog"."default" NOT NULL,
  "DisplayName" text COLLATE "pg_catalog"."default",
  "Description" text COLLATE "pg_catalog"."default",
  "Icon" text COLLATE "pg_catalog"."default",
  "SignOutUrl" text COLLATE "pg_catalog"."default",
  "Theme" text COLLATE "pg_catalog"."default",
  "AiAvatar" text COLLATE "pg_catalog"."default",
  "UserAvatar" text COLLATE "pg_catalog"."default",
  "RecommendedStartPrompts" text[] COLLATE "pg_catalog"."default" NOT NULL,
  "AgentOptionId" uuid,
  "TenantId" uuid NOT NULL,
  "CreatedAt" timestamptz(6) NOT NULL
)
;
COMMENT ON COLUMN "public"."Applications"."Id" IS '应用程序的唯一标识符';
COMMENT ON COLUMN "public"."Applications"."Key" IS '应用程序标识符，用于标识不同的业务应用';
COMMENT ON COLUMN "public"."Applications"."DisplayName" IS '应用程序显示名称';
COMMENT ON COLUMN "public"."Applications"."Description" IS '应用程序描述信息，说明应用的用途和功能';
COMMENT ON COLUMN "public"."Applications"."Icon" IS '应用程序图标';
COMMENT ON COLUMN "public"."Applications"."SignOutUrl" IS '应用程序退出URL';
COMMENT ON COLUMN "public"."Applications"."Theme" IS '应用程序主题';
COMMENT ON COLUMN "public"."Applications"."AiAvatar" IS '应用程序AI头像';
COMMENT ON COLUMN "public"."Applications"."UserAvatar" IS '应用程序用户头像';
COMMENT ON COLUMN "public"."Applications"."RecommendedStartPrompts" IS '推荐开始提示词';
COMMENT ON COLUMN "public"."Applications"."AgentOptionId" IS '关联的AgentInfoId';
COMMENT ON COLUMN "public"."Applications"."TenantId" IS '关联的租户ID，标识该应用程序属于哪个租户';
COMMENT ON COLUMN "public"."Applications"."CreatedAt" IS '应用程序创建时间，记录应用注册到系统的时间';

-- ----------------------------
-- Table structure for ClientIdentities
-- ----------------------------
DROP TABLE IF EXISTS "public"."ClientIdentities";
CREATE TABLE "public"."ClientIdentities" (
  "Id" uuid NOT NULL,
  "UniqueUserKey" varchar(256) COLLATE "pg_catalog"."default" NOT NULL,
  "UserAgent" text COLLATE "pg_catalog"."default",
  "IpAddress" text COLLATE "pg_catalog"."default",
  "Referer" text COLLATE "pg_catalog"."default",
  "TenantId" uuid NOT NULL,
  "CreatedAt" timestamptz(6) NOT NULL
)
;
COMMENT ON COLUMN "public"."ClientIdentities"."Id" IS '客户端身份的唯一标识符';
COMMENT ON COLUMN "public"."ClientIdentities"."UniqueUserKey" IS '客户在租户中的唯一标识符';
COMMENT ON COLUMN "public"."ClientIdentities"."UserAgent" IS '客户端的用户代理字符串，包含浏览器和操作系统信息';
COMMENT ON COLUMN "public"."ClientIdentities"."IpAddress" IS '客户端的IP地址，用于地理位置和安全分析';
COMMENT ON COLUMN "public"."ClientIdentities"."Referer" IS 'HTTP请求的来源页面URL，用于跟踪用户访问来源';
COMMENT ON COLUMN "public"."ClientIdentities"."TenantId" IS '关联的租户ID，标识该客户端属于哪个租户';
COMMENT ON COLUMN "public"."ClientIdentities"."CreatedAt" IS '客户端身份创建时间，用于审计和数据分析';


-- ----------------------------
-- Table structure for Sessions
-- ----------------------------
DROP TABLE IF EXISTS "public"."Sessions";
CREATE TABLE "public"."Sessions" (
  "Id" uuid NOT NULL,
  "Type" text COLLATE "pg_catalog"."default" NOT NULL,
  "AgentOptionId" uuid NOT NULL,
  "ApplicationId" uuid,
  "ClientIdentityId" uuid NOT NULL,
  "ParentMessageId" uuid,
  "CreatedAt" timestamptz(6) NOT NULL,
  "UpdatedAt" timestamptz(6) NOT NULL,
  "ParentSessionId" uuid
)
;
COMMENT ON COLUMN "public"."Sessions"."Id" IS '会话的唯一标识符';
COMMENT ON COLUMN "public"."Sessions"."Type" IS '会话类型';
COMMENT ON COLUMN "public"."Sessions"."AgentOptionId" IS '关联的代理选项ID';
COMMENT ON COLUMN "public"."Sessions"."ApplicationId" IS '关联的应用程序ID';
COMMENT ON COLUMN "public"."Sessions"."ClientIdentityId" IS '关联的客户端身份ID，标识该会话属于哪个客户端';
COMMENT ON COLUMN "public"."Sessions"."ParentMessageId" IS '父消息ID(SubAgent 时，父消息ID 为 Assistant 消息ID)';
COMMENT ON COLUMN "public"."Sessions"."CreatedAt" IS '会话创建时间，标记对话开始的时间点';
COMMENT ON COLUMN "public"."Sessions"."UpdatedAt" IS '会话最后更新时间，记录最后一次消息交互的时间';
COMMENT ON COLUMN "public"."Sessions"."ParentSessionId" IS '父会话ID(SubAgent 时，父会话ID 为 父会话ID)';

-- ----------------------------
-- Table structure for StoredMessages
-- ----------------------------
DROP TABLE IF EXISTS "public"."StoredMessages";
CREATE TABLE "public"."StoredMessages" (
  "Id" uuid NOT NULL,
  "SessionId" uuid NOT NULL,
  "Category" text COLLATE "pg_catalog"."default" NOT NULL,
  "Content" text COLLATE "pg_catalog"."default",
  "Extra" text COLLATE "pg_catalog"."default",
  "Model" varchar(32) COLLATE "pg_catalog"."default",
  "CreatedAt" timestamptz(6) NOT NULL,
  "AgentInfoId" uuid,
  "InputTokenCount" int4 NOT NULL,
  "OutputTokenCount" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."StoredMessages"."Id" IS '消息的唯一标识符';
COMMENT ON COLUMN "public"."StoredMessages"."SessionId" IS '关联的会话ID，标识该消息属于哪个会话（可能是 ChatSession 或 AgentSession)';
COMMENT ON COLUMN "public"."StoredMessages"."Category" IS '消息类别，标识消息的发送者类型（用户、助手、系统等）';
COMMENT ON COLUMN "public"."StoredMessages"."Content" IS '消息的主要内容，存储实际的对话文本';
COMMENT ON COLUMN "public"."StoredMessages"."Extra" IS '额外的消息信息，可用于存储元数据、格式化信息或其他扩展数据';
COMMENT ON COLUMN "public"."StoredMessages"."Model" IS '消息使用的模型';
COMMENT ON COLUMN "public"."StoredMessages"."CreatedAt" IS '消息创建时间，记录消息发送的精确时间点';
COMMENT ON COLUMN "public"."StoredMessages"."InputTokenCount" IS '输入Token数量';
COMMENT ON COLUMN "public"."StoredMessages"."OutputTokenCount" IS '输出Token数量';

-- ----------------------------
-- Table structure for TenantUsers
-- ----------------------------
DROP TABLE IF EXISTS "public"."TenantUsers";
CREATE TABLE "public"."TenantUsers" (
  "Id" uuid NOT NULL,
  "UniqueUserKey" text COLLATE "pg_catalog"."default" NOT NULL,
  "PasswordHash" text COLLATE "pg_catalog"."default" NOT NULL,
  "DisplayName" text COLLATE "pg_catalog"."default",
  "RoleKey" text COLLATE "pg_catalog"."default",
  "LastLoginAt" timestamptz(6),
  "CreatedAt" timestamptz(6) NOT NULL,
  "TenantId" uuid NOT NULL,
  "Gender" int2,
  "Status" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Active'::text,
  "Email" text COLLATE "pg_catalog"."default",
  "PhoneNumber" text COLLATE "pg_catalog"."default",
  "Position" text COLLATE "pg_catalog"."default",
  "Department" text COLLATE "pg_catalog"."default",
  "Description" text COLLATE "pg_catalog"."default",
  "AvatarUrl" text COLLATE "pg_catalog"."default",
  "LoginCount" int4 NOT NULL DEFAULT 0,
  "UpdatedAt" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."TenantUsers"."Id" IS '用户唯一标识符';
COMMENT ON COLUMN "public"."TenantUsers"."UniqueUserKey" IS '用户唯一键（如用户名或唯一标识）';
COMMENT ON COLUMN "public"."TenantUsers"."PasswordHash" IS '用户密码的哈希值';
COMMENT ON COLUMN "public"."TenantUsers"."DisplayName" IS '用户显示名称';
COMMENT ON COLUMN "public"."TenantUsers"."RoleKey" IS '用户角色标识（如管理员、普通用户等）';
COMMENT ON COLUMN "public"."TenantUsers"."LastLoginAt" IS '用户最后一次登录时间';
COMMENT ON COLUMN "public"."TenantUsers"."CreatedAt" IS '用户创建时间';
COMMENT ON COLUMN "public"."TenantUsers"."TenantId" IS '所属租户ID';
COMMENT ON COLUMN "public"."TenantUsers"."Gender" IS '用户性别：0-女，1-男，2-其他';
COMMENT ON COLUMN "public"."TenantUsers"."Status" IS '用户状态，默认Active，可为Inactive等';
COMMENT ON COLUMN "public"."TenantUsers"."Email" IS '用户邮箱地址';
COMMENT ON COLUMN "public"."TenantUsers"."PhoneNumber" IS '用户联系电话';
COMMENT ON COLUMN "public"."TenantUsers"."Position" IS '用户在组织中的职位，如：高级工程师、项目经理等';
COMMENT ON COLUMN "public"."TenantUsers"."Department" IS '用户所属部门';
COMMENT ON COLUMN "public"."TenantUsers"."Description" IS '用户描述或备注信息';
COMMENT ON COLUMN "public"."TenantUsers"."AvatarUrl" IS '用户头像的URL地址';
COMMENT ON COLUMN "public"."TenantUsers"."LoginCount" IS '用户累计登录次数';
COMMENT ON COLUMN "public"."TenantUsers"."UpdatedAt" IS '用户信息最后更新时间';

-- ----------------------------
-- Table structure for Tenants
-- ----------------------------
DROP TABLE IF EXISTS "public"."Tenants";
CREATE TABLE "public"."Tenants" (
  "Id" uuid NOT NULL,
  "Key" text COLLATE "pg_catalog"."default" NOT NULL,
  "Name" text COLLATE "pg_catalog"."default" NOT NULL,
  "Description" text COLLATE "pg_catalog"."default",
  "SecretKey" text COLLATE "pg_catalog"."default",
  "Disabled" bool NOT NULL,
  "CreatedAt" timestamptz(6) NOT NULL
)
;
COMMENT ON COLUMN "public"."Tenants"."Id" IS '租户的唯一标识符';
COMMENT ON COLUMN "public"."Tenants"."Key" IS '租户的唯一标识符';
COMMENT ON COLUMN "public"."Tenants"."Name" IS '租户名称，用于显示和识别不同的组织';
COMMENT ON COLUMN "public"."Tenants"."Description" IS '租户描述信息，提供关于组织的详细说明';
COMMENT ON COLUMN "public"."Tenants"."SecretKey" IS '租户的密钥，用于验证租户的合法性';
COMMENT ON COLUMN "public"."Tenants"."Disabled" IS '租户是否禁用';
COMMENT ON COLUMN "public"."Tenants"."CreatedAt" IS '租户创建时间，记录组织加入系统的时间';

-- ----------------------------
-- Table structure for Tickets
-- ----------------------------
DROP TABLE IF EXISTS "public"."Tickets";
CREATE TABLE "public"."Tickets" (
  "Id" uuid NOT NULL,
  "UserKey" text COLLATE "pg_catalog"."default" NOT NULL,
  "TicketCode" text COLLATE "pg_catalog"."default" NOT NULL,
  "ApplicationId" uuid NOT NULL,
  "IsUsed" bool NOT NULL,
  "TicketType" text COLLATE "pg_catalog"."default" NOT NULL,
  "ExpiredAt" timestamptz(6) NOT NULL,
  "CreatedAt" timestamptz(6) NOT NULL,
  "TenantId" uuid NOT NULL
)
;
COMMENT ON COLUMN "public"."Tickets"."Id" IS '票据ID';
COMMENT ON COLUMN "public"."Tickets"."UserKey" IS '用户键';
COMMENT ON COLUMN "public"."Tickets"."TicketCode" IS '票据码';
COMMENT ON COLUMN "public"."Tickets"."ApplicationId" IS '应用程序ID';
COMMENT ON COLUMN "public"."Tickets"."IsUsed" IS '是否已使用';
COMMENT ON COLUMN "public"."Tickets"."TicketType" IS '票据类型';
COMMENT ON COLUMN "public"."Tickets"."ExpiredAt" IS '过期时间';
COMMENT ON COLUMN "public"."Tickets"."CreatedAt" IS '创建时间';
COMMENT ON COLUMN "public"."Tickets"."TenantId" IS '关联的租户ID，标识该客户端属于哪个租户';


-- ----------------------------
-- Table structure for __EFMigrationsHistory
-- ----------------------------
DROP TABLE IF EXISTS "public"."__EFMigrationsHistory";
CREATE TABLE "public"."__EFMigrationsHistory" (
  "MigrationId" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "ProductVersion" varchar(32) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Indexes structure for table AgentOptions
-- ----------------------------
CREATE INDEX "IX_AgentOptions_ParentId" ON "public"."AgentOptions" USING btree (
  "ParentId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table AgentOptions
-- ----------------------------
ALTER TABLE "public"."AgentOptions" ADD CONSTRAINT "PK_AgentOptions" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table Applications
-- ----------------------------
CREATE INDEX "IX_Applications_AgentOptionId" ON "public"."Applications" USING btree (
  "AgentOptionId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "IX_Applications_TenantId" ON "public"."Applications" USING btree (
  "TenantId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table Applications
-- ----------------------------
ALTER TABLE "public"."Applications" ADD CONSTRAINT "PK_Applications" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table ClientIdentities
-- ----------------------------
CREATE INDEX "IX_ClientIdentities_TenantId" ON "public"."ClientIdentities" USING btree (
  "TenantId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table ClientIdentities
-- ----------------------------
ALTER TABLE "public"."ClientIdentities" ADD CONSTRAINT "PK_ClientIdentities" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table Sessions
-- ----------------------------
CREATE INDEX "IX_Sessions_AgentOptionId" ON "public"."Sessions" USING btree (
  "AgentOptionId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "IX_Sessions_ApplicationId" ON "public"."Sessions" USING btree (
  "ApplicationId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "IX_Sessions_ClientIdentityId" ON "public"."Sessions" USING btree (
  "ClientIdentityId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table Sessions
-- ----------------------------
ALTER TABLE "public"."Sessions" ADD CONSTRAINT "PK_Sessions" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table StoredMessages
-- ----------------------------
CREATE INDEX "IX_StoredMessages_AgentInfoId" ON "public"."StoredMessages" USING btree (
  "AgentInfoId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "IX_StoredMessages_SessionId" ON "public"."StoredMessages" USING btree (
  "SessionId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table StoredMessages
-- ----------------------------
ALTER TABLE "public"."StoredMessages" ADD CONSTRAINT "PK_StoredMessages" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table TenantUsers
-- ----------------------------
CREATE INDEX "IX_TenantUsers_TenantId" ON "public"."TenantUsers" USING btree (
  "TenantId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table TenantUsers
-- ----------------------------
ALTER TABLE "public"."TenantUsers" ADD CONSTRAINT "PK_TenantUsers" PRIMARY KEY ("Id");

-- ----------------------------
-- Primary Key structure for table Tenants
-- ----------------------------
ALTER TABLE "public"."Tenants" ADD CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id");

-- ----------------------------
-- Indexes structure for table Tickets
-- ----------------------------
CREATE INDEX "IX_Tickets_TenantId" ON "public"."Tickets" USING btree (
  "TenantId" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table Tickets
-- ----------------------------
ALTER TABLE "public"."Tickets" ADD CONSTRAINT "PK_Tickets" PRIMARY KEY ("Id");

-- ----------------------------
-- Primary Key structure for table __EFMigrationsHistory
-- ----------------------------
ALTER TABLE "public"."__EFMigrationsHistory" ADD CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId");

-- ----------------------------
-- Foreign Keys structure for table AgentOptions
-- ----------------------------
ALTER TABLE "public"."AgentOptions" ADD CONSTRAINT "FK_AgentOptions_AgentOptions_ParentId" FOREIGN KEY ("ParentId") REFERENCES "public"."AgentOptions" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table Applications
-- ----------------------------
ALTER TABLE "public"."Applications" ADD CONSTRAINT "FK_Applications_AgentOptions_AgentOptionId" FOREIGN KEY ("AgentOptionId") REFERENCES "public"."AgentOptions" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."Applications" ADD CONSTRAINT "FK_Applications_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "public"."Tenants" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table ClientIdentities
-- ----------------------------
ALTER TABLE "public"."ClientIdentities" ADD CONSTRAINT "FK_ClientIdentities_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "public"."Tenants" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table Sessions
-- ----------------------------
ALTER TABLE "public"."Sessions" ADD CONSTRAINT "FK_Sessions_AgentOptions_AgentOptionId" FOREIGN KEY ("AgentOptionId") REFERENCES "public"."AgentOptions" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."Sessions" ADD CONSTRAINT "FK_Sessions_Applications_ApplicationId" FOREIGN KEY ("ApplicationId") REFERENCES "public"."Applications" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."Sessions" ADD CONSTRAINT "FK_Sessions_ClientIdentities_ClientIdentityId" FOREIGN KEY ("ClientIdentityId") REFERENCES "public"."ClientIdentities" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table StoredMessages
-- ----------------------------
ALTER TABLE "public"."StoredMessages" ADD CONSTRAINT "FK_StoredMessages_AgentOptions_AgentInfoId" FOREIGN KEY ("AgentInfoId") REFERENCES "public"."AgentOptions" ("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."StoredMessages" ADD CONSTRAINT "FK_StoredMessages_Sessions_SessionId" FOREIGN KEY ("SessionId") REFERENCES "public"."Sessions" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table TenantUsers
-- ----------------------------
ALTER TABLE "public"."TenantUsers" ADD CONSTRAINT "FK_TenantUsers_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "public"."Tenants" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table Tickets
-- ----------------------------
ALTER TABLE "public"."Tickets" ADD CONSTRAINT "FK_Tickets_Tenants_TenantId" FOREIGN KEY ("TenantId") REFERENCES "public"."Tenants" ("Id") ON DELETE CASCADE ON UPDATE NO ACTION;
