# 创建一个名为 "hntsz-boot" 的桥接网络，在同一个网络中的容器可以通过容器名互相访问
networks:
  hntsz-boot:
    driver: bridge

services:
  mysql:
    image: mysql:8.0.29
    container_name: mysql
    restart: unless-stopped # 重启策略：除非手动停止容器，否则自动重启
    environment:
      - TZ=Asia/Shanghai
      - LANG= en_US.UTF-8
      - MYSQL_ROOT_PASSWORD=123456 #设置 root 用户的密码
    volumes:
      - ./mysql/conf/my.cnf:/etc/my.cnf # 挂载 my.cnf 文件到容器的指定路径
      - ./mysql/data:/var/lib/mysql # 持久化 MySQL 数据
      - ../sql/mysql:/docker-entrypoint-initdb.d # 初始化 SQL 脚本目录
    ports:
      - 3306:3306
    networks:
      - hntsz-boot # 加入 "hntsz-boot" 网络

  redis:
    image: redis:7.2.3
    container_name: redis
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf --requirepass 123456 --appendonly no # 启动 Redis 服务并添加密码为：123456，默认不开启 Redis AOF 方式持久化配置
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./redis/data:/data
      - ./redis/config/redis.conf:/etc/redis/redis.conf
    ports:
      - 6379:6379
    networks:
      - hntsz-boot

  minio:
    image: minio/minio:latest
    container_name: minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    ports:
      - 9000:9000
      - 9001:9001
    environment:
      - TZ=Asia/Shanghai
      - LANG=en_US.UTF-8
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - ./minio/data:/data
      - ./minio/config:/root/.minio
    networks:
      - hntsz-boot

  xxl-job-admin:
    image: xuxueli/xxl-job-admin:2.4.0
    container_name: xxl-job-admin
    restart: unless-stopped
    environment:
      PARAMS: '--spring.datasource.url=*********************************************************************************************************************** --spring.datasource.username=root --spring.datasource.password=123456 --spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver'
    volumes:
      - ./xxljob/logs:/data/applogs
    ports:
      - 8080:8080
    networks:
      - hntsz-boot