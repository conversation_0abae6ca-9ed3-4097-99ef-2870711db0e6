

[mysqld]
# 字符集与排序规则
character-set-server = utf8mb4  # 服务端默认字符集
collation-server = utf8mb4_0900_ai_ci # 服务端默认排序规则

# 网络与路径
datadir         = /var/lib/mysql   # 数据文件存放的目录
bind-address    = 0.0.0.0  # 允许远程连接，默认 127.0.0.1 只允许本地连接
port            = 3306  # 显式指定端口（默认3306可不写）

# 客户端字符集同步（避免乱码）
init_connect    = 'SET NAMES utf8mb4'  # 连接初始化时设置字符集

[client]
default-character-set = utf8mb4  # 客户端默认字符集

[mysql]
default-character-set = utf8mb4  # MySQL 命令行工具字符集
