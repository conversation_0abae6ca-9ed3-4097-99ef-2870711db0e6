# AI Hub 令牌 API 文档

## 接口概述
获取 AI Hub 服务的会话令牌接口

## 请求信息

**接口地址：** `POST /api/v1/ticket/create`  
**完整URL：** `https://aihub.staging.hntsz.com/api/v1/ticket/create`  
**请求方法：** POST  
**Content-Type：** application/json

## 请求认证

| 头部字段 | 类型 | 必填 | 说明 |
|----------|------|------|------|
| Authorization | string | 是 | 服务密钥，用于身份验证 |

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| UserKey | string | 是 | 用户标识符 |
| Application | string | 否 | 应用标识符（为空将使用默认Application）|

## 请求示例

```http
POST https://aihub.staging.hntsz.com/api/v1/ticket/create
Authorization: TENANT_KEY
Content-Type: application/json

{
    "UserKey": "UserKey"
    "Application": "ApplicationKey"
}
```

## 响应信息

**成功响应（HTTP 200）：**

```json
{
    "data": {
        "ticketCode": "ah-sk-Wb6XNjmZsr2zHpvDv1yjUcPG3yj49TCjIZA3SBQyyY9NMyLO14vJrmhmWqij1ep5",
        "expiredAt": "2025-08-08T08:47:33.2984319Z"
    },
    "success": true,
    "message": null
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | object | 票据信息对象 |
| data.ticketCode | string | 会话令牌，以 "ah-sk-" 开头 |
| data.expiredAt | string | 令牌过期时间（ISO 8601 格式） |
| success | boolean | 请求是否成功 |
| message | string/null | 错误信息，成功时为 null |


## 使用说明

1. 该接口用于获取 AI Hub 服务的会话令牌
2. 返回的 `ticketCode` 可用于后续 API 调用
3. 令牌格式为 "ah-sk-" 前缀加随机字符串
4. 令牌具有明确的过期时间，过期后需重新获取
5. 令牌有效期约为 30 分钟

## 注意事项

- 确保 Authorization 头部中密钥的安全性，不要在客户端代码中硬编码
- UserKey 用于标识不同用户，应确保唯一性
- 当前为测试环境 (staging)，生产环境URL可能不同
- 妥善保存返回的 ticketCode，避免泄露