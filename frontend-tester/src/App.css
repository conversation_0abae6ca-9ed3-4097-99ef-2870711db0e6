@keyframes loading-dot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Markdown table styles */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #d9d9d9;
  padding: 4px 4px;
  text-align: left;
}

.markdown-content table th {
  background-color: #fafafa;
  font-weight: 600;
}

.markdown-content table tr:nth-child(even) {
  background-color: #fafafa;
}

/* Fix avatar compression */
.ant-avatar {
  flex-shrink: 0 !important;
  min-width: 32px !important;
  min-height: 32px !important;
}

/* Make card body scrollable */
.ant-card-body {
  flex: 1 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  padding: 8px !important;
}
