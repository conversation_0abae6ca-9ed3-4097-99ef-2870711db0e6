import { useState, useEffect, useRef } from 'react'
import { Layout, Input, Button, Card, List, Typography, Space, Avatar, message } from 'antd'
import { SendOutlined, RobotOutlined, UserOutlined, PlusOutlined } from '@ant-design/icons'
import { HubConnectionBuilder, HubConnection } from '@microsoft/signalr'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import './App.css'

const { Header, Content, Footer } = Layout
const { TextArea } = Input
const { Title } = Typography

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date,
  isFinished: boolean
  toolCalls?: ToolCall[]
}

interface StreamingMessage {
  messageId: string
  content: string
}

interface MessageDto {
  id: string
  content: string
  category: 'User' | 'Assistant'
  timestamp: string
  toolCalls?: ToolCall[]
}

interface BeginStreamingMessage {
  messageId: string
}

interface ToolCall {
  id: string
  toolName: string
  toolDescription: string
  needConfirm: boolean
  isConfirm: boolean
}

interface EndStreamingMessage {
  messageId: string
  content: string
  toolCalls: ToolCall[]
}

interface CreateNewSessionSuccess {
  sessionId: string
}

interface BlockMessage {
  messageId: string
  content: string
  toolCalls: ToolCall[]
}

function App() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [connection, setConnection] = useState<HubConnection | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Generate or retrieve client identity ID
    let clientIdentityId = localStorage.getItem('clientIdentityId')
    if (!clientIdentityId) {
      clientIdentityId = crypto.randomUUID()
      localStorage.setItem('clientIdentityId', clientIdentityId)
    }

    const newConnection = new HubConnectionBuilder()
      .withUrl(`http://localhost:5000/chathub?ticket=ah-sk-123`)
      .withAutomaticReconnect()
      .build()

    setConnection(newConnection)

    return () => {
      newConnection.stop()
    }
  }, [])

  const handleBeginStreamingMessage = (beginStreamingData: string) => {

    const beginStreamingMessage: BeginStreamingMessage = JSON.parse(beginStreamingData)
    setMessages(value => {
      return [...value, {
        id: beginStreamingMessage.messageId,
        content: "",
        isUser: false,
        timestamp: new Date(),
        isFinished: false
      }];
    });
    
  }

  const handleStreamingMessage = (streamingData: string) => {
    const streamingMessage: StreamingMessage = JSON.parse(streamingData)

    setMessages(value => {
      const messageIndex = value.findIndex(msg => msg.id === streamingMessage.messageId)
      if(messageIndex !== -1){
        console.log("handleStreamingMessage", streamingMessage.content)
        const updatedMessages = [...value]
        updatedMessages[messageIndex] = {
          ...updatedMessages[messageIndex],
          content: updatedMessages[messageIndex].content + streamingMessage.content
        }
        return updatedMessages;
      }
      return value;
    });

  }

  const handleEndStreamingMessage = (endStreamingData: string) => {
    const endStreamingMessage: EndStreamingMessage = JSON.parse(endStreamingData)
    
    setMessages(value => {
      const messageExists = value.find(msg => msg.id === endStreamingMessage.messageId)
      
      if(messageExists){
        // Create a new array with the updated message
        return value.map(msg => 
          msg.id === endStreamingMessage.messageId 
            ? {
                ...msg,
                content: endStreamingMessage.content,
                isFinished: true,
                toolCalls: endStreamingMessage.toolCalls
              }
            : msg
        );
      }
      return value;
    })
  }

  const handleHistoryMessages = (historyData: string) => {
    const historyMessages: MessageDto[] = JSON.parse(historyData)
    
    const convertedMessages: Message[] = historyMessages
      //.filter(msg => msg.content && msg.content.trim() !== '')
      .map(msg => ({
        id: msg.id,
        content: msg.content,
        isUser: msg.category === 'User',
        timestamp: new Date(msg.timestamp),
        isFinished: true,
        toolCalls: msg.toolCalls
      }))
      
    setMessages(convertedMessages)
  }

  const handleCreateNewSessionSuccess = (createNewSessionSuccessData: string) => {
    const createNewSessionSuccess: CreateNewSessionSuccess = JSON.parse(createNewSessionSuccessData)
    message.success('New session created')
  }

  const handleBlockMessage = (blockMessageData: string) => {
    const blockMessage: BlockMessage = JSON.parse(blockMessageData)
    setMessages(value => {
      return [...value, {
        id: blockMessage.messageId,
        content: blockMessage.content,
        isUser: false,
        timestamp: new Date(),
        isFinished: true,
        toolCalls: blockMessage.toolCalls
      }];
    });
  }

  const handleBusy = () => {
    setLoading(true)
  }

  const handleIdle = () => {
    setLoading(false)
  }

  useEffect(() => {
    if (connection) {
      connection.start()
        .then(() => {
          message.success('Connected to Aihub AI')
          
          connection.on('CreateNewSessionSuccess', handleCreateNewSessionSuccess)
          connection.on('BeginStreamingMessage', handleBeginStreamingMessage)
          connection.on('StreamingMessage', handleStreamingMessage)
          connection.on('EndStreamingMessage', handleEndStreamingMessage)
          connection.on('BlockMessage', handleBlockMessage)
          connection.on('HistoryMessages', handleHistoryMessages)
          connection.on('Busy', handleBusy)
          connection.on('Idle', handleIdle)
        })
        .catch(error => {
          message.error('Failed to connect to Aihub AI')
          console.error('Connection failed: ', error)
        })
    }

    return () => {
      if (connection) {
        connection.off('CreateNewSessionSuccess', handleCreateNewSessionSuccess)
        connection.off('BeginStreamingMessage', handleBeginStreamingMessage)
        connection.off('StreamingMessage', handleStreamingMessage)
        connection.off('EndStreamingMessage', handleEndStreamingMessage)
        connection.off('BlockMessage', handleBlockMessage)
        connection.off('HistoryMessages', handleHistoryMessages)
        connection.off('Busy', handleBusy)
        connection.off('Idle', handleIdle)
      }
    }
  }, [connection])

  const sendMessage = async () => {
    if (!inputValue.trim() || !connection) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date(),
      isFinished: true
    }

    setMessages(prev => [...prev, userMessage])
    setLoading(true)
    
    try {
      await connection.invoke('SendMessage', inputValue)
      setInputValue('')
    } catch (error) {
      message.error('Failed to send message')
      console.error('Send failed: ', error)
      setLoading(false)
    }
  }

  const createNewSession = async () => {
    if (!connection) return
    
    try {
      await connection.invoke('CreateNewSession')
      setMessages([])
      message.success('New session created')
    } catch (error) {
      message.error('Failed to create new session')
      console.error('Create new session failed: ', error)
    }
  }

  const handleToolCallConfirm = async (messageId: string, toolCallId: string, isConfirm: boolean) => {
    if (!connection) return
    
    try {
      await connection.invoke('ConfirmToolCall', messageId, toolCallId, isConfirm)
      
      // Update local state to reflect the confirmation
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId && msg.toolCalls) {
          return {
            ...msg,
            toolCalls: msg.toolCalls.map(tool => 
              tool.id === toolCallId 
                ? { ...tool, isConfirm: isConfirm }
                : tool
            )
          }
        }
        return msg
      }))
      
      message.success(isConfirm ? 'Tool call confirmed' : 'Tool call rejected')
    } catch (error) {
      message.error('Failed to confirm tool call')
      console.error('Confirm tool call failed: ', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const loadingComponent = () => {
    return <div style={{ 
      display: 'flex', 
      justifyContent: 'flex-start',
      padding: '8px 0'
    }}>
      <div style={{ 
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
      }}>
        <div style={{
          width: '6px',
          height: '6px',
          backgroundColor: '#52c41a',
          borderRadius: '50%',
          animation: 'loading-dot 1.4s infinite ease-in-out both',
          animationDelay: '0s'
        }}></div>
        <div style={{
          width: '6px',
          height: '6px',
          backgroundColor: '#52c41a',
          borderRadius: '50%',
          animation: 'loading-dot 1.4s infinite ease-in-out both',
          animationDelay: '0.16s'
        }}></div>
        <div style={{
          width: '6px',
          height: '6px',
          backgroundColor: '#52c41a',
          borderRadius: '50%',
          animation: 'loading-dot 1.4s infinite ease-in-out both',
          animationDelay: '0.32s'
        }}></div>
      </div>
    </div>
  }

  return (
    <Layout style={{ minHeight: '100vh', width: '100vw' }}>
      <Header style={{ background: '#1890ff', padding: '0 24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={3} style={{ color: 'white', margin: 0, lineHeight: '64px' }}>
          Aihub AI Assistant
        </Title>
        <Button 
          type="primary" 
          ghost 
          icon={<PlusOutlined />}
          onClick={createNewSession}
          style={{ color: 'white', borderColor: 'white' }}
        >
          New Session
        </Button>
      </Header>
      
      <Content style={{ padding: '8px', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px - 50px)' }}>
        <Card 
          style={{ 
            flex: 1, 
            marginBottom: '16px',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          <div style={{ 
            flex: 1, 
            overflowY: 'auto', 
            maxHeight: '100%',
            padding: '8px'
          }}>
            {messages.length === 0 && (
              <div style={{ 
                textAlign: 'center', 
                color: '#999', 
                padding: '40px 0' 
              }}>
                <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <p>Welcome to Aihub AI! Ask me anything.</p>
              </div>
            )}
            {messages.length > 0 && (
              <>
                <List
                  dataSource={messages}
                  renderItem={(item) => (
                    <List.Item style={{ border: 'none', padding: '8px 0' }}>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: item.isUser ? 'flex-end' : 'flex-start',
                        width: '100%'
                      }}>
                        {(item.isUser || !item.isFinished || item.content.trim().length > 0 || (item.toolCalls && item.toolCalls.length > 0)) && (<div style={{ 
                          maxWidth: '85%',
                          display: 'flex',
                          alignItems: 'flex-start',
                          flexDirection: item.isUser ? 'row-reverse' : 'row'
                        }}>
                          <Avatar 
                            icon={item.isUser ? <UserOutlined /> : <RobotOutlined />}
                            style={{ 
                              backgroundColor: item.isUser ? '#1890ff' : '#52c41a',
                              margin: item.isUser ? '0 0 0 8px' : '0 8px 0 0'
                            }}
                          />
                          <div style={{ 
                            backgroundColor: item.isUser ? '#e6f4ff' : '#f6ffed',
                            padding: '12px 16px',
                            borderRadius: '8px',
                            border: item.isUser ? '1px solid #91caff' : '1px solid #b7eb8f'
                          }}>
                            <div style={{ wordBreak: 'break-word' }}>
                              {item.isUser ? (
                                item.content
                              ) : (
                                <div className="markdown-content">
                                  <ReactMarkdown remarkPlugins={[remarkGfm]}>{item.content}</ReactMarkdown>
                                </div>
                              )}
                              {(!item.isFinished && item.content.trim() === '') && loadingComponent()}
                              {item.toolCalls && item.toolCalls.length > 0 && (
                                <div style={{ 
                                  marginTop: '12px',
                                  padding: '8px 12px',
                                  backgroundColor: '#f0f2f5',
                                  borderRadius: '4px',
                                  fontSize: '12px'
                                }}>
                                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Tools Used:</div>
                                  {item.toolCalls.map((tool, index) => (
                                    <div key={index} style={{ marginBottom: '8px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                      <div>
                                        <span style={{ fontWeight: 'bold' }}>{tool.toolName}:</span> {tool.toolDescription}
                                        {tool.needConfirm && tool.isConfirm && (
                                          <span style={{ 
                                            marginLeft: '8px',
                                            color: '#52c41a'
                                          }}>
                                            ✓ Confirmed
                                          </span>
                                        )}
                                      </div>
                                      {tool.needConfirm && !tool.isConfirm && (
                                        <div style={{ display: 'flex', gap: '4px' }}>
                                          <Button 
                                            size="small" 
                                            type="primary"
                                            onClick={() => handleToolCallConfirm(item.id, tool.id, true)}
                                          >
                                            Confirm
                                          </Button>
                                          <Button 
                                            size="small" 
                                            danger
                                            onClick={() => handleToolCallConfirm(item.id, tool.id, false)}
                                          >
                                            Reject
                                          </Button>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                            <div style={{ 
                              fontSize: '12px', 
                              color: '#999', 
                              marginTop: '4px' 
                            }}>
                              {item.isFinished ? item.timestamp.toLocaleTimeString() : ''}
                            </div>
                          </div>
                        </div>)}
                      </div>
                    </List.Item>
                  )}
                />
              </>
            )}
            { loading && (<div style={{ 
              display: 'flex', 
              justifyContent: 'flex-start', 
              padding: '8px 0', 
              marginLeft: '40px'
            }}>
              {loadingComponent()}
            </div>)}
            <div ref={messagesEndRef} />
          </div>
        </Card>
        
        <Card>
          <Space.Compact style={{ display: 'flex', width: '100%' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me ..."
              autoSize={{ minRows: 1, maxRows: 3 }}
              style={{ flex: 1 }}
              disabled={loading}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={sendMessage}
              loading={loading}
              disabled={!inputValue.trim()}
            >
              Send
            </Button>
          </Space.Compact>
        </Card>
      </Content>
      
      <Footer style={{ textAlign: 'center', padding: '8px' }}>
        Aihub AI Assistant ©2024 Created with React + Ant Design
      </Footer>
    </Layout>
  )
}

export default App
