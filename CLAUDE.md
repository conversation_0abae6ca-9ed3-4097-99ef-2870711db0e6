# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack AI chat hub application with three main components:
- **backend-hubv2**: ASP.NET Core 9.0 API server with SignalR real-time communication
- **frontend-chat**: React/TypeScript chat interface using Vite and shadcn/ui
- **frontend-tester**: Basic React testing environment

## Common Development Commands

### Backend (.NET)
```bash
# Navigate to backend directory
cd backend-hubv2

# Build the solution
dotnet build

# Run the development server
dotnet run --project src/Aihub.Server

# Apply database migrations (if needed)
dotnet ef database update --project src/Aihub.Server
```

### Frontend Chat Application
```bash
# Navigate to frontend directory
cd frontend-chat

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Build for development
npm run build:dev

# Run linting
npm run lint

# Preview production build
npm run preview
```

## Architecture Overview

### Backend Architecture (ASP.NET Core)

The backend uses a **multi-agent architecture** with the following key components:

- **Agent System**: Located in `Agent/` directory
  - `AgentRunner.cs`: Main orchestrator for running agents
  - `ChatAgents/PrimaryAgent.cs`: Primary chat agent handling user interactions
  - `SubAgents/`: Specialized sub-agents (DatabaseSubAgent, ManualGuideSubAgent)
  - **Sub-Agent Pattern**: Each sub-agent inherits from `SubAgent.cs` and implements specific functionality with tool support

- **SignalR Hub**: `Hubs/ChatHub.cs` handles real-time WebSocket communication
- **Context Management**: `Contexts/` directory manages session, tenant, and application contexts
- **Entity Framework**: `EfCore/AihubDbContext.cs` with PostgreSQL support
- **LLM Integration**: `Llm/LlmClient.cs` interfaces with OpenRouter API for AI model access

### Frontend Architecture (React/TypeScript)

- **Component Structure**: 
  - `components/chat/`: Chat-specific components (ChatContainer, ChatMessage, ChatInput)
  - `components/ui/`: Reusable UI components using shadcn/ui
- **Hooks**: Custom React hooks in `hooks/`
  - `use-signalr-connection.ts`: SignalR connection management
  - `use-chat-messages.ts`: Chat message state management
- **State Management**: Uses React Query (@tanstack/react-query) for server state
- **Routing**: React Router with lazy-loaded components

### Communication Flow

1. Frontend connects to backend via SignalR (`/chathub` endpoint)
2. User messages trigger `AgentRunner` which orchestrates agent execution
3. Agents can invoke sub-agents with specialized tools (e.g., database queries)
4. Real-time updates sent back to frontend via SignalR
5. Context is maintained across multi-turn conversations

## Configuration

### Backend Configuration
- Main configuration in `appsettings.json` and `appsettings.Development.json`
- `AihubOption` class in `Options/` contains application-specific settings
- Database connection string configurable via configuration
- OpenRouter API integration for LLM access

### Frontend Configuration
- Vite configuration with manual chunk splitting for optimization
- Tailwind CSS for styling
- TypeScript with strict type checking
- ESLint for code quality

## Key Development Patterns

### Sub-Agent Development
When creating new sub-agents:
1. Inherit from `SubAgent` base class
2. Implement `GetToolTypes()` to return available tools
3. Tools should implement `ITool` interface with `ToolAttribute`
4. Use dependency injection for services

### Frontend Component Development  
- Follow existing shadcn/ui patterns
- Use TypeScript interfaces from `types/chat.ts`
- Implement proper error handling using error boundary patterns
- Follow the established hook patterns for state management

## Development Environment
- Backend: .NET 9.0, PostgreSQL database
- Frontend: Node.js, npm, Vite development server
- Real-time communication via SignalR WebSockets
- Development servers run on different ports (backend varies, frontend on 8080)